mode_train_files = {'mode1_train.mat', 'mode2_train.mat', 'mode3_train.mat', 'mode4_train.mat', 'mode5_train.mat'};


rng(42); % 固定随机种子便于复现
lambda_list = [1e-10,1e-9,1e-8,1e-7,1e-6,1e-5,1e-4,0.001,0.01,0.1, 1, 10, 100, 1000,1e4,1e5,1e6,1e7,1e8,1e9,1e10];
n_lambda = numel(lambda_list);
nmsc_arr = zeros(n_lambda, 1);
subspace_dist_arr = zeros(n_lambda, 1);
deltaD_cell = cell(n_lambda,1);

n_iter_init=30;
sparsity=2;
n_iter_new=n_iter_init;
n_atoms=30;

for idx_lambda = 1:n_lambda
    lambda = lambda_list(idx_lambda);
    [Dictionary_history, imp_idx_history, U_locked_history, nmsc,subspace_dist] = ...
        continual_dictionary_learning(mode_train_files, n_atoms, sparsity, n_iter_init, n_iter_new, lambda);
    nmsc_arr(idx_lambda) = nmsc;
    subspace_dist_arr(idx_lambda) = subspace_dist;

    % 保存差分热图
    D1 = Dictionary_history{1};
    Dlast = Dictionary_history{end};
    deltaD_cell{idx_lambda} = Dlast - D1;

    fprintf('lambda=%.1f: NMSC=%.4f, SubspaceDist=%.4f\n', ...
        lambda, nmsc, subspace_dist);
end

figure;
subplot(1,2,1);
semilogx(lambda_list, nmsc_arr, '-o','LineWidth',2);
xlabel('\lambda'); ylabel('归一化均方变化度NMSC');
title('字典整体变化度');
grid on;
subplot(1,2,2);
semilogx(lambda_list, subspace_dist_arr, '-o','LineWidth',2);
xlabel('\lambda'); ylabel('主空间夹角距离 (rad)');
title('主空间变化度');
grid on;

% =================== 可视化所有lambda下的字典差分热图 ===================
figure;
n_col = 5;   % 每行5个
n_row = ceil(n_lambda / n_col);
for idx_lambda = 1:n_lambda
    subplot(n_row, n_col, idx_lambda);
    imagesc(deltaD_cell{idx_lambda});
    colorbar;
    title(['\lambda=' num2str(lambda_list(idx_lambda),'%.0e')]);
    xlabel('原子编号'); ylabel('观测量编号');
    set(gca,'FontSize',8);
end
sgtitle('不同\lambda下最终字典与mode1字典差分热图');


figure;
n_col = 5;   % 每行5个
n_row = ceil(n_lambda / n_col);
for idx_lambda = 1:n_lambda
    subplot(n_row, n_col, idx_lambda);
    imagesc(abs(deltaD_cell{idx_lambda}));
    colorbar;
    title(['\lambda=' num2str(lambda_list(idx_lambda),'%.0e')]);
    xlabel('原子编号'); ylabel('观测量编号');
    set(gca,'FontSize',8);
end
sgtitle('不同\lambda下最终字典与mode1字典差分热图');

