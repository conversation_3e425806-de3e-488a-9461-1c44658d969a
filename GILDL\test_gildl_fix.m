%% 测试GILDL修复是否有效

fprintf('========== 测试GILDL修复 ==========\n');

%% 测试1: learn_D_GILDL函数基本调用
fprintf('1. 测试learn_D_GILDL函数基本调用...\n');
try
    % 创建测试数据
    Y_test = randn(8, 100);  % 8维特征，100个样本
    
    % 调用函数
    [Dict_history, NMSC, max_angle_rad, final_dict] = learn_D_GILDL(Y_test);
    
    fprintf('   ✅ learn_D_GILDL调用成功\n');
    fprintf('   字典大小: %dx%d\n', size(final_dict,1), size(final_dict,2));
    fprintf('   NMSC: %.4f\n', NMSC);
    fprintf('   最大夹角: %.4f弧度 (%.2f°)\n', max_angle_rad, max_angle_rad*180/pi);
    
catch ME
    fprintf('   ❌ learn_D_GILDL调用失败: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('   错误位置: 第%d行\n', ME.stack(1).line);
    end
end

%% 测试2: 默认参数调用
fprintf('\n2. 测试默认参数调用...\n');
try
    [Dict_history, NMSC, max_angle_rad, final_dict] = learn_D_GILDL();
    
    fprintf('   ✅ 默认参数调用成功\n');
    fprintf('   训练模式数: %d\n', length(Dict_history));
    fprintf('   最终字典大小: %dx%d\n', size(final_dict,1), size(final_dict,2));
    
catch ME
    fprintf('   ❌ 默认参数调用失败: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('   错误位置: %s 第%d行\n', ME.stack(1).name, ME.stack(1).line);
    end
end

%% 测试3: analyze_gildl_simple脚本
fprintf('\n3. 测试analyze_gildl_simple脚本...\n');
try
    % 运行简化分析脚本
    analyze_gildl_simple;
    
    fprintf('   ✅ analyze_gildl_simple运行成功\n');
    
    % 检查结果文件
    if exist('gildl_simple_analysis.mat', 'file')
        load('gildl_simple_analysis.mat', 'results_gildl');
        fprintf('   结果文件已生成\n');
        fprintf('   包含字段: %s\n', strjoin(fieldnames(results_gildl), ', '));
    end
    
catch ME
    fprintf('   ❌ analyze_gildl_simple运行失败: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('   错误位置: %s 第%d行\n', ME.stack(1).name, ME.stack(1).line);
    end
end

%% 测试4: run_gildl_analysis脚本
fprintf('\n4. 测试run_gildl_analysis脚本...\n');
try
    % 检查必要文件
    required_files = {'learn_D_GILDL.m', 'analyze_gildl_simple.m', 'compute_far_fdr_gildl.m'};
    all_exist = true;
    
    for i = 1:length(required_files)
        if ~exist(required_files{i}, 'file')
            fprintf('   ❌ 缺少文件: %s\n', required_files{i});
            all_exist = false;
        end
    end
    
    if all_exist
        fprintf('   ✅ 所有必要文件都存在\n');
        fprintf('   可以运行: run(''run_gildl_analysis.m'')\n');
    else
        fprintf('   ❌ 缺少必要文件，无法完整运行\n');
    end
    
catch ME
    fprintf('   ❌ 文件检查失败: %s\n', ME.message);
end

fprintf('\n========== 测试完成 ==========\n');
fprintf('如果所有测试都通过，可以运行: run(''run_gildl_analysis.m'')\n');
