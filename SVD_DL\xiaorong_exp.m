% 获取四组实验结果
[~, nmsc1, subspace_dist1] = run_continual_dictionary_learning(false, false);
[~, nmsc2, subspace_dist2] = run_continual_dictionary_learning(true, false);
[~, nmsc3, subspace_dist3] = run_continual_dictionary_learning(false, true);
[~, nmsc4, subspace_dist4] = run_continual_dictionary_learning(true, true);

% 组合数据
nmsc_all = [nmsc1, nmsc2, nmsc3, nmsc4];
subspace_all = [subspace_dist1, subspace_dist2, subspace_dist3, subspace_dist4];

labels = {'完整模型', '无主原子替换', '无主空间保护', '双重消融'};

% 绘制NMSC变化度对比
figure;
bar(nmsc_all);
set(gca, 'XTickLabel', labels, 'XTickLabelRotation', 20, 'FontSize', 12);
ylabel('NMSC（字典变化度）');
title('不同配置下的字典变化度');
grid on;

% 绘制主空间变化度对比
figure;
bar(subspace_all);
set(gca, 'XTickLabel', labels, 'XTickLabelRotation', 20, 'FontSize', 12);
ylabel('主空间距离（rad）');
title('不同配置下的主空间变化度');
grid on;
