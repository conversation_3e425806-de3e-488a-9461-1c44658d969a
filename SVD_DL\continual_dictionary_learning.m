function [Dictionary_history, imp_idx_history, U_locked_history, nmsc,subspace_dist] = continual_dictionary_learning(...
    mode_train_files, n_atoms, sparsity, n_iter_init, n_iter_new, lambda)
% 持续字典学习（双重保护：主原子锁定+主空间保护）
% 
% 输入:
%   mode_train_files: cell数组，每个元素是训练数据文件名
%   n_atoms: 字典原子数
%   sparsity: 稀疏度
%   n_iter_init: mode1字典初始化迭代次数
%   n_iter_new: 后续mode新字典迭代次数
%   lambda: 主空间正则系数
%
% 输出:
%   Dictionary_history: 每个mode的字典
%   imp_idx_history: 每个mode主原子的下标
%   U_locked_history: 每个mode主空间
%   nmsc: 字典变化度

rng(42); % 固定随机种子便于复现
all_modes = numel(mode_train_files);

Dictionary_history = cell(all_modes, 1);
imp_idx_history = cell(all_modes, 1);
U_locked_history = cell(all_modes, 1);

%% ===== 1. 初始mode（mode1）KSVD训练字典 =====
load(mode_train_files{1}, 'train_data');
Y = train_data';  % [特征 x 样本]

% 字典初始化
D_init = randn(size(Y,1), n_atoms);
for k = 1:n_atoms
    D_init(:,k) = D_init(:,k) / norm(D_init(:,k));
end
[Dictionary, ~] = ksvd_simple(Y, D_init, sparsity, n_iter_init);

% SVD分析，主方向锁定
[U, S, ~] = svd(Dictionary, 'econ');
singular_values = diag(S);
energy = cumsum(singular_values.^2) / sum(singular_values.^2);
k_locked = find(energy >= 0.9, 1, 'first');
U_locked = U(:, 1:k_locked);

Dictionary_history{1} = Dictionary;
U_locked_history{1} = U_locked;
D_prev = Dictionary;
U_locked_prev = U_locked;
k_locked_prev = k_locked;

%% ===== 2. 增量持续学习 =====
for mode = 2:all_modes
    fprintf('==== 进入持续学习：mode%d ====\n', mode);
    % 加载新工况数据
    load(mode_train_files{mode}, 'train_data');
    Y_new = train_data';

    % 新mode KSVD
    D_init = randn(size(Y_new,1), n_atoms);
    for k = 1:n_atoms
        D_init(:,k) = D_init(:,k) / norm(D_init(:,k));
    end
    [D_new, ~] = ksvd_simple(Y_new, D_init, sparsity, n_iter_new);

    % SVD分解旧/新字典，判定主/次原子
    [~, So, Vo] = svd(D_prev, 'econ');
    V_weight_old = abs(Vo) * diag(So);
    [~, idx_old] = sort(V_weight_old, 'descend');
    energy_cum_old = cumsum(V_weight_old(idx_old)) / sum(V_weight_old);
    k_important = find(energy_cum_old >= 0.9, 1, 'first');
    imp_idx_old = idx_old(1:k_important);
    unimp_idx_old = idx_old(k_important+1:end);

    [~, S_new, V_new] = svd(D_new, 'econ');
    V_weight_new = abs(V_new) * diag(S_new);
    [~, idx_new] = sort(V_weight_new, 'descend');
    energy_cum_new = cumsum(V_weight_new(idx_new)) / sum(V_weight_new);
    k_important_new = find(energy_cum_new >= 0.9, 1, 'first');
    imp_idx_new = idx_new(1:k_important_new);

    % 新主原子替换旧次原子
    D_fused = D_prev;
    unimp_idx_old = flip(unimp_idx_old); % 确保优先替换最不重要
    replace_cnt = min(numel(imp_idx_new), numel(unimp_idx_old));
    if replace_cnt > 0
        D_fused(:, unimp_idx_old(1:replace_cnt)) = D_new(:, imp_idx_new(1:replace_cnt));
    end

    % 主方向空间保护(闭式解)
    U_locked = D_prev(:, imp_idx_old); % 或 Uo(:, 1:k_locked_prev)
    [Uo, ~, ~] = svd(D_prev, 'econ');
    U_locked = Uo(:, 1:k_locked_prev);
    P_locked = U_locked * U_locked';

    X = zeros(size(D_fused,2), size(Y_new,2));
    for n = 1:size(Y_new,2)
        X(:,n) = omp(D_fused, Y_new(:,n), sparsity);
    end

    A = lambda * (P_locked') * P_locked;
    B = X * X';
    C = lambda * (P_locked') * P_locked * D_prev + Y_new * X';
    D_fused = sylvester(A,B,C);

    % 单位归一化
    for k = 1:size(D_fused,2)
        D_fused(:,k) = D_fused(:,k) / norm(D_fused(:,k));
    end

    % 新主空间
    [U_new, S_new, ~] = svd(D_fused, 'econ');
    singular_values_new = diag(S_new);
    energy_new = cumsum(singular_values_new.^2) / sum(singular_values_new.^2);
    k_locked_new = find(energy_new >= 0.9, 1, 'first');
    U_locked_new = U_new(:, 1:k_locked_new);

    Dictionary_history{mode} = D_fused;
    imp_idx_history{mode} = imp_idx_old;
    U_locked_history{mode} = U_locked_new;
    D_prev = D_fused;
    U_locked_prev = U_locked_new;
    k_locked_prev = k_locked_new;

    fprintf('mode%d主原子数=%d，主空间维度=%d\n', mode, k_important, k_locked_new);
end

% 字典整体变化度
D1 = Dictionary_history{1};
Dlast = Dictionary_history{end};
epsilon = 1e-8;
num = (Dlast - D1).^2;
den = D1.^2 + epsilon;
nmsc = mean(num(:) ./ den(:));
fprintf('归一化均方变化度（NMSC）: %.4f\n', nmsc);

% 主空间变化度（夹角距离）
U1 = U_locked_history{1};
Ulast = U_locked_history{end};
min_dim = min(size(U1,2), size(Ulast,2));
U1 = U1(:,1:min_dim); Ulast = Ulast(:,1:min_dim);
subspace_dist = subspace(U1, Ulast); % Matlab自带subspace函数



end
