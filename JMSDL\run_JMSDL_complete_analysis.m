%% JMSDL完整分析 - 基于最新compute_dictionary_JMSDL.m
% 一键运行JMSDL字典学习和监测性能分析

clc; clear; close all;
rng(42);

fprintf('========== JMSDL完整分析 ==========\n');
fprintf('基于最新的compute_dictionary_JMSDL.m算法\n');
fprintf('分析内容: 字典学习 + 监测性能 + 方法比较\n\n');

%% 1. 检查必要文件
fprintf('🔍 检查必要文件...\n');

required_files = {};
for mode = 1:5
    required_files{end+1} = sprintf('mode%d_train.mat', mode);
end

% 检查compute_dictionary_JMSDL函数
if exist('compute_dictionary_JMSDL.m', 'file')
    fprintf('   ✓ compute_dictionary_JMSDL.m\n');
else
    fprintf('   ❌ compute_dictionary_JMSDL.m (缺失)\n');
    fprintf('   请确保最新的compute_dictionary_JMSDL.m文件在JMSDL目录中。\n');
    return;
end

missing_files = {};
for i = 1:length(required_files)
    if exist(required_files{i}, 'file')
        fprintf('   ✓ %s\n', required_files{i});
    else
        fprintf('   ❌ %s (缺失)\n', required_files{i});
        missing_files{end+1} = required_files{i};
    end
end

if ~isempty(missing_files)
    fprintf('   请确保所有必要的数据文件都在JMSDL目录中。\n');
    return;
end

fprintf('✅ 文件检查完成\n\n');

%% 2. JMSDL字典学习
fprintf('📚 开始JMSDL字典学习...\n');

learning_time = 0;
try
    tic;
    JMSDL_updated_analysis;
    learning_time = toc;
    fprintf('   ✅ JMSDL字典学习完成，耗时: %.1f秒\n', learning_time);
catch ME
    fprintf('   ❌ JMSDL字典学习失败: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('   错误位置: %s 第%d行\n', ME.stack(1).name, ME.stack(1).line);
    end
    return;
end

%% 3. 监测性能分析
fprintf('\n🎯 开始监测性能分析...\n');

monitoring_time = 0;
try
    tic;
    JMSDL_monitoring_analysis;
    monitoring_time = toc;
    fprintf('   ✅ 监测性能分析完成，耗时: %.1f秒\n', monitoring_time);
catch ME
    fprintf('   ❌ 监测性能分析失败: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('   错误位置: %s 第%d行\n', ME.stack(1).name, ME.stack(1).line);
    end
    return;
end

%% 4. 加载并整合结果
fprintf('\n📋 整合分析结果...\n');

try
    % 加载字典学习结果
    load('JMSDL_updated_results.mat', 'JMSDL_updated_results');
    learning_results = JMSDL_updated_results;
    
    % 加载监测性能结果
    load('JMSDL_monitoring_results.mat', 'JMSDL_monitoring_results');
    monitoring_results = JMSDL_monitoring_results;
    
    fprintf('   ✓ 成功加载所有分析结果\n');
    
catch ME
    fprintf('   ❌ 结果加载失败: %s\n', ME.message);
    return;
end

%% 5. 生成综合报告
fprintf('\n📄 生成综合分析报告...\n');

fprintf('\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
fprintf('📊 JMSDL方法综合分析报告\n');
fprintf('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');

%% 5.1 基本信息
fprintf('\n🔧 基本配置:\n');
fprintf('   方法名称: %s\n', learning_results.method);
fprintf('   工况数量: %d\n', length(learning_results.Dictionary_history));
fprintf('   字典大小: %dx%d\n', size(learning_results.Dictionary_history{1}));
fprintf('   稀疏度: %d\n', monitoring_results.sparsity);

%% 5.2 字典演化性能
fprintf('\n📈 字典演化性能:\n');

NMSC_history = learning_results.NMSC_history;
fprintf('   NMSC (归一化均方变化度):\n');
for i = 1:length(NMSC_history)
    fprintf('     工况%d→%d: %.6f\n', i, i+1, NMSC_history(i));
end
fprintf('     平均值: %.6f\n', mean(NMSC_history));

% 子空间夹角分析
Subspace_angle_history = learning_results.Subspace_angle_history;
valid_angles = Subspace_angle_history(~isnan(Subspace_angle_history));
if ~isempty(valid_angles)
    fprintf('   主方向子空间夹角:\n');
    for i = 1:length(Subspace_angle_history)
        if ~isnan(Subspace_angle_history(i))
            fprintf('     工况%d→%d: %.4f弧度 (%.2f°)\n', i, i+1, ...
                    Subspace_angle_history(i), Subspace_angle_history(i)*180/pi);
        end
    end
    fprintf('     平均值: %.4f弧度 (%.2f°)\n', mean(valid_angles), mean(valid_angles)*180/pi);
end

%% 5.3 监测性能
fprintf('\n🎯 过程监测性能:\n');

FAR_all = monitoring_results.FAR_all;
FDR_all = monitoring_results.FDR_all;
FAR_overall = monitoring_results.FAR_overall;
FDR_overall = monitoring_results.FDR_overall;

fprintf('   各工况检测性能:\n');
fprintf('     工况    FAR      FDR\n');
fprintf('     ----   ------   ------\n');
for m = 1:length(FAR_all)
    fprintf('     %2d     %.4f   %.4f\n', m, FAR_all(m), FDR_all(m));
end

fprintf('   总体性能:\n');
fprintf('     平均FAR: %.4f (误报率，越小越好)\n', FAR_overall);
fprintf('     平均FDR: %.4f (检出率，越大越好)\n', FDR_overall);

%% 5.4 性能评估
fprintf('\n⭐ 性能评估:\n');

% 字典稳定性评估
avg_nmsc = mean(NMSC_history);
if avg_nmsc < 0.1
    fprintf('   字典稳定性: 优秀 (NMSC=%.6f)\n', avg_nmsc);
elseif avg_nmsc < 0.5
    fprintf('   字典稳定性: 良好 (NMSC=%.6f)\n', avg_nmsc);
else
    fprintf('   字典稳定性: 一般 (NMSC=%.6f)\n', avg_nmsc);
end

% 监测性能评估
if FAR_overall < 0.05 && FDR_overall > 0.8
    fprintf('   监测性能: 优秀 (FAR=%.4f, FDR=%.4f)\n', FAR_overall, FDR_overall);
elseif FAR_overall < 0.1 && FDR_overall > 0.7
    fprintf('   监测性能: 良好 (FAR=%.4f, FDR=%.4f)\n', FAR_overall, FDR_overall);
else
    fprintf('   监测性能: 需改进 (FAR=%.4f, FDR=%.4f)\n', FAR_overall, FDR_overall);
end

%% 6. 保存综合结果
fprintf('\n💾 保存综合分析结果...\n');

comprehensive_results = struct();
comprehensive_results.method = 'JMSDL_Complete';
comprehensive_results.learning_results = learning_results;
comprehensive_results.monitoring_results = monitoring_results;
comprehensive_results.analysis_time = struct('learning_time', learning_time, ...
                                            'monitoring_time', monitoring_time, ...
                                            'total_time', learning_time + monitoring_time);

save('JMSDL_comprehensive_results.mat', 'comprehensive_results');
fprintf('   综合结果已保存到: JMSDL_comprehensive_results.mat\n');

%% 7. 输出文件总结
fprintf('\n📁 生成的文件总结:\n');

output_files = {
    'D_final_JMSDL_updated.mat', '最终字典';
    'JMSDL_updated_results.mat', '字典学习结果';
    'JMSDL_updated_analysis.fig', '字典演化分析图';
    'JMSDL_monitoring_results.mat', '监测性能结果';
    'JMSDL_monitoring_performance.fig', '监测性能图';
    'JMSDL_comprehensive_results.mat', '综合分析结果'
};

fprintf('   生成的文件:\n');
for i = 1:size(output_files, 1)
    filename = output_files{i, 1};
    description = output_files{i, 2};
    
    if exist(filename, 'file')
        file_info = dir(filename);
        fprintf('     ✓ %s (%.1f KB) - %s\n', filename, file_info.bytes/1024, description);
    else
        fprintf('     ❌ %s - %s (未生成)\n', filename, description);
    end
end

%% 8. 使用建议
fprintf('\n💡 使用建议:\n');
fprintf('   1. 查看字典演化: open(''JMSDL_updated_analysis.fig'')\n');
fprintf('   2. 查看监测性能: open(''JMSDL_monitoring_performance.fig'')\n');
fprintf('   3. 加载详细数据: load(''JMSDL_comprehensive_results.mat'')\n');
fprintf('   4. 参数调优: 修改JMSDL_updated_analysis.m中的参数\n');
fprintf('   5. 算法改进: 基于compute_dictionary_JMSDL.m进行优化\n');

fprintf('\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
fprintf('🎉 JMSDL完整分析完成！\n');
fprintf('总耗时: %.1f分钟\n', (learning_time + monitoring_time)/60);
fprintf('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
