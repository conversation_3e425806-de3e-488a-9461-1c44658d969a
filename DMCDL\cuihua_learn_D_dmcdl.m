%% DMCDL方法应用于催化数据集
% 模仿GILDL\cuihua_learn_D.m将data_selected_F1.mat到data_selected_F3.mat应用在DMCDL方法上

clc; clear; close all;
rng(42);

fprintf('========== DMCDL方法应用于催化数据集 ==========\n');
fprintf('数据集: 催化数据 (data_selected_F1~F3)\n');
fprintf('方法: DMCDL (双重记忆持续字典学习)\n\n');

%% 参数设置
n_atoms = 20;           % 字典原子数
sparsity = 3;           % 稀疏度
n_iter = 50;            % 迭代次数
lambda_x = 1e-3;        % 稀疏惩罚参数
lambda_1 = 1e-6;        % 初始正则化参数
zeta = 1e-12;           % 数值稳定性参数
M = 10;                 % 记忆池容量
all_modes = 3;          % 催化数据集有3个模式

fprintf('参数设置:\n');
fprintf('  字典原子数: %d\n', n_atoms);
fprintf('  稀疏度: %d\n', sparsity);
fprintf('  迭代次数: %d\n', n_iter);
fprintf('  记忆池容量: %d\n', M);

%% 初始化存储
Dictionary_history = cell(all_modes, 1);
Weight_history = cell(all_modes, 1);
Memory_history = cell(all_modes, 1);
Threshold_history = zeros(all_modes, 1);

%% Mode 1: 初始建模 (F1数据)
fprintf('\n=== Mode 1: 初始建模 (F1数据) ===\n');

% 加载F1数据
load('data_selected_F1.mat');
Y1 = cell2mat(data_selected)';  % [特征维度 x 样本数]
[m, n1] = size(Y1);

fprintf('  F1数据大小: %dx%d\n', m, n1);

% DMCDL初始建模
[D, X, w, W] = dmcdl_initial(Y1, n_atoms, sparsity, n_iter, lambda_1, zeta);

% 建立阈值和记忆池
[Rtr, Yh] = build_threshold_and_memory(D, X, Y1, M);

% 保存Mode 1结果
Dictionary_history{1} = D;
Weight_history{1} = W;
Memory_history{1} = Yh;
Threshold_history(1) = Rtr;

fprintf('  Mode 1完成: 字典大小=%dx%d, 阈值=%.6f, 记忆池大小=%d\n', ...
        size(D,1), size(D,2), Rtr, size(Yh,2));

%% Mode 2: 增量学习 (F2数据)
fprintf('\n=== Mode 2: 增量学习 (F2数据) ===\n');

% 加载F2数据
load('data_selected_F2.mat');
Y2 = cell2mat(data_selected)';  % [特征维度 x 样本数]
[~, n2] = size(Y2);

fprintf('  F2数据大小: %dx%d\n', size(Y2,1), n2);

% 双重记忆拼接
Y_concat = [Y2, Yh];
fprintf('  拼接后数据大小: %dx%d (新数据%d + 记忆%d)\n', ...
        size(Y_concat,1), size(Y_concat,2), n2, size(Yh,2));

% DMCDL增量学习
[D, X, W, w] = dmcdl_incremental(D, W, Y_concat, n_atoms, sparsity, n_iter, lambda_x, zeta, w);

% 更新阈值和记忆池
[Rtr, Yh] = build_threshold_and_memory(D, X, Y_concat, M);

% 保存Mode 2结果
Dictionary_history{2} = D;
Weight_history{2} = W;
Memory_history{2} = Yh;
Threshold_history(2) = Rtr;

fprintf('  Mode 2完成: 字典大小=%dx%d, 阈值=%.6f, 记忆池大小=%d\n', ...
        size(D,1), size(D,2), Rtr, size(Yh,2));

%% Mode 3: 增量学习 (F3数据)
fprintf('\n=== Mode 3: 增量学习 (F3数据) ===\n');

% 加载F3数据
load('data_selected_F3.mat');
Y3 = cell2mat(data_selected)';  % [特征维度 x 样本数]
[~, n3] = size(Y3);

fprintf('  F3数据大小: %dx%d\n', size(Y3,1), n3);

% 双重记忆拼接
Y_concat = [Y3, Yh];
fprintf('  拼接后数据大小: %dx%d (新数据%d + 记忆%d)\n', ...
        size(Y_concat,1), size(Y_concat,2), n3, size(Yh,2));

% DMCDL增量学习
[D, X, W, w] = dmcdl_incremental(D, W, Y_concat, n_atoms, sparsity, n_iter, lambda_x, zeta, w);

% 更新阈值和记忆池
[Rtr, Yh] = build_threshold_and_memory(D, X, Y_concat, M);

% 保存Mode 3结果
Dictionary_history{3} = D;
Weight_history{3} = W;
Memory_history{3} = Yh;
Threshold_history(3) = Rtr;

fprintf('  Mode 3完成: 字典大小=%dx%d, 阈值=%.6f, 记忆池大小=%d\n', ...
        size(D,1), size(D,2), Rtr, size(Yh,2));

%% 保存最终结果
fprintf('\n保存结果...\n');

% 保存最终字典
D_final_cuihua = D;
save('D_final_cuihua_dmcdl.mat', 'D_final_cuihua');

% 保存完整的演化历史
cuihua_dmcdl_results = struct();
cuihua_dmcdl_results.method = 'DMCDL_Cuihua';
cuihua_dmcdl_results.Dictionary_history = Dictionary_history;
cuihua_dmcdl_results.Weight_history = Weight_history;
cuihua_dmcdl_results.Memory_history = Memory_history;
cuihua_dmcdl_results.Threshold_history = Threshold_history;
cuihua_dmcdl_results.parameters = struct('n_atoms', n_atoms, 'sparsity', sparsity, ...
                                        'n_iter', n_iter, 'lambda_x', lambda_x, ...
                                        'lambda_1', lambda_1, 'M', M);

save('cuihua_dmcdl_results.mat', 'cuihua_dmcdl_results');

fprintf('  最终字典已保存到: D_final_cuihua_dmcdl.mat\n');
fprintf('  完整结果已保存到: cuihua_dmcdl_results.mat\n');

%% 结果分析
fprintf('\n========== 结果分析 ==========\n');

% 计算字典演化指标
NMSC_history = zeros(all_modes-1, 1);
epsilon = 1e-8;

for i = 2:all_modes
    D_prev = Dictionary_history{i-1};
    D_curr = Dictionary_history{i};
    deltaD = D_curr - D_prev;
    NMSC_history(i-1) = mean((deltaD(:).^2) ./ (D_prev(:).^2 + epsilon));
    
    fprintf('  NMSC (F%d→F%d): %.6f\n', i-1, i, NMSC_history(i-1));
end

% 权重矩阵分析
fprintf('\n权重矩阵演化:\n');
for i = 1:all_modes
    W = Weight_history{i};
    weight_norm = norm(W, 'fro');
    weight_trace = trace(W);
    fprintf('  Mode %d: 权重范数=%.4f, 迹=%.4f\n', i, weight_norm, weight_trace);
end

% 记忆池分析
fprintf('\n记忆池演化:\n');
for i = 1:all_modes
    Yh = Memory_history{i};
    memory_size = size(Yh, 2);
    fprintf('  Mode %d: 记忆池大小=%d, 阈值=%.6f\n', i, memory_size, Threshold_history(i));
end

%% 可视化
fprintf('\n生成可视化...\n');

figure('Position', [100, 100, 1200, 800]);

% 子图1: 字典演化
subplot(2,3,1);
imagesc(Dictionary_history{1});
colorbar;
title('Mode 1 字典 (F1)', 'FontSize', 12);
xlabel('原子索引');
ylabel('特征维度');

subplot(2,3,2);
imagesc(Dictionary_history{2});
colorbar;
title('Mode 2 字典 (F2)', 'FontSize', 12);
xlabel('原子索引');
ylabel('特征维度');

subplot(2,3,3);
imagesc(Dictionary_history{3});
colorbar;
title('Mode 3 字典 (F3)', 'FontSize', 12);
xlabel('原子索引');
ylabel('特征维度');

% 子图4: NMSC趋势
subplot(2,3,4);
plot(2:all_modes, NMSC_history, 'bo-', 'LineWidth', 2, 'MarkerSize', 8);
xlabel('目标模式');
ylabel('NMSC');
title('DMCDL-催化: NMSC变化', 'FontSize', 12);
grid on;
for i = 1:length(NMSC_history)
    text(i+1, NMSC_history(i), sprintf('%.4f', NMSC_history(i)), ...
         'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom');
end

% 子图5: 权重矩阵范数
subplot(2,3,5);
weight_norms = arrayfun(@(i) norm(Weight_history{i}, 'fro'), 1:all_modes);
plot(1:all_modes, weight_norms, 'ro-', 'LineWidth', 2, 'MarkerSize', 8);
xlabel('模式');
ylabel('权重矩阵范数');
title('权重矩阵演化', 'FontSize', 12);
grid on;

% 子图6: 阈值演化
subplot(2,3,6);
plot(1:all_modes, Threshold_history, 'go-', 'LineWidth', 2, 'MarkerSize', 8);
xlabel('模式');
ylabel('重构误差阈值');
title('阈值演化', 'FontSize', 12);
grid on;

sgtitle('DMCDL方法 - 催化数据集分析', 'FontSize', 16);

% 保存图像
savefig('dmcdl_cuihua_analysis.fig');
fprintf('  可视化图已保存到: dmcdl_cuihua_analysis.fig\n');

fprintf('\n🎉 DMCDL催化数据集学习完成！\n');
fprintf('平均NMSC: %.6f\n', mean(NMSC_history));
fprintf('最终权重范数: %.4f\n', weight_norms(end));
fprintf('最终记忆池大小: %d\n', size(Memory_history{end}, 2));
