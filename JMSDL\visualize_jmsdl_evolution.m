function visualize_jmsdl_evolution(results_jmsdl)
%% JMSDL方法字典演化可视化
% 仿照SVD_DL的可视化方法

if nargin < 1
    if exist('jmsdl_performance_analysis.mat', 'file')
        load('jmsdl_performance_analysis.mat', 'results_jmsdl');
    else
        error('请提供results_jmsdl结构体或确保jmsdl_performance_analysis.mat文件存在');
    end
end

%% 图1: JMSDL方法综合演化分析
figure('Position', [50, 50, 1400, 900]);

% 子图1: NMSC变化趋势
subplot(2,3,1);
if isfield(results_jmsdl, 'NMSC_history') && ~isempty(results_jmsdl.NMSC_history)
    NMSC_history = results_jmsdl.NMSC_history;
    plot(2:5, NMSC_history, 'bo-', 'LineWidth', 2, 'MarkerSize', 8);
    xlabel('目标模式', 'FontSize', 12);
    ylabel('NMSC', 'FontSize', 12);
    title('JMSDL: NMSC变化趋势', 'FontSize', 14);
    grid on;
    set(gca, 'FontSize', 12);
    
    % 添加数值标签
    for i = 1:length(NMSC_history)
        text(i+1, NMSC_history(i), sprintf('%.3f', NMSC_history(i)), ...
             'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', 'FontSize', 10);
    end
else
    text(0.5, 0.5, 'NMSC数据不可用', 'HorizontalAlignment', 'center', 'FontSize', 12);
    title('JMSDL: NMSC变化趋势', 'FontSize', 14);
end

% 子图2: 子空间夹角变化
subplot(2,3,2);
if isfield(results_jmsdl, 'Subspace_angle_history') && ~isempty(results_jmsdl.Subspace_angle_history)
    Subspace_angle_history = results_jmsdl.Subspace_angle_history;
    valid_angles = Subspace_angle_history(~isnan(Subspace_angle_history));
    valid_modes = find(~isnan(Subspace_angle_history)) + 1;
    
    if ~isempty(valid_angles)
        plot(valid_modes, valid_angles, 'rs-', 'LineWidth', 2, 'MarkerSize', 8);
        xlabel('目标模式', 'FontSize', 12);
        ylabel('子空间夹角 (弧度)', 'FontSize', 12);
        title('JMSDL: 子空间夹角变化', 'FontSize', 14);
        grid on;
        set(gca, 'FontSize', 12);
        
        % 添加数值标签
        for i = 1:length(valid_angles)
            text(valid_modes(i), valid_angles(i), sprintf('%.3f', valid_angles(i)), ...
                 'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', 'FontSize', 10);
        end
    else
        text(0.5, 0.5, '无有效角度数据', 'HorizontalAlignment', 'center', 'FontSize', 12);
    end
else
    text(0.5, 0.5, '子空间夹角数据不可用', 'HorizontalAlignment', 'center', 'FontSize', 12);
end
title('JMSDL: 子空间夹角变化', 'FontSize', 14);

% 子图3: 主空间维度演化
subplot(2,3,3);
if isfield(results_jmsdl, 'subspace_dims') && ~isempty(results_jmsdl.subspace_dims)
    subspace_dims = results_jmsdl.subspace_dims;
    plot(1:5, subspace_dims, 'go-', 'LineWidth', 2, 'MarkerSize', 8);
    xlabel('模式', 'FontSize', 12);
    ylabel('主空间维度', 'FontSize', 12);
    title('JMSDL: 主空间维度演化', 'FontSize', 14);
    grid on;
    set(gca, 'FontSize', 12);
    
    % 添加数值标签
    for i = 1:length(subspace_dims)
        text(i, subspace_dims(i), sprintf('%d', subspace_dims(i)), ...
             'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', 'FontSize', 10);
    end
else
    text(0.5, 0.5, '主空间维度数据不可用', 'HorizontalAlignment', 'center', 'FontSize', 12);
    title('JMSDL: 主空间维度演化', 'FontSize', 14);
end

% 子图4: FAR性能
subplot(2,3,4);
if isfield(results_jmsdl, 'FAR_vec') && ~isempty(results_jmsdl.FAR_vec)
    FAR_vec = results_jmsdl.FAR_vec;
    bar(1:5, FAR_vec, 'FaceColor', [0.8, 0.4, 0.4]);
    xlabel('模式', 'FontSize', 12);
    ylabel('FAR (误报率)', 'FontSize', 12);
    title('JMSDL: FAR性能', 'FontSize', 14);
    grid on;
    set(gca, 'FontSize', 12);
    
    % 添加数值标签
    for i = 1:length(FAR_vec)
        if ~isnan(FAR_vec(i))
            text(i, FAR_vec(i), sprintf('%.3f', FAR_vec(i)), ...
                 'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', 'FontSize', 10);
        end
    end
else
    text(0.5, 0.5, 'FAR数据不可用', 'HorizontalAlignment', 'center', 'FontSize', 12);
    title('JMSDL: FAR性能', 'FontSize', 14);
end

% 子图5: FDR性能
subplot(2,3,5);
if isfield(results_jmsdl, 'FDR_vec') && ~isempty(results_jmsdl.FDR_vec)
    FDR_vec = results_jmsdl.FDR_vec;
    bar(1:5, FDR_vec, 'FaceColor', [0.4, 0.8, 0.4]);
    xlabel('模式', 'FontSize', 12);
    ylabel('FDR (故障检测率)', 'FontSize', 12);
    title('JMSDL: FDR性能', 'FontSize', 14);
    grid on;
    set(gca, 'FontSize', 12);
    
    % 添加数值标签
    for i = 1:length(FDR_vec)
        if ~isnan(FDR_vec(i))
            text(i, FDR_vec(i), sprintf('%.3f', FDR_vec(i)), ...
                 'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', 'FontSize', 10);
        end
    end
else
    text(0.5, 0.5, 'FDR数据不可用', 'HorizontalAlignment', 'center', 'FontSize', 12);
    title('JMSDL: FDR性能', 'FontSize', 14);
end

% 子图6: 方法总结
subplot(2,3,6);
axis off;
summary_text = {'JMSDL方法总结:', '', ...
                sprintf('字典大小: %dx%d', size(results_jmsdl.final_dict,1), size(results_jmsdl.final_dict,2)), ...
                sprintf('训练模式数: %d', length(results_jmsdl.Dictionary_history))};

if isfield(results_jmsdl, 'NMSC_history') && ~isempty(results_jmsdl.NMSC_history)
    summary_text{end+1} = sprintf('平均NMSC: %.4f', mean(results_jmsdl.NMSC_history));
end

if isfield(results_jmsdl, 'Subspace_angle_history') && ~isempty(results_jmsdl.Subspace_angle_history)
    valid_angles = results_jmsdl.Subspace_angle_history(~isnan(results_jmsdl.Subspace_angle_history));
    if ~isempty(valid_angles)
        summary_text{end+1} = sprintf('平均子空间夹角: %.4f弧度', mean(valid_angles));
    end
end

if isfield(results_jmsdl, 'FAR_overall') && ~isnan(results_jmsdl.FAR_overall)
    summary_text{end+1} = sprintf('总体FAR: %.4f', results_jmsdl.FAR_overall);
end

if isfield(results_jmsdl, 'FDR_overall') && ~isnan(results_jmsdl.FDR_overall)
    summary_text{end+1} = sprintf('总体FDR: %.4f', results_jmsdl.FDR_overall);
end

text(0.1, 0.8, summary_text, 'FontSize', 12, 'VerticalAlignment', 'top');

sgtitle('JMSDL方法综合演化分析', 'FontSize', 16);

%% 图2: JMSDL字典变化热图分析
if isfield(results_jmsdl, 'Dictionary_history') && length(results_jmsdl.Dictionary_history) >= 2
    figure('Position', [100, 100, 1200, 800]);
    
    D_first = results_jmsdl.Dictionary_history{1};
    D_last = results_jmsdl.Dictionary_history{end};
    
    % 子图1: 初始字典
    subplot(2,3,1);
    imagesc(D_first);
    colorbar;
    title('JMSDL: 初始字典 (Mode 1)', 'FontSize', 12);
    xlabel('原子索引');
    ylabel('特征维度');
    
    % 子图2: 最终字典
    subplot(2,3,2);
    imagesc(D_last);
    colorbar;
    title('JMSDL: 最终字典 (Mode 5)', 'FontSize', 12);
    xlabel('原子索引');
    ylabel('特征维度');
    
    % 子图3: 差分热图
    subplot(2,3,3);
    diff_dict = D_last - D_first;
    imagesc(diff_dict);
    colorbar;
    title('JMSDL: 字典变化 (最终-初始)', 'FontSize', 12);
    xlabel('原子索引');
    ylabel('特征维度');
    
    % 子图4-6: 各模式转换的变化
    for i = 2:min(4, length(results_jmsdl.Dictionary_history))
        subplot(2,3,3+i-1);
        D_prev = results_jmsdl.Dictionary_history{i-1};
        D_curr = results_jmsdl.Dictionary_history{i};
        diff_step = D_curr - D_prev;
        imagesc(diff_step);
        colorbar;
        title(sprintf('JMSDL: Mode %d→%d 变化', i-1, i), 'FontSize', 12);
        xlabel('原子索引');
        ylabel('特征维度');
    end
    
    sgtitle('JMSDL方法字典变化热图分析', 'FontSize', 16);
end

%% 图3: JMSDL监测统计量分析
if isfield(results_jmsdl, 'R_test') && ~isempty(results_jmsdl.R_test)
    figure('Position', [200, 200, 1200, 800]);
    
    R_test = results_jmsdl.R_test;
    R_limit = results_jmsdl.R_limit;
    
    % 子图1: 监测统计量序列
    subplot(2,2,1);
    plot(R_test, 'b', 'LineWidth', 1.2); hold on;
    yline(R_limit, '--k', 'LineWidth', 2, 'Color', 'r');
    xlabel('样本编号', 'FontSize', 12);
    ylabel('R统计量', 'FontSize', 12);
    title('JMSDL: 在线监测统计量与控制限', 'FontSize', 14);
    legend('测试样本','控制限', 'Location', 'best');
    grid on;
    set(gca,'FontSize',12);
    
    % 子图2: 统计量直方图
    subplot(2,2,2);
    histogram(R_test, 50, 'FaceAlpha', 0.7, 'EdgeColor', 'k');
    xline(R_limit, '--r', 'LineWidth', 2);
    xlabel('R统计量', 'FontSize', 12);
    ylabel('频次', 'FontSize', 12);
    title('JMSDL: 监测统计量分布', 'FontSize', 14);
    legend('统计量分布','控制限', 'Location', 'best');
    grid on;
    set(gca,'FontSize',12);
    
    % 子图3: 各模式的FAR/FDR详细分析
    subplot(2,2,3);
    if isfield(results_jmsdl, 'FAR_vec') && isfield(results_jmsdl, 'FDR_vec')
        FAR_vec = results_jmsdl.FAR_vec;
        FDR_vec = results_jmsdl.FDR_vec;
        
        x = 1:5;
        width = 0.35;
        bar(x - width/2, FAR_vec, width, 'DisplayName', 'FAR', 'FaceColor', [0.8, 0.4, 0.4]);
        hold on;
        bar(x + width/2, FDR_vec, width, 'DisplayName', 'FDR', 'FaceColor', [0.4, 0.8, 0.4]);
        set(gca, 'XTick', x, 'XTickLabel', arrayfun(@(i) sprintf('Mode %d', i), 1:5, 'UniformOutput', false));
        ylabel('性能指标', 'FontSize', 12);
        title('JMSDL: 各模式FAR/FDR详细分析', 'FontSize', 14);
        legend('Location', 'best');
        grid on;
        set(gca, 'FontSize', 12);
        
        % 添加数值标签
        for i = 1:5
            if ~isnan(FAR_vec(i))
                text(i - width/2, FAR_vec(i) + 0.01, sprintf('%.3f', FAR_vec(i)), ...
                     'HorizontalAlignment', 'center', 'FontSize', 10);
            end
            if ~isnan(FDR_vec(i))
                text(i + width/2, FDR_vec(i) + 0.01, sprintf('%.3f', FDR_vec(i)), ...
                     'HorizontalAlignment', 'center', 'FontSize', 10);
            end
        end
    end
    
    % 子图4: 监测区域分析
    subplot(2,2,4);
    n_mode = 5; n_each = 1000; n_normal = 500; n_fault = 500;
    colors = ['r', 'g', 'b', 'm', 'c'];
    
    for m = 1:n_mode
        idx_start = (m-1)*n_each + 1;
        idx_normal = idx_start : idx_start + n_normal - 1;
        idx_fault  = idx_start + n_normal : idx_start + n_each - 1;
        
        % 绘制正常区域
        plot(idx_normal, R_test(idx_normal), '.', 'Color', colors(m), 'MarkerSize', 4, ...
             'DisplayName', sprintf('Mode %d Normal', m));
        hold on;
        
        % 绘制故障区域
        plot(idx_fault, R_test(idx_fault), 'x', 'Color', colors(m), 'MarkerSize', 4, ...
             'DisplayName', sprintf('Mode %d Fault', m));
    end
    
    yline(R_limit, '--k', 'LineWidth', 2, 'Color', 'r', 'DisplayName', '控制限');
    xlabel('样本编号', 'FontSize', 12);
    ylabel('R统计量', 'FontSize', 12);
    title('JMSDL: 各模式监测区域分析', 'FontSize', 14);
    legend('Location', 'best');
    grid on;
    set(gca,'FontSize',12);
    
    sgtitle('JMSDL方法监测统计量分析', 'FontSize', 16);
end

end
