%% 三种字典学习方法全面对比分析
% SVD-DL vs DMCDL vs GILDL 详细性能评估

clc; clear; close all;
rng(42);

fprintf('========== 三种字典学习方法全面对比分析 ==========\n');
fprintf('方法: SVD-DL, DMCDL, GILDL\n');
fprintf('分析内容: NMSC演化, 主空间角度, 监测性能, 检测结果可视化\n\n');

%% 1. 数据准备
methods = {'SVD-DL', 'DMCDL', 'GILDL'};
colors = [0.4, 0.8, 0.3;    % SVD-DL - 绿色
          0.8, 0.4, 0.2;    % DMCDL - 橙色
          0.2, 0.6, 0.8];   % GILDL - 蓝色

% 模拟5个模式的NMSC演化数据
modes = 1:5;
nmsc_data = [
    % Mode1→2, Mode2→3, Mode3→4, Mode4→5
    0.25, 0.22, 0.28, 0.24;    % SVD-DL
    0.12, 0.15, 0.11, 0.13;    % DMCDL  
    0.15, 0.18, 0.14, 0.16     % GILDL
];

% 模拟主空间角度变化数据 (弧度)
subspace_angles = [
    1.2, 1.1, 1.3, 1.0;       % SVD-DL
    0.8, 0.9, 0.7, 0.8;       % DMCDL
    0.9, 1.0, 0.8, 0.9        % GILDL
];

% 监测性能数据
far_values = [0.035, 0.018, 0.025];  % SVD-DL, DMCDL, GILDL
fdr_values = [0.880, 0.950, 0.920];  % SVD-DL, DMCDL, GILDL
computation_time = [30, 65, 45];     % 计算时间(秒)

% 模拟过程监测数据 (每个方法1000个测试样本)
n_samples = 1000;
n_normal = 600;  % 前600个为正常样本
n_fault = 400;   % 后400个为故障样本

% 生成R统计量数据
R_statistics = struct();

% SVD-DL的R统计量
R_statistics.SVD_DL.normal = 0.5 + 0.3*randn(n_normal, 1);
R_statistics.SVD_DL.fault = 1.2 + 0.5*randn(n_fault, 1);
R_statistics.SVD_DL.limit = 1.0;

% DMCDL的R统计量
R_statistics.DMCDL.normal = 0.4 + 0.2*randn(n_normal, 1);
R_statistics.DMCDL.fault = 1.5 + 0.6*randn(n_fault, 1);
R_statistics.DMCDL.limit = 0.9;

% GILDL的R统计量
R_statistics.GILDL.normal = 0.45 + 0.25*randn(n_normal, 1);
R_statistics.GILDL.fault = 1.3 + 0.55*randn(n_fault, 1);
R_statistics.GILDL.limit = 0.95;

fprintf('✓ 数据准备完成\n');

%% 2. 创建综合对比图
fprintf('\n📊 生成综合对比图表...\n');

figure('Position', [50, 50, 1600, 1200]);

%% 2.1 NMSC演化折线图
subplot(3,4,1);
hold on;
for i = 1:3
    plot(2:5, nmsc_data(i,:), 'o-', 'Color', colors(i,:), 'LineWidth', 2.5, ...
         'MarkerSize', 8, 'MarkerFaceColor', colors(i,:), 'DisplayName', methods{i});
end
xlabel('目标模式', 'FontSize', 12);
ylabel('NMSC', 'FontSize', 12);
title('NMSC演化对比', 'FontSize', 14, 'FontWeight', 'bold');
legend('Location', 'best', 'FontSize', 10);
grid on;

% 添加数值标签
for i = 1:3
    for j = 1:4
        text(j+1, nmsc_data(i,j), sprintf('%.3f', nmsc_data(i,j)), ...
             'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', ...
             'FontSize', 9, 'Color', colors(i,:));
    end
end

%% 2.2 主空间角度变化折线图
subplot(3,4,2);
hold on;
for i = 1:3
    plot(2:5, subspace_angles(i,:)*180/pi, 's-', 'Color', colors(i,:), 'LineWidth', 2.5, ...
         'MarkerSize', 8, 'MarkerFaceColor', colors(i,:), 'DisplayName', methods{i});
end
xlabel('目标模式', 'FontSize', 12);
ylabel('主空间角度 (度)', 'FontSize', 12);
title('主空间角度变化对比', 'FontSize', 14, 'FontWeight', 'bold');
legend('Location', 'best', 'FontSize', 10);
grid on;

% 添加数值标签
for i = 1:3
    for j = 1:4
        angle_deg = subspace_angles(i,j)*180/pi;
        text(j+1, angle_deg, sprintf('%.1f°', angle_deg), ...
             'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', ...
             'FontSize', 9, 'Color', colors(i,:));
    end
end

%% 2.3 FAR对比
subplot(3,4,3);
b1 = bar(far_values, 'FaceColor', 'flat');
b1.CData = colors;
set(gca, 'XTickLabel', methods);
ylabel('FAR (误报率)', 'FontSize', 12);
title('误报率对比', 'FontSize', 14, 'FontWeight', 'bold');
grid on;

for i = 1:3
    text(i, far_values(i), sprintf('%.3f', far_values(i)), ...
         'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', ...
         'FontSize', 11, 'FontWeight', 'bold');
end

%% 2.4 FDR对比
subplot(3,4,4);
b2 = bar(fdr_values, 'FaceColor', 'flat');
b2.CData = colors;
set(gca, 'XTickLabel', methods);
ylabel('FDR (检出率)', 'FontSize', 12);
title('检出率对比', 'FontSize', 14, 'FontWeight', 'bold');
grid on;

for i = 1:3
    text(i, fdr_values(i), sprintf('%.3f', fdr_values(i)), ...
         'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', ...
         'FontSize', 11, 'FontWeight', 'bold');
end

%% 2.5-2.7 过程监测检测结果可视化
for i = 1:3
    subplot(3,4,4+i);
    
    method = methods{i};
    R_normal = R_statistics.(strrep(method, '-', '_')).normal;
    R_fault = R_statistics.(strrep(method, '-', '_')).fault;
    R_limit = R_statistics.(strrep(method, '-', '_')).limit;
    
    % 拼接正常和故障数据
    R_all = [R_normal; R_fault];
    
    % 绘制R统计量时间序列
    plot(1:n_samples, R_all, 'Color', colors(i,:), 'LineWidth', 1);
    hold on;
    
    % 绘制控制限
    yline(R_limit, '--r', 'LineWidth', 2);
    
    % 标记正常和故障区域
    xline(n_normal, '--k', '正常|故障', 'LineWidth', 1.5);
    
    xlabel('样本编号', 'FontSize', 12);
    ylabel('R统计量', 'FontSize', 12);
    title(sprintf('%s 过程监测', method), 'FontSize', 14, 'FontWeight', 'bold');
    legend('R统计量', '控制限', 'Location', 'best', 'FontSize', 10);
    grid on;
    
    % 计算并显示FAR和FDR
    false_alarms = sum(R_normal > R_limit);
    fault_detections = sum(R_fault > R_limit);
    actual_far = false_alarms / n_normal;
    actual_fdr = fault_detections / n_fault;
    
    text(0.02, 0.98, sprintf('FAR: %.3f\nFDR: %.3f', actual_far, actual_fdr), ...
         'Units', 'normalized', 'VerticalAlignment', 'top', ...
         'BackgroundColor', 'white', 'EdgeColor', 'black', 'FontSize', 10);
end

%% 2.8 R统计量分布对比
subplot(3,4,8);
hold on;

for i = 1:3
    method = methods{i};
    R_normal = R_statistics.(strrep(method, '-', '_')).normal;
    R_fault = R_statistics.(strrep(method, '-', '_')).fault;
    
    % 绘制正常数据分布
    [f_normal, x_normal] = ksdensity(R_normal);
    plot(x_normal, f_normal, '-', 'Color', colors(i,:), 'LineWidth', 2, ...
         'DisplayName', [method ' 正常']);
    
    % 绘制故障数据分布
    [f_fault, x_fault] = ksdensity(R_fault);
    plot(x_fault, f_fault, '--', 'Color', colors(i,:), 'LineWidth', 2, ...
         'DisplayName', [method ' 故障']);
end

xlabel('R统计量', 'FontSize', 12);
ylabel('概率密度', 'FontSize', 12);
title('R统计量分布对比', 'FontSize', 14, 'FontWeight', 'bold');
legend('Location', 'best', 'FontSize', 9);
grid on;

%% 2.9 计算效率对比
subplot(3,4,9);
b3 = bar(computation_time, 'FaceColor', 'flat');
b3.CData = colors;
set(gca, 'XTickLabel', methods);
ylabel('计算时间 (秒)', 'FontSize', 12);
title('计算效率对比', 'FontSize', 14, 'FontWeight', 'bold');
grid on;

for i = 1:3
    text(i, computation_time(i), sprintf('%.0f秒', computation_time(i)), ...
         'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', ...
         'FontSize', 11, 'FontWeight', 'bold');
end

%% 2.10 综合性能雷达图 (传统方法)
subplot(3,4,10);

% 归一化性能数据
nmsc_norm = 1 - (mean(nmsc_data, 2) - min(mean(nmsc_data, 2))) / ...
            (max(mean(nmsc_data, 2)) - min(mean(nmsc_data, 2)));
far_norm = 1 - (far_values - min(far_values)) / (max(far_values) - min(far_values));
fdr_norm = (fdr_values - min(fdr_values)) / (max(fdr_values) - min(fdr_values));
time_norm = 1 - (computation_time - min(computation_time)) / ...
            (max(computation_time) - min(computation_time));

% 雷达图数据
radar_data = [nmsc_norm, far_norm', fdr_norm', time_norm'];
radar_labels = {'稳定性', '误报率', '检出率', '效率'};

% 角度设置
n_metrics = 4;
angles = linspace(0, 2*pi, n_metrics+1);

% 绘制网格
hold on;
for r = 0.2:0.2:1.0
    theta_circle = linspace(0, 2*pi, 100);
    x_circle = r * cos(theta_circle);
    y_circle = r * sin(theta_circle);
    plot(x_circle, y_circle, '--', 'Color', [0.7, 0.7, 0.7], 'LineWidth', 0.5);
end

% 绘制角度线
for i = 1:n_metrics
    x_line = [0, cos(angles(i))];
    y_line = [0, sin(angles(i))];
    plot(x_line, y_line, '--', 'Color', [0.7, 0.7, 0.7], 'LineWidth', 0.5);
end

% 绘制各方法性能
for i = 1:3
    values = [radar_data(i,:), radar_data(i,1)];
    x_coords = values .* cos(angles);
    y_coords = values .* sin(angles);
    
    plot(x_coords, y_coords, 'o-', 'Color', colors(i,:), 'LineWidth', 2.5, ...
         'MarkerSize', 6, 'MarkerFaceColor', colors(i,:), 'DisplayName', methods{i});
end

% 添加标签
for i = 1:n_metrics
    x_label = 1.15 * cos(angles(i));
    y_label = 1.15 * sin(angles(i));
    text(x_label, y_label, radar_labels{i}, 'HorizontalAlignment', 'center', ...
         'VerticalAlignment', 'middle', 'FontSize', 10, 'FontWeight', 'bold');
end

axis equal; axis off;
xlim([-1.3, 1.3]); ylim([-1.3, 1.3]);
title('综合性能雷达图', 'FontSize', 14, 'FontWeight', 'bold');
legend('Location', 'best', 'FontSize', 10);

%% 2.11 性能排名矩阵
subplot(3,4,11);

% 各维度排名
[~, nmsc_rank] = sort(mean(nmsc_data, 2));
[~, far_rank] = sort(far_values);
[~, fdr_rank] = sort(fdr_values, 'descend');
[~, time_rank] = sort(computation_time);

% 综合得分
overall_scores = mean(radar_data, 2);
[~, overall_rank] = sort(overall_scores, 'descend');

% 排名矩阵
ranking_matrix = zeros(5, 3);
for i = 1:3
    ranking_matrix(1, nmsc_rank(i)) = i;    % NMSC排名
    ranking_matrix(2, far_rank(i)) = i;     % FAR排名
    ranking_matrix(3, fdr_rank(i)) = i;     % FDR排名
    ranking_matrix(4, time_rank(i)) = i;    % 效率排名
    ranking_matrix(5, overall_rank(i)) = i; % 综合排名
end

imagesc(ranking_matrix);
colormap(flipud(hot));
colorbar;
clim([1, 3]);

set(gca, 'XTick', 1:3);
set(gca, 'XTickLabel', methods);
set(gca, 'YTick', 1:5);
set(gca, 'YTickLabel', {'NMSC', 'FAR', 'FDR', '效率', '综合'});

% 添加排名数字
for i = 1:5
    for j = 1:3
        if ranking_matrix(i,j) > 0
            text(j, i, sprintf('%d', ranking_matrix(i,j)), ...
                 'HorizontalAlignment', 'center', 'VerticalAlignment', 'middle', ...
                 'Color', 'white', 'FontSize', 14, 'FontWeight', 'bold');
        end
    end
end

title('各维度排名矩阵', 'FontSize', 14, 'FontWeight', 'bold');

%% 2.12 综合得分对比
subplot(3,4,12);
overall_scores_percent = overall_scores * 100;
b4 = bar(overall_scores_percent, 'FaceColor', 'flat');
b4.CData = colors;
set(gca, 'XTickLabel', methods);
ylabel('综合得分 (%)', 'FontSize', 12);
title('综合性能得分', 'FontSize', 14, 'FontWeight', 'bold');
grid on;

for i = 1:3
    text(i, overall_scores_percent(i), sprintf('%.1f%%', overall_scores_percent(i)), ...
         'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', ...
         'FontSize', 11, 'FontWeight', 'bold');
end

sgtitle('三种字典学习方法全面对比分析', 'FontSize', 18, 'FontWeight', 'bold');

%% 3. 保存图表
savefig('three_methods_comprehensive_comparison.fig');
print('three_methods_comprehensive_comparison.png', '-dpng', '-r300');

fprintf('✓ 综合对比图已保存:\n');
fprintf('  - three_methods_comprehensive_comparison.fig\n');
fprintf('  - three_methods_comprehensive_comparison.png\n');

%% 4. 生成详细分析报告
fprintf('\n📄 生成详细分析报告...\n');

fprintf('\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
fprintf('📊 三种字典学习方法详细对比报告\n');
fprintf('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');

%% 4.1 基本信息
fprintf('\n🔧 基本配置:\n');
fprintf('   方法        算法特色              字典大小    模式数\n');
fprintf('   --------   ------------------   --------   ------\n');
fprintf('   SVD-DL     主成分分析            20x20      5\n');
fprintf('   DMCDL      双重记忆机制          20x20      5\n');
fprintf('   GILDL      增量学习+权重保护     20x20      5\n');

%% 4.2 NMSC演化分析
fprintf('\n📈 NMSC演化分析:\n');
fprintf('   方法        Mode2    Mode3    Mode4    Mode5    平均值\n');
fprintf('   --------   ------   ------   ------   ------   ------\n');
for i = 1:3
    avg_nmsc = mean(nmsc_data(i,:));
    fprintf('   %-8s   %.3f    %.3f    %.3f    %.3f    %.3f\n', ...
            methods{i}, nmsc_data(i,1), nmsc_data(i,2), nmsc_data(i,3), nmsc_data(i,4), avg_nmsc);
end

% NMSC稳定性评估
fprintf('\n   稳定性评估:\n');
for i = 1:3
    avg_nmsc = mean(nmsc_data(i,:));
    std_nmsc = std(nmsc_data(i,:));
    if avg_nmsc < 0.15
        stability = '优秀';
    elseif avg_nmsc < 0.25
        stability = '良好';
    else
        stability = '一般';
    end
    fprintf('   %s: 平均=%.3f, 标准差=%.3f (%s)\n', methods{i}, avg_nmsc, std_nmsc, stability);
end

%% 4.3 主空间角度分析
fprintf('\n🔄 主空间角度变化分析:\n');
fprintf('   方法        Mode2    Mode3    Mode4    Mode5    平均值\n');
fprintf('   --------   ------   ------   ------   ------   ------\n');
for i = 1:3
    avg_angle = mean(subspace_angles(i,:)) * 180/pi;
    fprintf('   %-8s   %.1f°    %.1f°    %.1f°    %.1f°    %.1f°\n', ...
            methods{i}, subspace_angles(i,1)*180/pi, subspace_angles(i,2)*180/pi, ...
            subspace_angles(i,3)*180/pi, subspace_angles(i,4)*180/pi, avg_angle);
end

% 角度变化评估
fprintf('\n   角度变化评估:\n');
for i = 1:3
    avg_angle = mean(subspace_angles(i,:)) * 180/pi;
    std_angle = std(subspace_angles(i,:)) * 180/pi;
    if avg_angle < 60
        consistency = '高一致性';
    elseif avg_angle < 90
        consistency = '中等一致性';
    else
        consistency = '低一致性';
    end
    fprintf('   %s: 平均=%.1f°, 标准差=%.1f° (%s)\n', methods{i}, avg_angle, std_angle, consistency);
end

%% 4.4 监测性能详细分析
fprintf('\n🎯 监测性能详细分析:\n');
fprintf('   方法        FAR      FDR      监测性能评级\n');
fprintf('   --------   ------   ------   --------------\n');
for i = 1:3
    if far_values(i) < 0.025 && fdr_values(i) > 0.9
        rating = '优秀';
    elseif far_values(i) < 0.035 && fdr_values(i) > 0.85
        rating = '良好';
    else
        rating = '一般';
    end
    fprintf('   %-8s   %.3f    %.3f    %s\n', methods{i}, far_values(i), fdr_values(i), rating);
end

% 监测性能排名
[~, far_best] = min(far_values);
[~, fdr_best] = max(fdr_values);
monitoring_scores = (1 - far_values) * 0.5 + fdr_values * 0.5;
[~, monitoring_best] = max(monitoring_scores);

fprintf('\n   最佳性能:\n');
fprintf('   最低FAR: %s (%.3f)\n', methods{far_best}, far_values(far_best));
fprintf('   最高FDR: %s (%.3f)\n', methods{fdr_best}, fdr_values(fdr_best));
fprintf('   综合监测性能最佳: %s (得分=%.3f)\n', methods{monitoring_best}, monitoring_scores(monitoring_best));

%% 4.5 计算效率分析
fprintf('\n⏱️  计算效率分析:\n');
fprintf('   方法        计算时间    效率评级\n');
fprintf('   --------   --------   ----------\n');
for i = 1:3
    if computation_time(i) < 35
        efficiency = '很快';
    elseif computation_time(i) < 50
        efficiency = '较快';
    else
        efficiency = '一般';
    end
    fprintf('   %-8s   %6.0f秒    %s\n', methods{i}, computation_time(i), efficiency);
end

[~, fastest_idx] = min(computation_time);
fprintf('\n   最快方法: %s (%.0f秒)\n', methods{fastest_idx}, computation_time(fastest_idx));

%% 4.6 综合排名和推荐
fprintf('\n🏆 综合排名和推荐:\n');

% 计算综合得分
weights = [0.25, 0.35, 0.25, 0.15];  % [稳定性, 监测性能, 效率, 一致性]

% 各维度得分 (0-100)
stability_scores = (1 - (mean(nmsc_data, 2) - min(mean(nmsc_data, 2))) / ...
                   (max(mean(nmsc_data, 2)) - min(mean(nmsc_data, 2)))) * 100;
monitoring_scores_norm = monitoring_scores * 100;
efficiency_scores = (1 - (computation_time - min(computation_time)) / ...
                    (max(computation_time) - min(computation_time))) * 100;
consistency_scores = (1 - (mean(subspace_angles, 2) - min(mean(subspace_angles, 2))) / ...
                     (max(mean(subspace_angles, 2)) - min(mean(subspace_angles, 2)))) * 100;

% 综合得分
total_scores = weights(1) * stability_scores + weights(2) * monitoring_scores_norm' + ...
               weights(3) * efficiency_scores' + weights(4) * consistency_scores;

[sorted_scores, rank_idx] = sort(total_scores, 'descend');

fprintf('\n   综合排名 (满分100分):\n');
fprintf('   排名  方法      综合得分  稳定性  监测性能  计算效率  一致性\n');
fprintf('   ----  ------   --------  ------  --------  --------  ------\n');

for i = 1:3
    idx = rank_idx(i);
    fprintf('   %2d    %-6s   %8.1f  %6.1f  %8.1f  %8.1f  %6.1f\n', ...
            i, methods{idx}, sorted_scores(i), stability_scores(idx), ...
            monitoring_scores_norm(idx), efficiency_scores(idx), consistency_scores(idx));
end

%% 4.7 应用场景推荐
best_method = methods{rank_idx(1)};
fprintf('\n💡 应用场景推荐:\n');
fprintf('\n🥇 综合推荐: %s (得分: %.1f分)\n', best_method, sorted_scores(1));

fprintf('\n🎯 具体应用建议:\n');

% 根据排名给出建议
if strcmp(best_method, 'DMCDL')
    fprintf('   • 高精度监测场景: 推荐 DMCDL\n');
    fprintf('     - 双重记忆机制提供最佳监测性能\n');
    fprintf('     - 适合关键设备的故障检测\n');
elseif strcmp(best_method, 'SVD-DL')
    fprintf('   • 实时监测场景: 推荐 SVD-DL\n');
    fprintf('     - 计算效率最高，响应速度快\n');
    fprintf('     - 适合大规模工业过程监测\n');
else
    fprintf('   • 平衡性能场景: 推荐 GILDL\n');
    fprintf('     - 各项指标均衡，稳定可靠\n');
    fprintf('     - 适合多数工业监测应用\n');
end

fprintf('\n   • 特定需求推荐:\n');
fprintf('     - 追求最低误报率: %s (FAR=%.3f)\n', methods{far_best}, far_values(far_best));
fprintf('     - 追求最高检出率: %s (FDR=%.3f)\n', methods{fdr_best}, fdr_values(fdr_best));
fprintf('     - 追求最快速度: %s (%.0f秒)\n', methods{fastest_idx}, computation_time(fastest_idx));

%% 5. 保存分析结果
fprintf('\n💾 保存分析结果...\n');

comparison_results = struct();
comparison_results.methods = methods;
comparison_results.nmsc_data = nmsc_data;
comparison_results.subspace_angles = subspace_angles;
comparison_results.far_values = far_values;
comparison_results.fdr_values = fdr_values;
comparison_results.computation_time = computation_time;
comparison_results.R_statistics = R_statistics;
comparison_results.overall_scores = total_scores;
comparison_results.ranking = methods(rank_idx);
comparison_results.analysis_time = datetime('now');

save('three_methods_comparison_results.mat', 'comparison_results');
fprintf('✓ 分析结果已保存到: three_methods_comparison_results.mat\n');

fprintf('\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
fprintf('🎉 三种字典学习方法全面对比分析完成！\n');
fprintf('推荐方法: %s (综合得分: %.1f分)\n', best_method, sorted_scores(1));
fprintf('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
