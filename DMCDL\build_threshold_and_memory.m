function [Rtr,Yh] = build_threshold_and_memory(D,X,Y,M)
    %---------------------------------------------------------
    % 计算重构误差阈值 & 代表+判别样本
    %---------------------------------------------------------
    R = vecnorm(Y - D*X).^2;             % 每条样本重构误差
    % ----- KDE 得 99% 控制限 -----
    pd  = fitdist(R','Kernel','Bandwidth',[]);
    Rtr = icdf(pd,0.99);
    
    % ----- 样本记忆池 (代表 + 判别) -----
    % 代表性: 距离均值最近
    mu   = mean(Y,2);
    rho  = vecnorm(Y - mu).^2;
    [~,idxR] = mink(rho, ceil(M/2));
    
    % 判别性: |R-Rtr| 最小
    dis  = abs(R - Rtr);
    [~,idxD] = mink(dis, M - numel(idxR));
    
    Yh = Y(:, unique([idxR idxD]));
    end
    