%% 快速运行最优参数分析
% 一键执行NMSC和主方向子空间夹角分析

fprintf('========== 开始最优参数分析 ==========\n');
fprintf('分析目标: 计算NMSC和主方向子空间夹角\n');
fprintf('最优参数: λ=1e+03, n_atoms=20, sparsity=2\n');
fprintf('预期性能: FDR=1.0000, FAR=0.0084\n\n');

% 检查必要文件
required_files = {'mode1_train.mat', 'mode2_train.mat', 'mode3_train.mat', ...
                  'mode4_train.mat', 'mode5_train.mat', 'ksvd_simple_silent.m', ...
                  'omp.m', 'compute_nmsc_and_angles.m', 'visualize_dictionary_evolution.m'};

missing_files = {};
for i = 1:length(required_files)
    if ~exist(required_files{i}, 'file')
        missing_files{end+1} = required_files{i};
    end
end

if ~isempty(missing_files)
    fprintf('❌ 缺少以下必要文件:\n');
    for i = 1:length(missing_files)
        fprintf('   - %s\n', missing_files{i});
    end
    fprintf('请确保所有文件都在当前目录中。\n');
    return;
end

fprintf('✅ 所有必要文件检查完成\n\n');

% 运行分析
try
    fprintf('🚀 开始执行分析...\n');
    tic;
    
    % 执行主分析脚本
    analyze_optimal_parameters;
    
    elapsed_time = toc;
    fprintf('\n✅ 分析完成！\n');
    fprintf('⏱️  总耗时: %.1f 分钟\n', elapsed_time/60);
    
    % 加载结果并显示关键信息
    load('optimal_parameter_analysis.mat', 'results_optimal');
    
    fprintf('\n📋 快速结果总览:\n');
    fprintf('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
    
    % NMSC结果
    NMSC_history = results_optimal.NMSC_history;
    fprintf('📊 NMSC (归一化均方相干性):\n');
    for i = 1:4
        fprintf('   Mode %d→%d: %.4f\n', i, i+1, NMSC_history(i));
    end
    fprintf('   平均值: %.4f\n', mean(NMSC_history));
    
    % 子空间夹角结果
    Subspace_angle_history = results_optimal.Subspace_angle_history;
    valid_angles = Subspace_angle_history(~isnan(Subspace_angle_history));
    fprintf('\n📐 主方向子空间夹角 (弧度):\n');
    for i = 1:4
        if ~isnan(Subspace_angle_history(i))
            fprintf('   Mode %d→%d: %.4f弧度 (%.2f°)\n', i, i+1, ...
                    Subspace_angle_history(i), Subspace_angle_history(i)*180/pi);
        else
            fprintf('   Mode %d→%d: 无法计算\n', i, i+1);
        end
    end
    if ~isempty(valid_angles)
        fprintf('   平均值: %.4f弧度 (%.2f°)\n', mean(valid_angles), mean(valid_angles)*180/pi);
    end
    
    % 主空间维度
    subspace_dims = results_optimal.subspace_dims;
    fprintf('\n🔍 主空间维度演化:\n');
    for i = 1:5
        fprintf('   Mode %d: %d维\n', i, subspace_dims(i));
    end
    
    fprintf('\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
    
    % 结果解释
    fprintf('\n💡 结果解释:\n');
    
    % NMSC解释 (归一化均方变化度)
    avg_nmsc = mean(NMSC_history);
    fprintf('🔸 NMSC平均值 %.4f 说明 (归一化均方变化度):\n', avg_nmsc);
    if avg_nmsc < 0.1
        fprintf('   字典在模式转换中变化很小，保持了高度稳定性\n');
        fprintf('   这表明算法能够很好地保持已学习的特征\n');
    elseif avg_nmsc < 0.5
        fprintf('   字典在模式转换中有适度的变化\n');
        fprintf('   在稳定性和适应性之间取得了平衡\n');
    else
        fprintf('   字典在模式转换中变化较大\n');
        fprintf('   这表明算法具有强适应性，能够学习新的特征\n');
    end

    % 子空间夹角解释 (弧度)
    if ~isempty(valid_angles)
        avg_angle = mean(valid_angles);
        avg_angle_deg = avg_angle * 180 / pi;
        fprintf('\n🔸 子空间夹角平均值 %.4f弧度 (%.2f°) 说明:\n', avg_angle, avg_angle_deg);
        if avg_angle < pi/6  % 30度
            fprintf('   主方向在模式转换中变化较小，保持了良好的稳定性\n');
            fprintf('   这有利于保持监测的一致性\n');
        elseif avg_angle < pi/3  % 60度
            fprintf('   主方向在模式转换中有中等程度的变化\n');
            fprintf('   在稳定性和适应性之间取得了平衡\n');
        else
            fprintf('   主方向在模式转换中变化较大\n');
            fprintf('   这表明算法能够适应不同模式的主要特征\n');
        end
    end
    
    % 性能预期
    fprintf('\n🎯 基于这些指标，最优参数组合的特点:\n');
    fprintf('   ✓ 高FDR (1.0000): 能够检测到所有故障\n');
    fprintf('   ✓ 低FAR (0.0084): 误报率很低\n');
    fprintf('   ✓ NMSC值表明字典演化的稳定性\n');
    fprintf('   ✓ 子空间夹角反映主方向的变化程度\n');
    
    fprintf('\n📁 输出文件:\n');
    fprintf('   - SVD_DL-optimal_parameter_analysis.mat: 完整分析结果\n');
    fprintf('   - 多个可视化图表窗口已打开\n');
    
    fprintf('\n🎉 分析完成！请查看生成的图表以获得更详细的信息。\n');
    
catch ME
    fprintf('\n❌ 分析过程中出现错误:\n');
    fprintf('错误信息: %s\n', ME.message);
    fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    fprintf('\n请检查:\n');
    fprintf('1. 所有数据文件是否存在\n');
    fprintf('2. 所有函数文件是否在路径中\n');
    fprintf('3. MATLAB版本是否支持所用函数\n');
end
