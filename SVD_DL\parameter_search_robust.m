%% 鲁棒参数搜索实验 - 带完整错误处理的版本
% 专门处理数值计算中可能出现的各种错误

rng(42);

%% ========== 参数网格搜索设置 ==========
% 定义参数范围 - 使用中等规模进行测试
lambda_list = [1e-5, 1e-4, 1e-3, 1e-2];  % 4个lambda值
n_atoms_list = [40, 50, 60];  % 3个原子数
sparsity_list = [2, 3];  % 2个稀疏度

% 计算总的参数组合数
n_lambda = length(lambda_list);
n_atoms_count = length(n_atoms_list);
n_sparsity = length(sparsity_list);
total_combinations = n_lambda * n_atoms_count * n_sparsity;

fprintf('鲁棒测试版本 - 总共需要测试 %d 个参数组合\n', total_combinations);
fprintf('参数范围:\n');
fprintf('  lambda: [%.1e, %.1e, %.1e, %.1e]\n', lambda_list);
fprintf('  n_atoms: [%d, %d, %d]\n', n_atoms_list);
fprintf('  sparsity: [%d, %d]\n', sparsity_list);

% 存储所有结果
results = struct();
results.lambda_vals = [];
results.n_atoms_vals = [];
results.sparsity_vals = [];
results.FAR_vals = [];
results.FDR_vals = [];
results.R_test_all = {};
results.R_limit_vals = [];
results.error_messages = {};

combination_idx = 0;
success_count = 0;

% 三重嵌套循环进行网格搜索
for ll = 1:n_lambda
    lambda = lambda_list(ll);
    
    for aa = 1:n_atoms_count
        n_atoms = n_atoms_list(aa);
        
        for ss = 1:n_sparsity
            sparsity = sparsity_list(ss);
            combination_idx = combination_idx + 1;
            
            fprintf('\n==== 组合 %d/%d: lambda=%.2e, n_atoms=%d, sparsity=%d ====\n', ...
                    combination_idx, total_combinations, lambda, n_atoms, sparsity);
            
            try
                %% ========== 1. 加载训练数据 ==========
                load('mode1_train.mat');  % 变量 train_data，1000x8
                Y = train_data';          % [8 x 1000]
                
                % 检查数据有效性
                if any(isnan(Y(:))) || any(isinf(Y(:)))
                    error('训练数据包含 NaN 或 Inf 值');
                end
                
                %% ========== 2. K-SVD训练 ==========
                n_iter = 30;  % 减少迭代次数
                D_init = randn(8, n_atoms);
                for k = 1:n_atoms
                    D_init(:,k) = D_init(:,k) / norm(D_init(:,k));
                end
                
                % 检查初始字典
                if any(isnan(D_init(:))) || any(isinf(D_init(:)))
                    error('初始字典包含 NaN 或 Inf 值');
                end
                
                [Dictionary, ~] = ksvd_simple(Y, D_init, sparsity, n_iter);
                
                % 检查训练后的字典
                if any(isnan(Dictionary(:))) || any(isinf(Dictionary(:)))
                    error('训练后的字典包含 NaN 或 Inf 值');
                end

                [U, S, V] = svd(Dictionary, 'econ');
                singular_values = diag(S);
                
                % 检查奇异值
                if any(isnan(singular_values)) || any(isinf(singular_values))
                    error('字典的奇异值包含 NaN 或 Inf');
                end
                
                energy = cumsum(singular_values.^2) / sum(singular_values.^2);
                k_locked = find(energy >= 0.9, 1, 'first');
                if isempty(k_locked)
                    k_locked = min(3, size(U,2));  % 至少保留3个主成分
                end
                U_locked = U(:, 1:k_locked);

                Dictionary_history = cell(5,1);
                U_locked_history = cell(5,1);
                Dictionary_history{1} = Dictionary;
                U_locked_history{1} = U_locked;
                D_prev = Dictionary;
                U_locked_prev = U_locked;
                k_locked_prev = k_locked;
                
                % 简化的多模式训练（减少计算复杂度）
                for mode = 2:3  % 只训练到mode 3，减少计算时间
                    fname = sprintf('mode%d_train.mat', mode);
                    if ~exist(fname, 'file')
                        warning('文件 %s 不存在，跳过', fname);
                        continue;
                    end
                    
                    load(fname); Y_new = train_data';
                    
                    % 检查新数据
                    if any(isnan(Y_new(:))) || any(isinf(Y_new(:)))
                        warning('Mode %d 数据包含 NaN 或 Inf，跳过', mode);
                        continue;
                    end
                    
                    n_atoms_current = size(D_prev,2);
                    D_init = randn(8, n_atoms_current);
                    for k = 1:n_atoms_current
                        D_init(:,k) = D_init(:,k) / norm(D_init(:,k));
                    end
                    [D_new, ~] = ksvd_simple(Y_new, D_init, sparsity, 20);  % 进一步减少迭代

                    % 简化的字典融合
                    D_fused = 0.7 * D_prev + 0.3 * D_new;  % 简单的加权融合
                    
                    % 归一化
                    for k = 1:size(D_fused,2)
                        if norm(D_fused(:,k)) > 0
                            D_fused(:,k) = D_fused(:,k) / norm(D_fused(:,k));
                        end
                    end
                    
                    % 检查融合后的字典
                    if any(isnan(D_fused(:))) || any(isinf(D_fused(:)))
                        warning('融合后的字典包含 NaN 或 Inf，使用原字典');
                        D_fused = D_prev;
                    end

                    [U_new, S_new, ~] = svd(D_fused, 'econ');
                    singular_values_new = diag(S_new);
                    energy_new = cumsum(singular_values_new.^2) / sum(singular_values_new.^2);
                    k_locked_new = find(energy_new >= 0.9, 1, 'first');
                    if isempty(k_locked_new)
                        k_locked_new = min(3, size(U_new,2));
                    end
                    U_locked_new = U_new(:, 1:k_locked_new);

                    Dictionary_history{mode} = D_fused;
                    U_locked_history{mode} = U_locked_new;
                    D_prev = D_fused;
                    U_locked_prev = U_locked_new;
                    k_locked_prev = k_locked_new;
                end
                
                %% ========== 字典训练后性能评估 ==========
                D_K = Dictionary_history{end};
                
                % 最终检查字典
                if any(isnan(D_K(:))) || any(isinf(D_K(:)))
                    error('最终字典包含 NaN 或 Inf 值');
                end

                % --- 训练及测试数据准备 ---
                Y_train = []; Y_test = [];
                for mm = 1:3  % 只使用前3个模式的数据
                    try
                        load(sprintf("mode%d_train.mat",mm)); Y_train=[Y_train;train_data];
                        load(sprintf("mode%d_test_normal.mat",mm)); Y_test=[Y_test;test_normal_data];
                        load(sprintf("mode%d_test_fault.mat",mm));  Y_test=[Y_test;test_fault_data];
                    catch
                        warning('加载 mode %d 数据失败', mm);
                    end
                end
                Y_train = Y_train'; Y_test = Y_test';
                
                % 检查数据
                if isempty(Y_train) || isempty(Y_test)
                    error('训练或测试数据为空');
                end

                % === 训练集编码+统计量
                R_train = zeros(1, size(Y_train,2));
                for i = 1:size(Y_train,2)
                    y = Y_train(:,i);
                    try
                        x = omp(D_K, y, sparsity);
                        R_train(i) = norm(y - D_K*x, 2)^2;
                    catch
                        R_train(i) = norm(y, 2)^2;  % 如果OMP失败，使用原始信号的能量
                    end
                end
                
                % 检查并处理 R_train
                R_train = R_train(~isnan(R_train) & ~isinf(R_train) & R_train >= 0);
                if length(R_train) < 10
                    error('有效的训练统计量太少');
                end
                
                % 使用更鲁棒的阈值计算方法
                R_limit = quantile(R_train, 0.99);  % 使用99%分位数作为控制限
                
                % === 测试集编码+统计量
                R_test = zeros(1, size(Y_test,2));
                for i = 1:size(Y_test,2)
                    y = Y_test(:,i);
                    try
                        x = omp(D_K, y, sparsity);
                        R_test(i) = norm(y - D_K*x, 2)^2;
                    catch
                        R_test(i) = norm(y, 2)^2;
                    end
                end
                
                % 检查测试统计量
                if any(isnan(R_test)) || any(isinf(R_test))
                    warning('测试统计量包含异常值，进行清理');
                    R_test(isnan(R_test) | isinf(R_test)) = max(R_test(~isnan(R_test) & ~isinf(R_test)));
                end

                % === 计算FAR/FDR ===
                n_mode = 3; n_each = size(Y_test,2) / n_mode; 
                n_normal = n_each / 2; n_fault = n_each / 2;
                
                FAR_vec = zeros(n_mode,1); FDR_vec = zeros(n_mode,1);
                for m = 1:n_mode
                    idx_start = (m-1)*n_each + 1;
                    idx_normal = idx_start : idx_start + n_normal - 1;
                    idx_fault  = idx_start + n_normal : idx_start + n_each - 1;
                    
                    if max(idx_fault) <= length(R_test)
                        FAR_vec(m) = sum(R_test(idx_normal) > R_limit) / n_normal;
                        FDR_vec(m) = sum(R_test(idx_fault)  > R_limit) / n_fault;
                    end
                end
                
                FAR_current = mean(FAR_vec(FAR_vec >= 0));
                FDR_current = mean(FDR_vec(FDR_vec >= 0));
                
                % 最终检查结果
                if isnan(FAR_current) || isnan(FDR_current) || FAR_current < 0 || FDR_current < 0
                    error('FAR 或 FDR 计算结果异常');
                end

                % 存储成功的结果
                results.lambda_vals(end+1) = lambda;
                results.n_atoms_vals(end+1) = n_atoms;
                results.sparsity_vals(end+1) = sparsity;
                results.FAR_vals(end+1) = FAR_current;
                results.FDR_vals(end+1) = FDR_current;
                results.R_test_all{end+1} = R_test;
                results.R_limit_vals(end+1) = R_limit;
                results.error_messages{end+1} = '';
                
                success_count = success_count + 1;
                fprintf('✓ 成功: FAR=%.4f, FDR=%.4f\n', FAR_current, FDR_current);
                
            catch ME
                fprintf('✗ 失败: %s\n', ME.message);
                % 存储失败的结果
                results.lambda_vals(end+1) = lambda;
                results.n_atoms_vals(end+1) = n_atoms;
                results.sparsity_vals(end+1) = sparsity;
                results.FAR_vals(end+1) = NaN;
                results.FDR_vals(end+1) = NaN;
                results.R_test_all{end+1} = [];
                results.R_limit_vals(end+1) = NaN;
                results.error_messages{end+1} = ME.message;
            end
            
        end  % sparsity loop
    end  % n_atoms loop
end  % lambda loop

%% ========== 分析结果 ==========
fprintf('\n========== 鲁棒参数搜索完成 ==========\n');
fprintf('总共测试了 %d 个参数组合\n', length(results.FDR_vals));
fprintf('成功的参数组合: %d 个\n', success_count);
fprintf('失败的参数组合: %d 个\n', length(results.FDR_vals) - success_count);

if success_count == 0
    fprintf('所有参数组合都失败了！请检查数据和参数设置。\n');

    % 显示错误统计
    fprintf('\n错误类型统计:\n');
    error_types = {};
    for i = 1:length(results.error_messages)
        if ~isempty(results.error_messages{i})
            error_types{end+1} = results.error_messages{i};
        end
    end
    [unique_errors, ~, idx] = unique(error_types);
    for i = 1:length(unique_errors)
        count = sum(idx == i);
        fprintf('  %s: %d 次\n', unique_errors{i}, count);
    end

    return;
end

% 移除失败的结果
valid_idx = ~isnan(results.FDR_vals);
valid_FDR = results.FDR_vals(valid_idx);
valid_lambda = results.lambda_vals(valid_idx);
valid_n_atoms = results.n_atoms_vals(valid_idx);
valid_sparsity = results.sparsity_vals(valid_idx);
valid_FAR = results.FAR_vals(valid_idx);

% 找到最佳FDR对应的参数组合
[maxFDR, max_idx] = max(valid_FDR);
best_lambda = valid_lambda(max_idx);
best_n_atoms = valid_n_atoms(max_idx);
best_sparsity = valid_sparsity(max_idx);
best_FAR = valid_FAR(max_idx);

fprintf('\n最佳FDR = %.4f\n', maxFDR);
fprintf('对应参数组合:\n');
fprintf('  lambda = %.2e\n', best_lambda);
fprintf('  n_atoms = %d\n', best_n_atoms);
fprintf('  sparsity = %d\n', best_sparsity);
fprintf('  对应FAR = %.4f\n', best_FAR);

% 找到最小FAR对应的参数组合
[minFAR, min_idx] = min(valid_FAR);
minFAR_lambda = valid_lambda(min_idx);
minFAR_n_atoms = valid_n_atoms(min_idx);
minFAR_sparsity = valid_sparsity(min_idx);
minFAR_FDR = valid_FDR(min_idx);

fprintf('\n最小FAR = %.4f\n', minFAR);
fprintf('对应参数组合:\n');
fprintf('  lambda = %.2e\n', minFAR_lambda);
fprintf('  n_atoms = %d\n', minFAR_n_atoms);
fprintf('  sparsity = %d\n', minFAR_sparsity);
fprintf('  对应FDR = %.4f\n', minFAR_FDR);

%% ========== 可视化结果 ==========
if success_count > 1
    % 参数对FDR的影响
    figure('Position', [100, 100, 1200, 400]);

    subplot(1,3,1);
    scatter(valid_lambda, valid_FDR, 50, 'filled');
    xlabel('Lambda'); ylabel('FDR');
    title('Lambda对FDR的影响');
    set(gca, 'XScale', 'log');
    grid on;

    subplot(1,3,2);
    scatter(valid_n_atoms, valid_FDR, 50, 'filled');
    xlabel('N\_atoms'); ylabel('FDR');
    title('N\_atoms对FDR的影响');
    grid on;

    subplot(1,3,3);
    scatter(valid_sparsity, valid_FDR, 50, 'filled');
    xlabel('Sparsity'); ylabel('FDR');
    title('Sparsity对FDR的影响');
    grid on;

    % FAR vs FDR散点图
    figure('Position', [100, 100, 600, 500]);
    scatter(valid_FAR, valid_FDR, 50, 'filled'); hold on;
    scatter(best_FAR, maxFDR, 100, 'r', 'filled', '^');
    scatter(minFAR, minFAR_FDR, 100, 'b', 'filled', 'v');
    xlabel('FAR'); ylabel('FDR');
    title('FAR vs FDR 散点图 (鲁棒测试)');
    legend('所有参数组合', '最大FDR', '最小FAR', 'Location', 'best');
    grid on;
    set(gca,'FontSize',12);

    % 成功率分析
    figure('Position', [100, 100, 800, 300]);

    % 按lambda分析成功率
    subplot(1,3,1);
    lambda_success = zeros(size(lambda_list));
    for i = 1:length(lambda_list)
        total_for_lambda = sum(results.lambda_vals == lambda_list(i));
        success_for_lambda = sum(results.lambda_vals == lambda_list(i) & ~isnan(results.FDR_vals));
        lambda_success(i) = success_for_lambda / total_for_lambda;
    end
    bar(1:length(lambda_list), lambda_success);
    set(gca, 'XTickLabel', arrayfun(@(x) sprintf('%.0e', x), lambda_list, 'UniformOutput', false));
    ylabel('成功率');
    title('Lambda成功率');
    grid on;

    % 按n_atoms分析成功率
    subplot(1,3,2);
    atoms_success = zeros(size(n_atoms_list));
    for i = 1:length(n_atoms_list)
        total_for_atoms = sum(results.n_atoms_vals == n_atoms_list(i));
        success_for_atoms = sum(results.n_atoms_vals == n_atoms_list(i) & ~isnan(results.FDR_vals));
        atoms_success(i) = success_for_atoms / total_for_atoms;
    end
    bar(1:length(n_atoms_list), atoms_success);
    set(gca, 'XTickLabel', arrayfun(@(x) sprintf('%d', x), n_atoms_list, 'UniformOutput', false));
    ylabel('成功率');
    title('N\_atoms成功率');
    grid on;

    % 按sparsity分析成功率
    subplot(1,3,3);
    sparsity_success = zeros(size(sparsity_list));
    for i = 1:length(sparsity_list)
        total_for_sparsity = sum(results.sparsity_vals == sparsity_list(i));
        success_for_sparsity = sum(results.sparsity_vals == sparsity_list(i) & ~isnan(results.FDR_vals));
        sparsity_success(i) = success_for_sparsity / total_for_sparsity;
    end
    bar(1:length(sparsity_list), sparsity_success);
    set(gca, 'XTickLabel', arrayfun(@(x) sprintf('%d', x), sparsity_list, 'UniformOutput', false));
    ylabel('成功率');
    title('Sparsity成功率');
    grid on;
end

%% ========== 保存结果 ==========
save('parameter_search_robust_results.mat', 'results', 'success_count', ...
     'best_lambda', 'best_n_atoms', 'best_sparsity', 'maxFDR', 'best_FAR', 'minFAR');

fprintf('\n鲁棒测试结果已保存到 parameter_search_robust_results.mat\n');
fprintf('建议使用最佳参数组合: lambda=%.2e, n_atoms=%d, sparsity=%d\n', ...
        best_lambda, best_n_atoms, best_sparsity);
