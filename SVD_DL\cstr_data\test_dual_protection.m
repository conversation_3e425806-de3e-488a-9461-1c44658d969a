%% 测试双重保护SVD_DL方法

fprintf('========== 测试双重保护SVD_DL方法 ==========\n');

%% 1. 检查必要文件
fprintf('1. 检查必要文件...\n');

if exist('CSTR_3modes_train_data.mat', 'file')
    fprintf('   ✓ CSTR训练数据文件存在\n');
else
    fprintf('   ❌ 缺少CSTR训练数据文件\n');
    return;
end

if exist('learn_DL_CSTR.m', 'file')
    fprintf('   ✓ 双重保护算法文件存在\n');
else
    fprintf('   ❌ 缺少算法文件\n');
    return;
end

%% 2. 运行双重保护算法
fprintf('\n2. 运行双重保护算法...\n');

try
    tic;
    learn_DL_CSTR;
    elapsed_time = toc;
    fprintf('   ✅ 算法运行成功，耗时: %.1f秒\n', elapsed_time);
catch ME
    fprintf('   ❌ 算法运行失败: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('   错误位置: %s 第%d行\n', ME.stack(1).name, ME.stack(1).line);
    end
    return;
end

%% 3. 验证输出结果
fprintf('\n3. 验证输出结果...\n');

% 检查结果文件
if exist('CSTR_SVD_DL_results.mat', 'file')
    fprintf('   ✓ 结果文件已生成\n');
    
    % 加载并检查结果
    load('CSTR_SVD_DL_results.mat');
    
    % 检查字典历史
    if exist('Dictionary_history_CSTR', 'var')
        n_modes = length(Dictionary_history_CSTR);
        fprintf('   ✓ 字典历史包含 %d 个模式\n', n_modes);
        
        for i = 1:n_modes
            dict_size = size(Dictionary_history_CSTR{i});
            fprintf('     模式%d字典: %dx%d\n', i, dict_size(1), dict_size(2));
        end
    else
        fprintf('   ❌ 字典历史变量缺失\n');
    end
    
    % 检查主空间历史
    if exist('U_locked_history_CSTR', 'var')
        fprintf('   ✓ 主空间历史已保存\n');
        
        for i = 1:length(U_locked_history_CSTR)
            subspace_size = size(U_locked_history_CSTR{i});
            fprintf('     模式%d主空间: %dx%d\n', i, subspace_size(1), subspace_size(2));
        end
    else
        fprintf('   ❌ 主空间历史变量缺失\n');
    end
    
    % 检查主原子历史
    if exist('imp_idx_history_CSTR', 'var')
        fprintf('   ✓ 主原子历史已保存\n');
        
        for i = 1:length(imp_idx_history_CSTR)
            if ~isempty(imp_idx_history_CSTR{i})
                fprintf('     模式%d主原子数: %d\n', i, length(imp_idx_history_CSTR{i}));
            end
        end
    else
        fprintf('   ❌ 主原子历史变量缺失\n');
    end
    
else
    fprintf('   ❌ 结果文件未生成\n');
    return;
end

%% 4. 验证双重保护效果
fprintf('\n4. 验证双重保护效果...\n');

% 计算字典变化
if exist('Dictionary_history_CSTR', 'var') && length(Dictionary_history_CSTR) >= 2
    fprintf('   字典变化分析:\n');
    
    for i = 2:length(Dictionary_history_CSTR)
        D_prev = Dictionary_history_CSTR{i-1};
        D_curr = Dictionary_history_CSTR{i};
        
        % 计算Frobenius范数变化
        diff_norm = norm(D_curr - D_prev, 'fro');
        relative_change = diff_norm / norm(D_prev, 'fro');
        
        fprintf('     模式%d→%d: 绝对变化=%.4f, 相对变化=%.4f\n', ...
                i-1, i, diff_norm, relative_change);
    end
end

% 验证主空间稳定性
if exist('U_locked_history_CSTR', 'var') && length(U_locked_history_CSTR) >= 2
    fprintf('   主空间稳定性分析:\n');
    
    for i = 2:length(U_locked_history_CSTR)
        U_prev = U_locked_history_CSTR{i-1};
        U_curr = U_locked_history_CSTR{i};
        
        % 计算子空间夹角
        min_dim = min(size(U_prev,2), size(U_curr,2));
        if min_dim > 0
            try
                angle = subspace(U_prev(:,1:min_dim), U_curr(:,1:min_dim));
                fprintf('     模式%d→%d: 子空间夹角=%.4f弧度 (%.2f°)\n', ...
                        i-1, i, angle, angle*180/pi);
            catch
                fprintf('     模式%d→%d: 子空间夹角计算失败\n', i-1, i);
            end
        end
    end
end

%% 5. 检查可视化文件
fprintf('\n5. 检查可视化文件...\n');

if exist('CSTR_SVD_DL_dual_protection_evolution.fig', 'file')
    fprintf('   ✓ 可视化文件已生成\n');
    
    % 获取文件信息
    file_info = dir('CSTR_SVD_DL_dual_protection_evolution.fig');
    fprintf('   文件大小: %.1f KB\n', file_info.bytes/1024);
else
    fprintf('   ❌ 可视化文件未生成\n');
end

%% 6. 性能评估
fprintf('\n6. 性能评估...\n');

if exist('Dictionary_history_CSTR', 'var')
    % 计算NMSC
    epsilon = 1e-8;
    NMSC_values = [];
    
    for i = 2:length(Dictionary_history_CSTR)
        D_prev = Dictionary_history_CSTR{i-1};
        D_curr = Dictionary_history_CSTR{i};
        deltaD = D_curr - D_prev;
        NMSC = mean((deltaD(:).^2) ./ (D_prev(:).^2 + epsilon));
        NMSC_values(end+1) = NMSC;
        
        fprintf('   NMSC (模式%d→%d): %.6f\n', i-1, i, NMSC);
    end
    
    if ~isempty(NMSC_values)
        fprintf('   平均NMSC: %.6f\n', mean(NMSC_values));
        
        % 评估稳定性
        if mean(NMSC_values) < 0.1
            fprintf('   ✅ 字典变化很小，双重保护效果显著\n');
        elseif mean(NMSC_values) < 0.5
            fprintf('   ✅ 字典变化适中，保护机制有效\n');
        else
            fprintf('   ⚠️  字典变化较大，可能需要调整保护参数\n');
        end
    end
end

%% 7. 总结
fprintf('\n========== 测试总结 ==========\n');

% 检查所有关键组件
components = {
    'CSTR_SVD_DL_results.mat', '结果数据文件';
    'CSTR_SVD_DL_dual_protection_evolution.fig', '可视化文件'
};

fprintf('生成的文件:\n');
for i = 1:size(components, 1)
    filename = components{i, 1};
    description = components{i, 2};
    
    if exist(filename, 'file')
        file_info = dir(filename);
        fprintf('   ✓ %s (%.1f KB) - %s\n', filename, file_info.bytes/1024, description);
    else
        fprintf('   ❌ %s - %s (未生成)\n', filename, description);
    end
end

fprintf('\n双重保护机制验证:\n');
if exist('Dictionary_history_CSTR', 'var') && exist('U_locked_history_CSTR', 'var') && exist('imp_idx_history_CSTR', 'var')
    fprintf('   ✅ 主原子保护: 已实现\n');
    fprintf('   ✅ 主空间保护: 已实现\n');
    fprintf('   ✅ 字典演化历史: 已保存\n');
    fprintf('   ✅ 双重保护SVD_DL: 运行成功\n');
else
    fprintf('   ❌ 双重保护机制: 部分组件缺失\n');
end

fprintf('\n🎉 双重保护SVD_DL测试完成！\n');

% 使用建议
fprintf('\n💡 使用建议:\n');
fprintf('1. 查看可视化结果: open(''CSTR_SVD_DL_dual_protection_evolution.fig'')\n');
fprintf('2. 运行性能分析: analyze_CSTR_performance\n');
fprintf('3. 调整保护参数: 修改learn_DL_CSTR.m中的lambda值\n');
fprintf('4. 比较不同方法: 运行其他字典学习方法进行对比\n');
