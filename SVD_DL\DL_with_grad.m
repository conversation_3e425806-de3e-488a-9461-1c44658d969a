rng(42); % 固定随机种子便于复现
%% ===================== 1. 加载训练数据 =====================
load('mode1_train.mat');  % 得到变量 train_data，大小1000x8
Y = train_data';          % [8 x 1000]

%% ===================== 2. K-SVD 训练字典 ====================
n_atoms = 30;      % 字典原子数
sparsity = 2;      % 稀疏度
n_iter = 50;       % 迭代次数

% 字典初始化为标准正态分布+归一化
D_init = randn(8, n_atoms);
for k = 1:n_atoms
    D_init(:,k) = D_init(:,k) / norm(D_init(:,k));
end

% 训练
[Dictionary, ~] = ksvd_simple(Y, D_init, sparsity, n_iter);

%% ===================== 3. SVD分解与主方向空间锁定 =====================
[U, S, V] = svd(Dictionary, 'econ');
singular_values = diag(S);
energy = cumsum(singular_values.^2) / sum(singular_values.^2);
k_locked = find(energy >= 0.9, 1, 'first');
U_locked = U(:, 1:k_locked);

fprintf('主方向数目k_locked = %d\n', k_locked);
disp('主方向（U_locked）已完成上锁，可用于后续持续学习约束！');

save('mode1_locked_basis.mat', 'U_locked');  % 保存上锁主方向

figure;
plot(singular_values, 'o-'); xlabel('奇异值序号'); ylabel('奇异值大小');
title('KSVD字典的奇异值谱');
grid on;

%% ========= 4. 增量引入mode2到mode5并持续训练字典 =============

all_modes = 5   ;    % 工况数
Dictionary_history = cell(all_modes, 1);
imp_idx_history = cell(all_modes, 1);
U_locked_history = cell(all_modes, 1);

Dictionary_history{1} = Dictionary;
U_locked_history{1} = U_locked;
D_prev = Dictionary;
U_locked_prev = U_locked;
k_locked_prev = k_locked;

lambda = 1e-2;    % 主空间正则项系数
micro_iter = 30; % 微调步数

lr=1.1e-7;

for mode = 2:all_modes
    fprintf('==== 进入持续学习：mode%d ====\n', mode);

    % === 1. 新mode独立KSVD训练新字典 ===
    fname = sprintf('mode%d_train.mat', mode);
    load(fname);    % 得到 train_data
    Y_new = train_data';   % [8 x 1000]
    n_atoms = size(D_prev,2);
    sparsity = 2;
    n_iter = 30;

    % 随机初始化
    D_init = randn(8, n_atoms);
    for k = 1:n_atoms
        D_init(:,k) = D_init(:,k) / norm(D_init(:,k));
    end
    [D_new, ~] = ksvd_simple(Y_new, D_init, sparsity, n_iter);

    % === 2. SVD分解旧字典，主原子判定 ===
    [Uo, So, Vo] = svd(D_prev, 'econ');
    S_diag = diag(So);
    V_weight = abs(Vo) * S_diag;
    [~, idx] = sort(V_weight, 'descend');
    V_weight_sorted = V_weight(idx);
    energy_cum = cumsum(V_weight_sorted) / sum(V_weight_sorted);
    k_important = find(energy_cum >= 0.9, 1, 'first');
    imp_idx = idx(1:k_important);       % 重要原子
    unimp_idx = idx(k_important+1:end); % 不重要原子

    % === 3. 合成新字典：重要原子用 Slerp 微调，非重要原子用新原子 ===
    D_fused = D_prev;          % 先拷贝旧字典
    t = 0.02;                   % Slerp 插值因子(0~1)，可做超参
    for j = imp_idx            % 逐个重要原子球面插值
        v0 = D_prev(:,j);      v0 = v0 / norm(v0);           % 旧方向
        v1 = D_new(:,j);       v1 = v1 / norm(v1);           % 新方向
        cos_th = dot(v0, v1);
        cos_th = max(min(cos_th,1),-1);                      % 数值安全
        theta  = acos(cos_th);                               % 夹角
        if theta < 1e-6       % 几乎同向则直接保持
            v_slerp = v0;
        else                  % Slerp 公式
            v_slerp = (sin((1-t)*theta)/sin(theta))*v0 + ...
                       (sin(t*theta)/sin(theta))*v1;
        end
        D_fused(:,j) = v_slerp / norm(v_slerp);              % 单位化
    end
    
    % 非重要原子完全替换
    if ~isempty(unimp_idx)
        D_fused(:, unimp_idx) = D_new(:, unimp_idx);
    end

    % === 4. 主方向空间正则化微调（主空间保护） ===
    U_locked = Uo(:, 1:k_locked_prev); % 使用上一次主空间
    P_locked = U_locked * U_locked';

    for t = 1:micro_iter
        % 稀疏编码
        X = zeros(size(D_fused,2), size(Y_new,2));
        for n = 1:size(Y_new,2)
            X(:,n) = omp(D_fused, Y_new(:,n), sparsity);
        end

        % 梯度
        grad_recon = -2 * (Y_new - D_fused*X) * X';
        grad_proj = 2 * (P_locked')*P_locked * (D_fused - D_prev);

        % 梯度下降更新
        D_fused = D_fused - lr * (grad_recon + lambda * grad_proj);

        % 列归一化
        for k = 1:size(D_fused,2)
            D_fused(:,k) = D_fused(:,k) / norm(D_fused(:,k));
        end
    end

    % === 5. 新字典SVD，用于下轮主空间锁定 ===
    [U_new, S_new, ~] = svd(D_fused, 'econ');
    singular_values_new = diag(S_new);
    energy_new = cumsum(singular_values_new.^2) / sum(singular_values_new.^2);
    k_locked_new = find(energy_new >= 0.9, 1, 'first');
    U_locked_new = U_new(:, 1:k_locked_new);

    Dictionary_history{mode} = D_fused;
    imp_idx_history{mode} = imp_idx;
    U_locked_history{mode} = U_locked_new;
    D_prev = D_fused;
    U_locked_prev = U_locked_new;
    k_locked_prev = k_locked_new;

    fprintf('mode%d主原子数=%d，主空间维度=%d\n', mode, k_important, k_locked_new);
end

disp('所有mode持续字典学习与双重保护已完成！');

%% ==================== 可视化字典变化热图与NMSC ====================
D1 = Dictionary_history{1};
Dlast = Dictionary_history{end};

save("Dlast.mat",'Dlast');

deltaD = Dlast - D1;  

figure;
imagesc(deltaD);
colorbar;
xlabel('字典原子编号'); ylabel('观测量编号');
title('最终字典与mode1字典差分热图');
set(gca,'FontSize',12);

figure;
imagesc(abs(deltaD));
colorbar;
xlabel('字典原子编号'); ylabel('观测量编号');
title('字典原子绝对变化热图');
set(gca,'FontSize',12);

epsilon = 1e-8;
num = (Dlast - D1).^2;
den = D1.^2 + epsilon;
nmsc = mean(num(:) ./ den(:));
fprintf('归一化均方变化度（NMSC）: %.4f\n', nmsc);

