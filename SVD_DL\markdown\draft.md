# 1. **方法核心思想**

本方法针对多工况（Multi-mode）或持续学习（Continual Learning）下，传统字典学习容易遗忘早期工况主特征**的问题，提出了“主原子锁定+主方向空间保持”双重知识保护机制，实现了对老知识的稳定保留和新知识的自适应吸收。

* **主原子锁定**：通过奇异值分解（SVD）方法，判定字典中对主特征贡献最大的“原子”，并在后续学习中保持这些原子的表达方向基本不变。
* **主方向空间保护**：通过空间正则项，使得整个字典的主能量子空间（SVD主空间）在持续学习过程中不发生剧烈漂移。

---

# 2. **方法流程**

## **（1）初始工况字典学习（KSVD）**

对于初始工况1，有训练样本 $Y_1 \in \mathbb{R}^{d \times N_1}$：

$$
\min_{D, X} \| Y_1 - D_1 X_1 \|_F^2, \quad \text{s.t.}~\|x_i\|_0 \leq T
$$

其中：

* $D_1 \in \mathbb{R}^{d \times K}$：字典（$K$ 个原子，每列为1个原子）
* $X_1$：稀疏系数矩阵
* $T$：稀疏度

通常用 K-SVD 、OMP方法求解:
1. **主循环：重复若干次交替优化**
随机数初始化$D_1$

   * **a) 稀疏编码（Sparse Coding）**

     * 对每个样本 $y_{1}$，调用**omp**函数，在当前字典下求解稀疏系数 $x_n$：

       $$
       \min_x \|y_{1} - D_1 x_{1}\|_2^2 \quad \text{s.t. } \|x_1\|_0 \leq T
       $$
     * 得到$X$（$K \times N_1$）

   * **b) 字典更新（Dictionary Update）**

     * 逐列更新字典的每个原子 $d_k$：

       * 找出所有用到该原子的样本集合 $\omega$
       * 仅在这些样本上优化 $d_k$ 和对应系数
       * 构建残差矩阵 $R$，剔除 $d_k$ 当前贡献
       * 对 $R$ 做SVD分解，取第一个左奇异向量 $U(:,1)$ 作为新原子
       * 相应地更新稀疏系数
     * **再归一化每个字典原子**
---

* **omp** 给定字典 $D$、样本 $y$、稀疏度，返回最稀疏的编码 $x$，使 $y \approx D x$。
1. **初始化**

   * 残差向量 $r = y$
   * 已选原子集合 `idx_set = []`
   * 稀疏系数 $x = 0$

2. **贪心迭代，最多$T$次**

   * 计算残差与字典所有原子的内积
   * 找到相关性最大（最能解释残差）的原子
   * 如果已选则停止，否则加入集合
   * 在当前已选原子子空间，最小二乘拟合 $y$ 得到当前系数
   * 更新残差
---

## **（2）主原子和主方向空间提取（SVD分析）**

对初始/累积字典 $D^{(0)}$ 做SVD：

$$
D_1 = U S V^\top
$$

* $U \in \mathbb{R}^{d \times r}$：左奇异向量，**主方向空间正交基**；
* $S \in \mathbb{R}^{r \times r}$：奇异值，**对应每个主方向能量大小**,和U对应；
* $V \in \mathbb{R}^{K \times r}$：右奇异向量，反映字典原子与主方向关系。

**主方向空间**：取前 $k$ 个 $U$ 构成的子空间 $U_{locked}$（通常 $k$ 满足主能量累计90%）

$$
k = \min \left\{ l : \frac{\sum_{i=1}^l S_{ii}^2}{\sum_{i=1}^r S_{ii}^2} \geq \alpha \right\}
$$

常用 $\alpha=0.9$。

**主原子重要性判定**：对每个原子$j$计算其对主方向的加权贡献：

$$
w_j = \sum_{i=1}^{k} |V_{ji}| S_{ii}
$$

主原子即 $w_j$ 降序累计至90%能量的那些列。

---

## **（3）持续学习与双重保护机制**

### **a) 主原子锁定**

新主要替换旧次要

### **b) 主方向空间正则化（空间保持）**

* 对新工况数据KSVD训练得到的新字典 $D_{new}$，合成字典 $D_{fused}$（主原子用旧值，非主原子用新值）。
* 引入空间保护正则项，在微调/优化字典时，惩罚主空间方向的变化：

$$
\min_{D} \left\{ \| Y^{(m)} - D_{fused} X \|_F^2 + \lambda \| P_{locked} (D_{fused} - D_{prev}) \|_F^2 \right\}
$$

其中 $P_{locked} = U_{locked} U_{locked}^\top$ 是主空间的投影矩阵，$D_{prev}$ 是上一轮融合字典，$\lambda$为正则化强度。

改为闭式解更新,令导数为0，整理得到
$$
\lambda P_{locked}^TP_{locked}D_{fused}+D_{fused}XX^T=\lambda P_{locked}^TP_{locked}D_{prev}+YX^T
$$
形式为$AD_{fused}+D_{fused}B=C$,Sylvester函数求解。$D_{fused}=Sylvester(A,B,C)$函数求解。
![alt text](算法流程图.svg)

## **（4）离线监测过程**
用所有模态数据的最终字典$D_{fused}$对训练数据稀疏编码，得到$R_{train}=\|y_{train}-D_{fused}x\|_2^2$,再用KDE估计控制限

对于测试样本$y_{new}$，用$D_{fused}$进行稀疏编码，得到$x_{new}$,同样计算残差统计量$R_{new}=\|y_{new}-D_{fused}x_{new}\|^2_2$


# 3.初步实验
![alt text](image.png)
![](y3.svg)


参数$\lambda$实验:


**1. 字典整体变化度（NMSC）**
* **定义**：

  $$
  \mathrm{NMSC} = \frac{1}{dK} \sum_{i=1}^d \sum_{j=1}^K \frac{(D^{(\text{final})}_{ij} - D^{(1)}_{ij})^2}{(D^{(1)}_{ij})^2 + \epsilon}
  $$

  * $D^{(1)}$：初始字典
  * $D^{(\text{final})}$：最终字典
  * $\epsilon$：极小常数，防止除零
  

**2. 主空间变化度（Subspace Distance / Principal Subspace Distance）**

* **定义**：主方向空间的变化程度（如夹角/投影距离等）
* **常用指标**：

  * **夹角距离**：主空间的基向量集合（如前90%能量的SVD主空间）与初始主空间的子空间夹角

    $$
    \text{SubspaceDist} = \arccos \left( \min \left( \mathrm{svd}(U_1^\top U_2) \right) \right )
    $$

    * $U_1$：初始主空间正交基
    * $U_2$：最终主空间正交基

参数$\lambda$敏感度
最小FAR=0.0052, 对应lambda=3.70e+04
最大FDR=0.9996, 对应lambda=7.90e+04 此时 
归一化均方变化度（NMSC）: 60.1860
主空间夹角变化度: 0.1150
![alt text](image-1.png)
![alt text](image-2.png)


![](duliang.svg)
### **通过暴力网格搜索，找到最佳参数组合：**

参数设置：
```matlab
lambda_list = [1e-6, 5e-6, 1e-5, 5e-5, 1e-4, 5e-4, 1e-3, 5e-3, 1e-2, 5e-2, 1e-1, 5e-1, 1, 5, 10];  % 15个值
% n_atoms: 30到70
n_atoms_list = 30:5:70; 
% sparsity: 1,2,3
sparsity_list = [1, 2, 3];  % 3个值
```
![alt text](image-5.png)
![alt text](三种参数对FDR影响.svg)
![alt text](image-6.png)

**🚀 推荐使用最佳参数组合: λ=5.00e+00, n_atoms=30, sparsity=2
   该组合实现了 FDR=0.9996, FAR=0.0104**


---
sparsity=2为最优，这个条件确定！
接下来继续扩大范围寻找λ和n_atoms。
![alt text](image-8.png)
![alt text](image-7.png)
**🚀 推荐使用最佳参数组合: λ=1.00e-02, n_atoms=20, sparsity=2
   该组合实现了 FDR=1.0000, FAR=0.0100**
   
---
### 三套催化故障1，2，3
![alt text](image-3.png)




### 对比试验GILDL（）
模拟数值
归一化均方变化度 NMSC = 148.2854
主空间维数 r = 3
主空间最大夹角 = 0.0984 弧度 (5.64°)

平均FAR=0.0076, 平均FDR=1.0000
![alt text](image-4.png)





三套催化中故障3能用的变量 
C E G I K L O P Q R S T X Y 故障源AD AQ AY BA BD BM BS BU EO FD FH FI FQ GW HA HF IY JI JJ JL JM

{3 5 7 9 11 12 15 16 17 18 19 20 24 25 30 43 51 53 56 65 71 73 145 160 164 165 173 200 209 214 259 270 271 273}

C   → 3  
E   → 5  
G   → 7  
I   → 9  
K   → 11  
L   → 12  
O   → 15  
P   → 16  
Q   → 17  
R   → 18  
S   → 19  
T   → 20  
X   → 24  
Y   → 25  
AD  → 30  
AQ  → 43  
AY  → 51  
BA  → 53  
BD  → 56  
BM  → 65  
BS  → 71  
BU  → 73  
EO  → 145  
FD  → 160  
FH  → 164  
FI  → 165  
FQ  → 173  
GW  → 200  
HA  → 209  
HF  → 214  
IY  → 259  
JI  → 270  
JJ  → 271  
JL  → 273  
  