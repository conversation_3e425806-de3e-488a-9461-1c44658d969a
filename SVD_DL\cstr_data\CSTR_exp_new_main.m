clear;close;
%%加载数据

% load('m1d00');load('m2d00');load('m3d00');load('m4d00');
% 
% train_mode1_norm=m1d00(1:400,1:54)';
% %删除第46,50,54列
% train_mode1_norm([46,50,54],:)=[];
% train_mode2_norm=m2d00(1:400,1:54)';
% train_mode2_norm([46,50,54],:)=[];
% train_mode3_norm=m3d00(1:400,1:54)';
% train_mode3_norm([46,50,54],:)=[];
% train_mode4_norm=m4d00(1:400,1:54)';
% train_mode4_norm([46,50,54],:)=[];
% 
% train_data=[train_mode1_norm,train_mode2_norm,train_mode3_norm,train_mode4_norm];
% n_1_samples=size(train_mode1_norm,2);
% n_2_samples=size(train_mode2_norm,2);
% n_3_samples=size(train_mode3_norm,2);
% n_4_samples=size(train_mode4_norm,2);
% 
% n_samples_set={n_1_samples,n_2_samples,n_3_samples,n_4_samples};
% 
% train_samples_labels=[ones(1,n_1_samples)*1,ones(1,n_2_samples)*2,ones(1,n_3_samples)*3,ones(1,n_4_samples)*4];
% 
% Dim_orig=size(train_data,1);
% total_samples=size(train_data,2);
% %% 2. 生成训练和测试数据
% % 训练数据
% train_mode1 = generate_data(1, 200)';
% train_mode2 = generate_data(2, 200)';
% train_mode3 = generate_data(3, 200)';
% train_data = [train_mode1,train_mode2,train_mode3];
% n_1_samples=size(train_mode1,2);
% n_2_samples=size(train_mode2,2);
% n_3_samples=size(train_mode3,2);
% 
% 
% 
% Dim_orig=size(train_data,1);
% total_samples=size(train_data,2);
% n_samples_set={n_1_samples,n_2_samples,n_3_samples};
% 
% train_samples_labels=[ones(1,n_1_samples)*1,ones(1,n_2_samples)*2,ones(1,n_3_samples)*3];
% 
% % 测试数据案例1
% test_mode1 = generate_data(1, 200);
% test_mode2 = generate_data(2, 200);
% test_mode2(:,1) = test_mode2(:,1) + 0.08;
% test_case1 = [test_mode1; test_mode2];
% 
% % 测试数据案例2
% test_case2 = [];
% % 模式1数据
% test_case2 = [test_case2; generate_data(1, 200)];
% % 模式2斜坡故障
% for i = 201:400
%     x = generate_data(2, 1);
%     x(1) = x(1) + 0.002*(i-100);
%     test_case2 = [test_case2; x];
% end
% % 模式3数据
% test_case2 = [test_case2; generate_data(3, 100)];
% % 模式3阶跃故障
% test_mode3_fault = generate_data(3, 100);
% test_mode3_fault(:,1) = test_mode3_fault(:,1) + 0.08;
% test_case2 = [test_case2; test_mode3_fault];

load('CSTR_3modes_train_data.mat')
train_data=simout';

train_mode1=train_data(:,1:400);
train_mode2=train_data(:,401:800);
train_mode3=train_data(:,801:1200);
n_1_samples=size(train_mode1,2);
n_2_samples=size(train_mode2,2);
n_3_samples=size(train_mode3,2);



Dim_orig=size(train_data,1);
total_samples=size(train_data,2);
n_samples_set={n_1_samples,n_2_samples,n_3_samples};

train_samples_labels=[ones(1,n_1_samples)*1,ones(1,n_2_samples)*2,ones(1,n_3_samples)*3];




n_mode=3; %模式数

%%优化目标函数
%初始化
X=train_data;
X_all=train_data;

Dim_lower=5;
max_iter=100;



%delta=1;
%gamma=1e-5;
%beta=1e-1;
mu = 1.2e1; % 参数 mu
lambda=1e-3;
kappa=1e-2;
mu_max = 1e6; % 最大 mu
rho = 1.01; % 参数 rho
eta=1.1e-13;
epsilon = 1e-6; % 定义收敛的容差
% 初始化记录变量收敛的数组
% 初始化误差记录数组
E1_norm = zeros(max_iter, 1);


% 调用初始化函数
X_set={train_mode1;train_mode2;train_mode3};

[P_all, Q_all, Z_all, E1_all,E2_all, J_all,Y1_all,Y2_all,Y3_all,...
P_set,Q_set,Z_set,E1_set,E2_set,J_set,Y1_set,Y2_set,Y3_set] = initializeVariables(n_mode, Dim_lower, Dim_orig, total_samples, n_samples_set);
for iter=1:max_iter
    %迭代次数
    disp(['Iteration: ', num2str(iter)]);
    %update J
    for i=1:n_mode
        J_set{i} = soft_thresholding(Z_set{i} + Y3_set{i} / mu, 1 / mu);
    end
    %step 2 更新Z
    for i=1:n_mode
       % 获取当前模式的变量

        Z = Z_set{i};
        Q = Q_set{i};
        J = J_set{i};
        P = P_set{i};
        E2 = E2_set{i};
        Y2 = Y2_set{i};
        Y3 = Y3_set{i};


        % 定义中心化矩阵 H
        n = size(X_set{i}, 2);  % 假设 X 的行数为样本数 n
        I = eye(n);  % 单位矩阵
        one = ones(n, 1);  % 全1向量
        
        % 中心化矩阵 H
        H = I - (1/n) * (one * one');  % 计算 H
        K = 0;  % 初始化 K
        for t = 1:n_mode
            if t ~= i  % 排除当前工况 s
                K_t=Z_set{t}'*Z_set{t};
                K_t_centered = H * K_t * H;  % 计算中心化后的 K_t
                K = K + K_t_centered;  % 加和所有中心化后的 K_tY1
            end
        end
        
        % 计算Z的闭式解
        %term1 = mu + 2 * kappa * (P * X * Q)' * (P * X * Q) - mu * (X * Q)' * X * Q + lambda * (K' + K);
        %term2 = mu * J - Y3 + 2 * kappa * (P * X * Q)' * (P * X * Q) - mu * (X * Q)' * X * Q + mu * (X * Q)' * E2 - (X * Q)'*Y2;
        
        % 计算Z
        %Z_set{i} = (term1) \ term2;
        grad_Z=mu*(Z_set{i}-J_set{i}+Y3_set{i}/mu)-2*kappa*(P_set{i}*X*Q_set{i})'*(P_set{i}*X*Q_set{i}-P_set{i}*X*Q_set{i}*Z_set{i})...
            -mu*(X*Q_set{i})'*(X*Q_set{i}-X*Q_set{i}*Z_set{i}-E2_set{i}+Y2_set{i}/mu)...
            +lambda*Z_set{i}*K'+lambda*Z_set{i}*K;
        Z_set{i}=Z_set{i}-eta*grad_Z;
    end
    %step 3 更新P
    for i=1:n_mode
        term3=(X_all * Q_set{i})*((Z_set{i}-eye(n_samples_set{i})*(Z_set{i}-eye(n_samples_set{i}))'))*(X_all * Q_set{i})';  % 计算 term4
        [eigenvectors, eigenvalues] = eig(term3);  % 计算特征值和特征向量
        [~, idx] = sort(diag(eigenvalues),'ascend');  % 对特征值进行排序
        % 选择前 Dim_lower 个特征向量
        P_set{i} = (eigenvectors(:, idx(1:Dim_lower)))';  % 更新 P
    end
     %step 4 更新Q
    for i=1:n_mode

        % grad_Q = 2 * beta * Q_set{i} + 2 * kappa * (P_set{i} * X)' * (P_set{i} * X * Q_set{i} - P_set{i} * X * Q_set{i} * Z_set{i}) * (eye(size(Z_set{i},2)) - Z_set{i}') ...
        %      + mu * X' * (X * Q_set{i} - X * Q_set{i} * Z_set{i} - E2_set{i} + 1 / mu * Y2_set{i}) * (eye(size(Z_set{i},2)) - Z_set{i}');
        %      %+mu *X'*(X_set{i}-X*Q_set{i}-E1_set{i}+1 / mu * Y2_set{i});

             
        grad_Q = 2 * kappa * (P_set{i} * X)' * (P_set{i} * X * Q_set{i} - P_set{i} * X * Q_set{i} * Z_set{i}) * (eye(size(Z_set{i},2)) - Z_set{i}') ...
             + mu * X' * (X * Q_set{i} - X * Q_set{i} * Z_set{i} - E2_set{i} + 1 / mu * Y2_set{i}) * (eye(size(Z_set{i},2)) - Z_set{i}') ...
             +mu *X' * (X_set{i}-X*Q_set{i}-E1_set{i}+1 / mu * Y2_set{i}); 
    

             
        Q_set{i}=Q_set{i}-eta*grad_Q;
    end
    %step 5 更新E1
    for i=1:n_mode
        R1= X_set{i} - X*Q_set{i} + Y1_set{i} / mu;
        for j=1:n_samples_set{i}
            norm_R = norm(R1(:, j), 2);  % 计算R的第 j 列的 L2 范数
            if norm_R > lambda
                E1_set{i}(:, j) = (1 - lambda / norm_R) * R1(:, j);  % 更新E
            else
                E1_set{i}(:, j) = zeros(size(R1, 1), 1);  % 如果小于lambda，则置为0
            end
        end
    end

    %step 6 更新E2
    for i=1:n_mode
        R2= X*Q_set{i}- X*Q_set{i}*Z_set{i}+ Y2_set{i} / mu;
        for j=1:n_samples_set{i}
            norm_R = norm(R2(:, j), 2);  % 计算R的第 j 列的 L2 范数
            if norm_R > lambda
                E2_set{i}(:, j) = (1 - lambda / norm_R) * R2(:, j);  % 更新E
            else
                E2_set{i}(:, j) = zeros(size(R2, 1), 1);  % 如果小于lambda，则置为0
            end
        end
    end
    %step 7 Y, Pi,mu
    for i=1:n_mode
        % Y1_set{i} = Y1_set{i} + mu * (X_set{i} - X * Q_set{i}-E1_set{i});  % 更新 Y1
        Y2_set{i} = Y2_set{i} + mu * (X * Q_set{i} - X * Q_set{i}*Z_set{i}-E2_set{i});  % 更新 Y2
        Y3_set{i} = Y3_set{i} + mu * (Z_set{i}-J_set{i});  % 更新 Y3
        
    end
    mu = min(mu * rho, mu_max);  % 更新 mu


    % 收敛条件
    % 计算收敛条件中的各个项
    conver1 = norm(X*Q_set{2} - X*Q_set{2} * Z_set{2} - E2_set{2}, 'inf'); % 检查 ||XQ - XQZ - E2||_∞
    conver2 = norm(Z_set{2} - J_set{2}, 'inf');         % 检查 ||Z - J||_∞
    conver3 = norm(P_set{2} * P_set{2}' - eye(size(P_set{2}, 1)), 'fro'); % 检查 ||P'P - I||_F
    conver4 = norm(X_set{1} - X*Q_set{1}  - E1_set{1}, 'inf');
    
    % 记录收敛条件的变化
    E1_norm(iter) = conver1;
    E2_norm(iter) = conver2;
    P_norm(iter) = conver3;
    E4_norm(iter) = conver4;

    % 如果所有条件都满足收敛，则跳出循环
    if conver1 < epsilon && conver2 < epsilon && conver3 < epsilon %&& conver4 < epsilon
        disp('Convergence achieved!');
        break;  % 满足收敛条件时跳出循环
    end
end


    G_set = cell(n_mode, 1);%初始化G
    for i = 1:n_mode
        G_set{i} = rand(Dim_orig, Dim_orig);
        if i == 1
           G_all = G_set{i};
        else
            G_all = [G_all; G_set{i}];
        end
    end

    P2_set = cell(n_mode, 1);%初始化SPE用的P2
    for i = 1:n_mode
    P2_set{i} = rand(Dim_lower, Dim_orig);
        if i == 1
            P2_all = P2_set{i};
        else
            P2_all = [P2_all; P2_set{i}];
        end
    end

    for i=1:n_mode
    G_set{i}=(X_all * Q_set{i})*(kappa*(Z_set{i}-eye(n_samples_set{i})*(Z_set{i}-eye(n_samples_set{i}))'))*(X_all * Q_set{i})';
    [eigenvectors, eigenvalues] = eig(G_set{i});  % 计算特征值和特征向量
    [~, idx] = sort(diag(eigenvalues),'descend');  % 对特征值进行排序
    % 选择前 Dim_lower 个特征向量
    P2_set{i} = (eigenvectors(:, idx(1:Dim_lower)))';  % 更新 P
    end






% 绘制收敛条件的变化情况为4个子图
figure;
%调整图的大小为合适的大小
set(gcf, 'Position', [100, 100, 1200, 800]); % 设置图形窗口大小



subplot(2, 2, 1);
plot(1:iter, E1_norm(1:iter), '-', 'DisplayName', '||XQ - XQZ - E2||_\infty','LineWidth',2,'Color','#f15bb5');
xlabel('Iteration');
ylabel('value');
title('||XQ - XQZ - E2||_\infty');


subplot(2, 2, 2);
plot(1:iter, E2_norm(1:iter), '-', 'DisplayName', '||Z - J||_\infty','LineWidth',2,'Color','#9b5de5');
xlabel('Iteration');
ylabel('value');
title('||Z - J||_\infty');


subplot(2, 2, 3);
plot(1:iter, P_norm(1:iter), '-', 'DisplayName', '||P''P - I||_F','LineWidth',2,'Color','#00bbf9');
xlabel('Iteration');
ylabel('value');
title('||P''P - I||_F');


subplot(2, 2, 4);
plot(1:iter, E4_norm(1:iter), '-', 'DisplayName', '||X - XQ - E1||_\infty','LineWidth',2,'Color','#00f5d4');
xlabel('Iteration');
ylabel('value');
title('||X - XQ - E1||_\infty');

% 在每个子图下方显示参数值
param_str = sprintf(['Dim\\_lower = %d, max\\_iter = %d, mu = %.1e\n' ...
                     'lambda = %.1e, kappa = %.1e, mu\\_max = %.1e\n' ...
                     'rho = %.1e,  eta = %.1e, epsilon = %.1e'], ...
                     Dim_lower, max_iter, mu, lambda, kappa, ...
                     mu_max, rho,  eta, epsilon);
annotation('textbox', [0.1, 0.01, 0.8, 0.05], 'String', param_str, ...
           'HorizontalAlignment', 'center', 'VerticalAlignment', 'middle', ...
           'FitBoxToText', 'on', 'BackgroundColor', 'white');

% 保存每次图像 .fig格式 带编号,如果有重名，在后面加上_1, _2
% 获取当前文件名和路径，保存在“conver_fig”文件夹下
save_dir = 'conver_fig'; % 保存文件夹名称
if ~exist(save_dir, 'dir')
    mkdir(save_dir); % 创建文件夹
end

% 获取当前时间戳
timestamp = datestr(now, 'yyyy_mm_dd_HH_MM_SS');
% 创建文件名
filename = sprintf('convergence_%s.fig', timestamp);
% 保存图像到指定文件夹
save_path = fullfile(save_dir, filename);
savefig(save_path);
disp(['Figure saved to: ', save_path]);

% %绘制P_set{}中的每个矩阵的图像
% figure;
% for i=1:n_mode
%     subplot(2,2,i);
%     imagesc(P_set{i});
%     colorbar;
%     title(['P_{', num2str(i), '}']);
% end
%保存P_set{}的值
save('P_set.mat', 'P_set');
save('P2_set.mat', 'P2_set');
%保存Q_set{}的值
save('Q_set.mat', 'Q_set');
save('Z_set.mat', 'Z_set');


%% 1. 数据生成函数
function X = generate_data(mode, num_samples)
    if mode == 1
        s1 = unifrnd(-10, -7, num_samples, 1);
        s2 = normrnd(-5, 1, num_samples, 1);
    elseif mode == 2
        s1 = unifrnd(-3, -1, num_samples, 1);
        s2 = normrnd(2, 1, num_samples, 1);
    elseif mode == 3
        s1 = unifrnd(2, 5, num_samples, 1);
        s2 = normrnd(7, 1, num_samples, 1);
    end
    
    A = [0.5768, 0.3766;
         0.7382, 0.0566;
         0.8291, 0.4009;
         0.6519, 0.2070;
         0.3972, 0.8045];
    
    S = [s1, s2];
    noise = normrnd(0, 0.01, num_samples, 5);
    X = S * A' + noise;
end
    



    



