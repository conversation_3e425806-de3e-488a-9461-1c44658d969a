function [Dictionary_history, nmsc, subspace_dist] = run_continual_dictionary_learning(ablate_atom_replacement, ablate_subspace_reg)
% ----------------------------------------------------------------------
% 两个开关：
%   ablate_atom_replacement = true 表示关闭新主原子替换旧次原子
%   ablate_subspace_reg     = true 表示关闭主方向空间正则化微调
% ----------------------------------------------------------------------

rng(42);  % 固定随机种子便于复现
all_modes = 5;
n_atoms = 30; sparsity = 2; n_iter = 50;
lambda = 1e-4;

%% 1. 初始化：mode1 训练字典
load('mode1_train.mat');
Y = train_data';
D_init = randn(8, n_atoms);
for k = 1:n_atoms
    D_init(:,k) = D_init(:,k) / norm(D_init(:,k));
end
[Dictionary, ~] = ksvd_simple(Y, D_init, sparsity, n_iter);

[U, S, ~] = svd(Dictionary, 'econ');
singular_values = diag(S);
energy = cumsum(singular_values.^2) / sum(singular_values.^2);
k_locked = find(energy >= 0.9, 1, 'first');
U_locked = U(:, 1:k_locked);

Dictionary_history = cell(all_modes, 1);
U_locked_history = cell(all_modes, 1);
Dictionary_history{1} = Dictionary;
U_locked_history{1} = U_locked;

D_prev = Dictionary;
U_locked_prev = U_locked;
k_locked_prev = k_locked;

%% 2. 增量训练
for mode = 2:all_modes
    fname = sprintf('mode%d_train.mat', mode);
    load(fname); Y_new = train_data';

    % 新mode独立KSVD训练
    D_init = randn(8, n_atoms);
    for k = 1:n_atoms
        D_init(:,k) = D_init(:,k) / norm(D_init(:,k));
    end
    [D_new, ~] = ksvd_simple(Y_new, D_init, sparsity, 30);

    % 旧主原子判定
    [~, So, Vo] = svd(D_prev, 'econ');
    V_weight = abs(Vo) * diag(So);
    [~, idx] = sort(V_weight, 'descend');
    energy_cum = cumsum(V_weight(idx)) / sum(V_weight);
    k_important = find(energy_cum >= 0.9, 1, 'first');
    imp_idx_old = idx(1:k_important); unimp_idx_old = idx(k_important+1:end);

    % 新主原子判定
    [~, S_new, V_new] = svd(D_new, 'econ');
    V_weight_new = abs(V_new) * diag(S_new);
    [~, idx_new] = sort(V_weight_new, 'descend');
    energy_cum_new = cumsum(V_weight_new(idx_new)) / sum(V_weight_new);
    k_important_new = find(energy_cum_new >= 0.9, 1, 'first');
    imp_idx_new = idx_new(1:k_important_new);

    % 构建新字典
    if ablate_atom_replacement && ablate_subspace_reg
        D_fused = D_new;
    else
        D_fused = D_prev;
        if ~ablate_atom_replacement
            replace_cnt = min(numel(imp_idx_new), numel(unimp_idx_old));
            D_fused(:, unimp_idx_old(1:replace_cnt)) = D_new(:, imp_idx_new(1:replace_cnt));
        end
        if ~ablate_subspace_reg
            U_locked = U_locked_prev;
            P_locked = U_locked * U_locked';
            X = zeros(size(D_fused,2), size(Y_new,2));
            for n = 1:size(Y_new,2)
                X(:,n) = omp(D_fused, Y_new(:,n), sparsity);
            end
            A = lambda * (P_locked') * P_locked;
            B = X * X';
            C = lambda * (P_locked') * P_locked * D_prev + Y_new * X';
            D_fused = sylvester(A,B,C);
            for k = 1:size(D_fused,2)
                D_fused(:,k) = D_fused(:,k) / norm(D_fused(:,k));
            end
        end
    end

    [U_new, S_new, ~] = svd(D_fused, 'econ');
    singular_values_new = diag(S_new);
    energy_new = cumsum(singular_values_new.^2) / sum(singular_values_new.^2);
    k_locked_new = find(energy_new >= 0.9, 1, 'first');
    U_locked_new = U_new(:, 1:k_locked_new);

    Dictionary_history{mode} = D_fused;
    U_locked_history{mode} = U_locked_new;
    D_prev = D_fused;
    U_locked_prev = U_locked_new;
    k_locked_prev = k_locked_new;

    fprintf('mode%d 主原子=%d，主空间维度=%d\n', mode, k_important, k_locked_new);
end

%% 3. 变化热图与NMSC
D1 = Dictionary_history{1};
Dlast = Dictionary_history{end};
deltaD = Dlast - D1;

figure;
imagesc(deltaD); colorbar;
xlabel('字典原子编号'); ylabel('观测量编号');
title('最终字典与mode1差分热图');

epsilon = 1e-8;
num = (Dlast - D1).^2;
den = D1.^2 + epsilon;
nmsc = mean(num(:) ./ den(:));
fprintf('NMSC = %.4f\n', nmsc);

%% 4. 主空间变化度（夹角距离）
U1 = U_locked_history{1};
Ulast = U_locked_history{end};
min_dim = min(size(U1,2), size(Ulast,2));
U1 = U1(:,1:min_dim); Ulast = Ulast(:,1:min_dim);
subspace_dist = subspace(U1, Ulast); % Matlab自带subspace函数
fprintf('主空间变化度（subspace distance）: %.4f\n', subspace_dist);
end
