# GILDL方法性能分析

## 🎯 分析目标

使用num_monitoring_exp.m中的性能检测方法来评估GILDL字典学习方法的性能，包括：
1. **NMSC** (归一化均方变化度)
2. **主方向子空间夹角**
3. **FAR/FDR** (误报率/故障检测率) - **完全按照num_monitoring_exp.m实现**
4. **字典演化可视化**

## 📁 文件说明

### 核心分析文件
- `learn_D.m` - 原始GILDL脚本 ⚠️**仅供参考**⚠️
- `learn_D_GILDL.m` - GILDL字典学习函数 ⭐**实际使用**⭐
- `num_monitoring_exp.m` - 原始FAR/FDR计算实现 ⚠️**参考实现**⚠️
- `analyze_gildl_performance.m` - GILDL性能分析主脚本
- `run_gildl_analysis.m` - 一键运行脚本 ⭐**推荐使用**⭐
- `compute_far_fdr_gildl.m` - FAR/FDR计算函数 (按照num_monitoring_exp.m)
- `visualize_gildl_evolution.m` - GILDL可视化函数
- `compare_methods_performance.m` - 与SVD_DL方法比较

### 输出文件
- `gildl_performance_analysis.mat` - GILDL完整分析结果
- `methods_comparison_results.mat` - 方法比较结果

## 🚀 使用方法

### 快速开始
```matlab
% 切换到GILDL目录
cd('GILDL')

% 一键运行GILDL分析
run('run_gildl_analysis.m')
```

### 分步执行
```matlab
% 1. GILDL性能分析
run('analyze_gildl_performance.m')

% 2. 可视化结果
load('gildl_performance_analysis.mat', 'results_gildl');
visualize_gildl_evolution(results_gildl);

% 3. 与SVD_DL比较
run('compare_methods_performance.m')
```

## 📊 性能指标定义

### 1. NMSC (归一化均方变化度)
**定义**: 
```
NMSC = mean((D_new - D_old)² ./ (D_old² + ε))
其中 ε = 1e-8
```
- **含义**: 值越小表示字典越稳定
- **值域**: [0, +∞)

### 2. 主方向子空间夹角
**定义**: 
```
subspace_angle = subspace(U_old, U_new)  % 弧度
```
- **含义**: 值越小表示主方向变化越小
- **值域**: [0, π/2] 弧度

### 3. FAR/FDR (按照num_monitoring_exp.m实现)
**计算流程**:
```matlab
% 1. 拼接1~5 mode训练/测试数据
% 2. 训练数据: OMP编码 + R统计量计算
% 3. KDE估计分布 + 控制限计算 (1-α分位数)
% 4. 测试数据: OMP编码 + R统计量计算
% 5. 按模式计算FAR/FDR
```
- **FAR** (False Alarm Rate): 误报率 = 正常样本被误判为故障的比例
- **FDR** (Fault Detection Rate): 故障检测率 = 故障样本被正确检测的比例
- **数据结构**: 每模式1000样本 (前500正常 + 后500故障)

## 📈 输出结果示例

### 控制台输出
```
📊 NMSC (归一化均方变化度):
   Mode 1→2: 0.1234
   Mode 2→3: 0.0987
   Mode 3→4: 0.1156
   Mode 4→5: 0.1045
   平均值: 0.1106

📐 主方向子空间夹角 (弧度):
   Mode 1→2: 0.2618弧度 (15.00°)
   Mode 2→3: 0.3491弧度 (20.00°)
   Mode 3→4: 0.2094弧度 (12.00°)
   Mode 4→5: 0.2793弧度 (16.00°)
   平均值: 0.2749弧度 (15.75°)

🎯 性能指标 (FAR/FDR):
   Mode 1: FAR=0.0120, FDR=0.8500
   Mode 2: FAR=0.0080, FDR=0.9200
   ...
   总体性能: FAR=0.0100, FDR=0.8800
```

### 可视化图表

#### 图1: GILDL方法综合演化分析 (6个子图)
1. **NMSC变化趋势**: 各模式转换的字典变化度
2. **子空间夹角变化**: 主方向的变化程度
3. **主空间维度演化**: 主空间维度的变化
4. **FAR性能**: 各模式的误报率
5. **FDR性能**: 各模式的故障检测率
6. **方法总结**: 关键指标汇总

#### 图2: GILDL字典变化热图分析 (6个子图)
1. **差分热图**: 最终字典与初始字典的差异
2. **绝对变化热图**: 字典元素的绝对变化
3. **相对变化热图**: 字典元素的相对变化
4-6. **各模式转换热图**: 每个模式转换的详细变化

#### 图3: GILDL监测统计量分析 (2个子图)
1. **统计量序列**: 监测统计量的时间序列
2. **统计量分布**: 监测统计量的直方图分布

## 🔍 结果解读指南

### NMSC解读
- `< 0.1`: 字典变化很小，高度稳定
- `0.1-0.5`: 适度变化，平衡稳定性和适应性
- `> 0.5`: 变化较大，强适应性

### 子空间夹角解读
- `< π/6 (30°)`: 主方向变化小，高稳定性
- `π/6-π/3 (30°-60°)`: 中等变化，平衡性能
- `> π/3 (60°)`: 变化大，强适应性

### FAR/FDR解读
- **理想情况**: FAR < 0.05, FDR > 0.9
- **可接受**: FAR < 0.1, FDR > 0.8
- **需要改进**: FAR > 0.1 或 FDR < 0.8

## 🆚 与SVD_DL方法比较

### 比较维度
1. **稳定性**: 通过NMSC比较字典变化程度
2. **适应性**: 通过子空间夹角比较主方向变化
3. **检测性能**: 通过FAR/FDR比较故障检测能力
4. **维度效率**: 通过主空间维度比较计算复杂度

### 比较结果解读
```matlab
% 运行比较分析
run('compare_methods_performance.m')
```

比较结果将显示：
- 数值对比表格
- 可视化比较图表
- 综合评价和使用建议

## 📋 检查清单

运行前确保：
- ✅ `learn_D.m` 文件存在且可正常运行
- ✅ 所有mode数据文件存在 (当前目录或../SVD_DL/)
- ✅ OMP函数可用 (或使用最小二乘备用方法)
- ✅ 足够的内存空间 (建议>2GB可用)
- ✅ MATLAB版本支持 (建议R2018b或更高)

## 🚨 注意事项

### 重要提醒
1. **不要修改 `learn_D.m`**: 保持GILDL算法的原始实现
2. **参数不变**: 使用learn_D.m中的默认参数设置
3. **数据路径**: 自动检查当前目录和../SVD_DL/目录的数据文件

### 常见问题
1. **learn_D函数失败**: 会自动使用备用字典生成方法
2. **OMP函数缺失**: 会自动使用最小二乘方法
3. **数据文件缺失**: 会提示具体缺失的文件
4. **内存不足**: 建议关闭其他程序或使用更高配置

## 📈 扩展分析

### 进一步分析
1. **参数敏感性**: 分析learn_D.m中参数对性能的影响
2. **计算复杂度**: 比较GILDL和SVD_DL的计算时间
3. **鲁棒性测试**: 在不同噪声水平下测试性能
4. **实时性能**: 评估在线学习和检测的性能

### 自定义分析
可以修改以下文件进行自定义分析：
- `analyze_gildl_performance.m`: 修改性能评估方法
- `visualize_gildl_evolution.m`: 添加新的可视化内容
- `compare_methods_performance.m`: 添加新的比较维度

## 📞 故障排除

### 错误处理
1. **文件路径问题**: 确保在正确的目录中运行
2. **函数依赖**: 检查所有必要的.m文件是否存在
3. **数据格式**: 确保数据文件格式正确
4. **版本兼容**: 检查MATLAB版本兼容性

### 性能优化
1. 使用并行计算工具箱（如果可用）
2. 减少数据量进行快速测试
3. 优化内存使用
4. 使用更高效的稀疏编码方法

## 🎯 总结

GILDL方法性能分析系统提供了：
- **完整的性能评估**: NMSC、子空间夹角、FAR/FDR
- **详细的可视化**: 多角度展示字典演化过程
- **方法比较**: 与SVD_DL方法的全面对比
- **易用性**: 一键运行和详细的文档说明

通过这个分析系统，你可以全面了解GILDL方法的性能特点，并与SVD_DL方法进行客观比较。
