# 参数网格搜索实验说明

## 概述
这个实验的目标是通过网格搜索找到最佳的FDR（故障检测率）对应的三个关键参数：
- `lambda`: 主空间保护参数
- `n_atoms`: 字典原子数
- `sparsity`: 稀疏度

## 文件说明

### 1. parameter_grid_search.m (完整版本)
- **功能**: 对所有参数组合进行完整的网格搜索
- **参数范围**:
  - lambda: [1e-6, 1e-5, 1e-4, 1e-3, 1e-2, 1e-1] (6个值)
  - n_atoms: [30, 40, 50, 60, 70] (5个值)
  - sparsity: [1, 2, 3, 4] (4个值)
- **总组合数**: 6 × 5 × 4 = 120个参数组合
- **运行时间**: 预计2-4小时（取决于计算机性能）

### 2. parameter_search_quick.m (快速版本)
- **功能**: 使用较少参数组合进行快速测试
- **参数范围**:
  - lambda: [1e-4, 1e-3, 1e-2] (3个值)
  - n_atoms: [40, 50, 60] (3个值)
  - sparsity: [2, 3] (2个值)
- **总组合数**: 3 × 3 × 2 = 18个参数组合
- **运行时间**: 预计30-60分钟
- **建议**: 首次运行时使用此版本进行测试

### 3. parameter_search_robust.m (鲁棒版本) ⭐推荐⭐
- **功能**: 带完整错误处理的参数搜索
- **参数范围**:
  - lambda: [1e-5, 1e-4, 1e-3, 1e-2] (4个值)
  - n_atoms: [40, 50, 60] (3个值)
  - sparsity: [2, 3] (2个值)
- **总组合数**: 4 × 3 × 2 = 24个参数组合
- **特点**:
  - 完整的错误处理和数据验证
  - 自动跳过失败的参数组合
  - 提供详细的错误统计和成功率分析
  - 使用更鲁棒的控制限计算方法
- **建议**: 遇到数值计算问题时使用此版本

### 4. parameter_search_clean.m (细化简洁版) ⭐⭐强烈推荐⭐⭐
- **功能**: 细化参数搜索，优化输出，完整3D可视化
- **参数范围**:
  - lambda: 1e-6到10，步长5倍 (15个值)
  - n_atoms: 30到70，步长2 (21个值)
  - sparsity: [1, 2, 3] (3个值)
- **总组合数**: 15 × 21 × 3 = 945个参数组合
- **特点**:
  - 🎯 大幅细化的参数搜索范围
  - 📊 完整的3D散点图和热力图分析
  - 🚀 优化的进度显示（每20个组合显示一次）
  - 📈 帕累托前沿分析
  - 🔍 多角度3D可视化
  - ⏱️ 智能时间估计和进度百分比
  - 💾 详细的搜索统计信息
- **运行时间**: 8-15小时
- **建议**: 生产环境的精细参数搜索，获得最优结果

### 5. test_single_combination.m (调试版本)
- **功能**: 测试单个参数组合，用于调试
- **特点**: 详细的中间步骤输出，帮助定位问题

### 6. preview_parameter_range.m (参数预览)
- **功能**: 预览细化参数搜索的范围和组合数
- **特点**:
  - 显示详细的参数分布
  - 时间估计和分批运行建议
  - 参数密度分析
  - 可视化参数分布

## 使用方法

### 步骤1: 准备数据
确保以下数据文件存在于当前目录：
```
mode1_train.mat, mode1_test_normal.mat, mode1_test_fault.mat
mode2_train.mat, mode2_test_normal.mat, mode2_test_fault.mat
mode3_train.mat, mode3_test_normal.mat, mode3_test_fault.mat
mode4_train.mat, mode4_test_normal.mat, mode4_test_fault.mat
mode5_train.mat, mode5_test_normal.mat, mode5_test_fault.mat
```

### 步骤2: 运行搜索
```matlab
% 🌟 强烈推荐：细化参数搜索（最全面的结果）
run('parameter_search_clean.m')

% 📊 预览参数范围（运行前查看）
run('preview_parameter_range.m')

% 🔧 其他版本选择：
% 鲁棒版本（处理数值问题）
run('parameter_search_robust.m')

% 快速测试版本
run('parameter_search_quick.m')

% 原始完整搜索
run('parameter_grid_search.m')

% 调试单个参数组合
run('test_single_combination.m')
```

### 步骤3: 查看结果
程序会输出：
1. 最佳FDR及其对应的参数组合
2. 最小FAR及其对应的参数组合
3. 可视化图表
4. 保存的结果文件

## 输出结果

### 控制台输出
```
最佳FDR = 0.8500
对应参数组合:
  lambda = 1.00e-03
  n_atoms = 50
  sparsity = 2
  对应FAR = 0.0200

最小FAR = 0.0100
对应参数组合:
  lambda = 1.00e-02
  n_atoms = 60
  sparsity = 3
  对应FDR = 0.7800
```

### 可视化图表
1. **监测统计量图**: 显示最优FDR参数组合下的监测统计量和控制限
2. **参数影响图**: 显示各个参数对FDR的单独影响
3. **3D散点图**: 显示三个参数与FDR的关系
4. **FAR vs FDR图**: 显示所有参数组合的FAR和FDR关系

### 保存的文件
- `parameter_search_results.mat`: 完整版本的所有结果
- `parameter_search_quick_results.mat`: 快速版本的结果

## 结果分析

### 如何选择最佳参数
1. **优先考虑FDR**: 如果故障检测率更重要，选择最大FDR对应的参数
2. **平衡FAR和FDR**: 在FAR vs FDR图中寻找帕累托最优点
3. **考虑计算复杂度**: 较大的n_atoms会增加计算时间

### 参数含义
- **lambda**: 控制主空间保护强度，值越大保护越强
- **n_atoms**: 字典大小，影响表示能力和计算复杂度
- **sparsity**: 稀疏约束，影响重构精度和计算效率

## 注意事项

1. **内存使用**: 完整版本可能需要较大内存，建议先运行快速版本
2. **错误处理**: 快速版本包含try-catch错误处理，会跳过失败的参数组合
3. **随机种子**: 使用固定随机种子(42)确保结果可重现
4. **依赖函数**: 需要ksvd_simple.m和omp.m函数

## 自定义参数范围

如需修改参数范围，编辑对应文件中的以下行：
```matlab
lambda_list = [1e-6, 1e-5, 1e-4, 1e-3, 1e-2, 1e-1];
n_atoms_list = [30, 40, 50, 60, 70];
sparsity_list = [1, 2, 3, 4];
```

## 故障排除

### 常见问题
1. **数据文件不存在**: 检查.mat文件是否在当前目录
2. **内存不足**: 使用快速版本或减少参数范围
3. **函数未找到**: 确保ksvd_simple.m和omp.m在路径中
4. **计算时间过长**: 使用快速版本或减少迭代次数
5. **ksdensity错误 "X 在删除 NaN 后没有剩余数据"**:
   - 使用 `parameter_search_robust.m` 版本
   - 或者运行 `test_single_combination.m` 调试具体问题
6. **数值计算异常 (NaN/Inf)**:
   - 某些参数组合可能导致数值不稳定
   - 使用鲁棒版本会自动跳过这些组合
7. **OMP求解失败**:
   - 稀疏度设置过高或字典条件数过大
   - 鲁棒版本会使用备用方法计算统计量

### 性能优化建议
1. 使用并行计算工具箱（如果可用）
2. 减少K-SVD迭代次数
3. 使用更少的参数组合进行初步筛选
