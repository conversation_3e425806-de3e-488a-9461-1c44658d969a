%% 完整的JMSDL性能分析运行脚本
% 仿照SVD_DL方法进行完整的性能分析

fprintf('========== 开始JMSDL方法性能分析 ==========\n');
fprintf('分析目标: 使用与SVD_DL相同的性能检测方法\n');
fprintf('字典学习方法: JMSDL\n');
fprintf('性能指标: NMSC、主方向子空间夹角、FAR、FDR\n');
fprintf('FAR/FDR计算: 完全按照num_monitoring_exp.m的实现\n\n');

%% 检查必要文件
fprintf('🔍 检查必要文件...\n');

% 检查训练数据文件
required_files = {'mode1_train.mat', 'mode2_train.mat', 'mode3_train.mat', 'mode4_train.mat', 'mode5_train.mat'};
missing_files = {};

for i = 1:length(required_files)
    if ~exist(required_files{i}, 'file')
        missing_files{end+1} = required_files{i};
    end
end

if ~isempty(missing_files)
    fprintf('❌ 缺少以下训练数据文件:\n');
    for i = 1:length(missing_files)
        fprintf('   - %s\n', missing_files{i});
    end
    return;
end

% 检查测试数据文件
test_files = {};
for i = 1:5
    normal_file = sprintf('mode%d_test_normal.mat', i);
    fault_file = sprintf('mode%d_test_fault.mat', i);
    
    if ~exist(normal_file, 'file')
        % 检查SVD_DL目录
        normal_file_svd = sprintf('../SVD_DL/mode%d_test_normal.mat', i);
        if exist(normal_file_svd, 'file')
            fprintf('✓ 找到测试数据文件: %s\n', normal_file_svd);
        else
            test_files{end+1} = normal_file;
        end
    else
        fprintf('✓ 找到测试数据文件: %s\n', normal_file);
    end
    
    if ~exist(fault_file, 'file') && ~exist(sprintf('../SVD_DL/%s', fault_file), 'file')
        test_files{end+1} = fault_file;
    end
end

if ~isempty(test_files)
    fprintf('❌ 缺少以下测试数据文件:\n');
    for i = 1:length(test_files)
        fprintf('   - %s\n', test_files{i});
    end
    fprintf('请确保测试数据文件在当前目录或../SVD_DL/目录中。\n');
    return;
end

% 检查OMP函数
if ~exist('omp', 'file')
    addpath('../SVD_DL');
    if ~exist('omp', 'file')
        fprintf('⚠️  警告: 未找到OMP函数，将使用内置备用方法\n');
    else
        fprintf('✓ 找到OMP函数\n');
    end
else
    fprintf('✓ 找到OMP函数\n');
end

fprintf('✅ 文件检查完成\n\n');

%% 检查字典文件
fprintf('🔍 检查JMSDL字典文件...\n');

dict_exists = exist('D_n_num_JMSDL.mat', 'file');
history_exists = exist('JMSDL_Dictionary_history.mat', 'file');

if dict_exists && history_exists
    fprintf('   ✓ 发现已训练的字典文件和字典历史\n');
else
    if ~dict_exists
        fprintf('   ❌ 未找到字典文件: D_n_num_JMSDL.mat\n');
    end
    if ~history_exists
        fprintf('   ❌ 未找到字典历史文件: JMSDL_Dictionary_history.mat\n');
    end
    fprintf('   开始运行JMSDL训练...\n'); 

    % 运行JMSDL训练
    Copy_of_JMSDL_new2_num;

    % 重新检查文件
    if exist('D_n_num_JMSDL.mat', 'file') && exist('JMSDL_Dictionary_history.mat', 'file')
        fprintf('   ✓ JMSDL训练完成，字典文件已生成\n');
    else
        error('JMSDL训练失败，未生成必要的字典文件');
    end
end

%% 运行分析
try
    fprintf('🚀 开始执行JMSDL性能分析...\n');
    tic;

    % 执行主分析脚本
    analyze_jmsdl_performance;
    
    elapsed_time = toc;
    fprintf('\n✅ JMSDL分析完成！\n');
    fprintf('⏱️  总耗时: %.1f 分钟\n', elapsed_time/60);
    
    % 加载结果并显示关键信息
    load('jmsdl_performance_analysis.mat', 'results_jmsdl');
    
    fprintf('\n📋 JMSDL方法结果总览:\n');
    fprintf('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
    
    % NMSC结果
    if isfield(results_jmsdl, 'NMSC_history') && ~isempty(results_jmsdl.NMSC_history)
        NMSC_history = results_jmsdl.NMSC_history;
        fprintf('📊 NMSC (归一化均方变化度):\n');
        for i = 1:length(NMSC_history)
            fprintf('   Mode %d→%d: %.4f\n', i, i+1, NMSC_history(i));
        end
        fprintf('   平均值: %.4f\n', mean(NMSC_history));
    end
    
    % 子空间夹角结果
    if isfield(results_jmsdl, 'Subspace_angle_history') && ~isempty(results_jmsdl.Subspace_angle_history)
        Subspace_angle_history = results_jmsdl.Subspace_angle_history;
        valid_angles = Subspace_angle_history(~isnan(Subspace_angle_history));
        fprintf('\n📐 主方向子空间夹角 (弧度):\n');
        for i = 1:length(Subspace_angle_history)
            if ~isnan(Subspace_angle_history(i))
                fprintf('   Mode %d→%d: %.4f弧度 (%.2f°)\n', i, i+1, ...
                        Subspace_angle_history(i), Subspace_angle_history(i)*180/pi);
            else
                fprintf('   Mode %d→%d: 无法计算\n', i, i+1);
            end
        end
        if ~isempty(valid_angles)
            fprintf('   平均值: %.4f弧度 (%.2f°)\n', mean(valid_angles), mean(valid_angles)*180/pi);
        end
    end
    
    % 主空间维度
    if isfield(results_jmsdl, 'subspace_dims') && ~isempty(results_jmsdl.subspace_dims)
        subspace_dims = results_jmsdl.subspace_dims;
        fprintf('\n🔍 主空间维度演化:\n');
        for i = 1:length(subspace_dims)
            fprintf('   Mode %d: %d维\n', i, subspace_dims(i));
        end
    end
    
    % 性能指标
    if isfield(results_jmsdl, 'FAR_overall') && isfield(results_jmsdl, 'FDR_overall')
        FAR_overall = results_jmsdl.FAR_overall;
        FDR_overall = results_jmsdl.FDR_overall;
        
        if isfield(results_jmsdl, 'FAR_vec') && isfield(results_jmsdl, 'FDR_vec')
            FAR_vec = results_jmsdl.FAR_vec;
            FDR_vec = results_jmsdl.FDR_vec;
            
            fprintf('\n🎯 性能指标 (FAR/FDR):\n');
            for i = 1:min(5, length(FAR_vec))
                if ~isnan(FAR_vec(i)) && ~isnan(FDR_vec(i))
                    fprintf('   Mode %d: FAR=%.4f, FDR=%.4f\n', i, FAR_vec(i), FDR_vec(i));
                end
            end
        end
        
        if ~isnan(FAR_overall) && ~isnan(FDR_overall)
            fprintf('   总体性能: FAR=%.4f, FDR=%.4f\n', FAR_overall, FDR_overall);
        end
    else
        fprintf('\n🎯 性能指标: 计算失败或未完成\n');
    end
    
    fprintf('\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
    
    % 结果解释
    fprintf('\n💡 JMSDL方法结果解释:\n');
    
    % NMSC解释
    if exist('NMSC_history', 'var') && ~isempty(NMSC_history)
        avg_nmsc = mean(NMSC_history);
        fprintf('🔸 NMSC平均值 %.4f 说明 (归一化均方变化度):\n', avg_nmsc);
        if avg_nmsc < 0.1
            fprintf('   字典在模式转换中变化很小，保持了高度稳定性\n');
            fprintf('   JMSDL方法能够很好地保持已学习的特征\n');
        elseif avg_nmsc < 0.5
            fprintf('   字典在模式转换中有适度的变化\n');
            fprintf('   JMSDL方法在稳定性和适应性之间取得了平衡\n');
        else
            fprintf('   字典在模式转换中变化较大\n');
            fprintf('   JMSDL方法具有强适应性，能够学习新的特征\n');
        end
    end
    
    % 子空间夹角解释
    if exist('valid_angles', 'var') && ~isempty(valid_angles)
        avg_angle = mean(valid_angles);
        avg_angle_deg = avg_angle * 180 / pi;
        fprintf('\n🔸 子空间夹角平均值 %.4f弧度 (%.2f°) 说明:\n', avg_angle, avg_angle_deg);
        if avg_angle < pi/6  % 30度
            fprintf('   主方向在模式转换中变化较小，保持了良好的稳定性\n');
            fprintf('   JMSDL方法有利于保持监测的一致性\n');
        elseif avg_angle < pi/3  % 60度
            fprintf('   主方向在模式转换中有中等程度的变化\n');
            fprintf('   JMSDL方法在稳定性和适应性之间取得了平衡\n');
        else
            fprintf('   主方向在模式转换中变化较大\n');
            fprintf('   JMSDL方法能够适应不同模式的主要特征\n');
        end
    end
    
    % 性能指标解释
    if exist('FAR_overall', 'var') && exist('FDR_overall', 'var')
        fprintf('\n🔸 性能指标 FAR=%.4f, FDR=%.4f 说明:\n', FAR_overall, FDR_overall);
        if FDR_overall > 0.9
            fprintf('   故障检测率很高，JMSDL方法能够有效检测故障\n');
        elseif FDR_overall > 0.7
            fprintf('   故障检测率良好，JMSDL方法具有较好的检测能力\n');
        else
            fprintf('   故障检测率有待提高，可能需要优化JMSDL参数\n');
        end
        
        if FAR_overall < 0.05
            fprintf('   误报率很低，JMSDL方法具有良好的稳定性\n');
        elseif FAR_overall < 0.1
            fprintf('   误报率较低，JMSDL方法稳定性良好\n');
        else
            fprintf('   误报率偏高，可能需要调整检测阈值\n');
        end
    end
    
    % 生成可视化
    fprintf('\n📊 生成可视化分析...\n');
    visualize_jmsdl_evolution(results_jmsdl);
    
    fprintf('\n📁 输出文件:\n');
    fprintf('   - jmsdl_performance_analysis.mat: 完整分析结果\n');
    fprintf('   - D_n_num_JMSDL.mat: 最终训练字典\n');
    fprintf('   - 多个可视化图表窗口已打开\n');
    
    fprintf('\n🎉 JMSDL方法性能分析完成！\n');
    fprintf('📊 请查看生成的图表以获得更详细的信息。\n');
    
    % 与其他方法的比较提示
    fprintf('\n💡 提示: 如需与SVD_DL和GILDL方法比较，可运行:\n');
    fprintf('   compare_all_methods_performance.m\n');
    
catch ME
    fprintf('\n❌ JMSDL分析过程中出现错误:\n');
    fprintf('错误信息: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    end
    fprintf('\n请检查:\n');
    fprintf('1. 所有训练和测试数据文件是否存在\n');
    fprintf('2. JMSDL算法是否正常工作\n');
    fprintf('3. MATLAB版本是否支持所用函数\n');
    fprintf('4. 当前目录权限是否允许读写文件\n');
end
