%% DMCDL催化数据集监测性能分析
% 基于compute_dictionary_dmcdl_cuihua.m的监测性能评估

clc; clear; close all;
rng(42);

fprintf('========== DMCDL催化数据集监测性能分析 ==========\n');

%% 1. 加载DMCDL催化数据集模型
fprintf('1. 加载DMCDL催化数据集模型...\n');

if exist('dmcdl_cuihua_model.mat', 'file')
    load('dmcdl_cuihua_model.mat');
    D_K = D_final_cuihua;
    Rtr_model = Rtr_final_cuihua;
    fprintf('   ✓ 成功加载DMCDL催化数据集模型\n');
    fprintf('   字典大小: %dx%d\n', size(D_K, 1), size(D_K, 2));
    fprintf('   模型阈值: %.6f\n', Rtr_model);
else
    fprintf('   ❌ 未找到dmcdl_cuihua_model.mat，开始训练...\n');
    compute_dictionary_dmcdl_cuihua;
    load('dmcdl_cuihua_model.mat');
    D_K = D_final_cuihua;
    Rtr_model = Rtr_final_cuihua;
    fprintf('   ✓ DMCDL训练完成，字典大小: %dx%d\n', size(D_K, 1), size(D_K, 2));
end

sparsity = 3;  % 与训练时保持一致

%% 2. 加载催化数据集训练和测试数据
fprintf('\n2. 加载催化数据集...\n');

Y_train = [];
Y_test = [];

% 加载训练数据 (data_selected_F1~F3)
fprintf('   加载训练数据:\n');
for mode = 1:3
    data_file = sprintf('data_selected_F%d.mat', mode);
    if exist(data_file, 'file')
        load(data_file, 'data_selected');
        Y_train = [Y_train; data_selected];
        fprintf('     F%d训练数据: %d样本\n', mode, length(data_selected));
    else
        fprintf('     ❌ 未找到%s\n', data_file);
    end
end

% 加载测试数据 (test_data_F1~F3)
fprintf('   加载测试数据:\n');
for mode = 1:3
    test_file = sprintf('test_data_F%d.mat', mode);
    if exist(test_file, 'file')
        load(test_file, 'test_data');
        Y_test = [Y_test; test_data];
        fprintf('     F%d测试数据: %d样本\n', mode, length(test_data));
    else
        fprintf('     ❌ 未找到%s\n', test_file);
    end
end

% 数据格式转换
Y_train = Y_train';          % [特征维度, 总样本数]
Y_test = Y_test';

Y_train = cell2mat(Y_train);
Y_test = cell2mat(Y_test);

fprintf('   总训练数据: %dx%d\n', size(Y_train, 1), size(Y_train, 2));
fprintf('   总测试数据: %dx%d\n', size(Y_test, 1), size(Y_test, 2));

%% 3. 训练数据R统计量计算
fprintf('\n3. 计算训练数据R统计量...\n');

R_train = zeros(1, size(Y_train, 2));
for i = 1:size(Y_train, 2)
    y = Y_train(:, i);
    x = omp(D_K, y, sparsity);
    R_train(i) = norm(y - D_K*x, 2)^2;
end

% 使用KDE估计控制限
alpha = 0.01;  % 99%置信度
try
    [f_R, xi_R] = ksdensity(R_train, 'Function', 'cdf');
    idx_R = find(f_R >= 1 - alpha, 1, 'first');
    R_limit = xi_R(idx_R);
catch
    % 如果KDE失败，使用分位数方法
    R_limit = quantile(R_train, 1 - alpha);
    fprintf('   警告: KDE失败，使用分位数方法\n');
end

fprintf('   训练样本数: %d\n', length(R_train));
fprintf('   R统计量范围: [%.6f, %.6f]\n', min(R_train), max(R_train));
fprintf('   控制限 (99%%置信度): %.6f\n', R_limit);
fprintf('   模型阈值: %.6f\n', Rtr_model);

%% 4. 测试数据R统计量计算
fprintf('\n4. 计算测试数据R统计量...\n');

R_test = zeros(1, size(Y_test, 2));
for i = 1:size(Y_test, 2)
    y = Y_test(:, i);
    x = omp(D_K, y, sparsity);
    R_test(i) = norm(y - D_K*x, 2)^2;
end

fprintf('   测试样本数: %d\n', length(R_test));
fprintf('   R统计量范围: [%.6f, %.6f]\n', min(R_test), max(R_test));

%% 5. 可视化监测结果
fprintf('\n5. 生成监测图表...\n');

figure('Position', [100, 100, 1200, 800]);

% 主监测图
subplot(2,2,[1,2]);
plot(R_test, 'b-', 'LineWidth', 1);
hold on;
yline(R_limit, '--r', 'LineWidth', 2, 'DisplayName', '统计控制限');
yline(Rtr_model, '--m', 'LineWidth', 2, 'DisplayName', 'DMCDL模型阈值');

% 标记不同数据集的分界线
n_each = length(R_test) / 3;
xline(n_each, '--g', 'F1|F2', 'LineWidth', 1.5);
xline(2*n_each, '--g', 'F2|F3', 'LineWidth', 1.5);

xlabel('样本编号', 'FontSize', 12);
ylabel('R统计量', 'FontSize', 12);
title('DMCDL催化数据集监测 - R统计量与控制限', 'FontSize', 14);
legend('测试样本', '统计控制限 (99%)', 'DMCDL模型阈值', 'Location', 'best');
grid on;

% 训练数据分布
subplot(2,2,3);
histogram(R_train, 50, 'Normalization', 'pdf', 'FaceAlpha', 0.7);
hold on;
xline(R_limit, '--r', 'LineWidth', 2);
xline(Rtr_model, '--m', 'LineWidth', 2);
xlabel('R统计量', 'FontSize', 12);
ylabel('概率密度', 'FontSize', 12);
title('训练数据R统计量分布', 'FontSize', 12);
legend('训练数据', '统计控制限', 'DMCDL阈值', 'Location', 'best');
grid on;

% 测试数据分布
subplot(2,2,4);
histogram(R_test, 50, 'Normalization', 'pdf', 'FaceAlpha', 0.7);
hold on;
xline(R_limit, '--r', 'LineWidth', 2);
xline(Rtr_model, '--m', 'LineWidth', 2);
xlabel('R统计量', 'FontSize', 12);
ylabel('概率密度', 'FontSize', 12);
title('测试数据R统计量分布', 'FontSize', 12);
legend('测试数据', '统计控制限', 'DMCDL阈值', 'Location', 'best');
grid on;

sgtitle('DMCDL方法 - 催化数据集过程监测', 'FontSize', 16);

% 保存监测图
savefig('dmcdl_cuihua_monitoring.fig');
fprintf('   监测图已保存到: dmcdl_cuihua_monitoring.fig\n');

%% 6. 计算FAR和FDR (使用两种阈值)
fprintf('\n6. 计算检测性能指标...\n');

% 假设每个数据集的测试数据中，前半部分为正常，后半部分为故障
n_datasets = 3;
n_each = length(R_test) / n_datasets;
n_normal_each = floor(n_each / 2);
n_fault_each = n_each - n_normal_each;

% 使用统计控制限
FAR_stat = zeros(n_datasets, 1);
FDR_stat = zeros(n_datasets, 1);

% 使用DMCDL模型阈值
FAR_dmcdl = zeros(n_datasets, 1);
FDR_dmcdl = zeros(n_datasets, 1);

for d = 1:n_datasets
    idx_start = (d-1)*n_each + 1;
    idx_normal = idx_start : idx_start + n_normal_each - 1;
    idx_fault = idx_start + n_normal_each : idx_start + n_each - 1;
    
    % 统计控制限
    FAR_stat(d) = sum(R_test(idx_normal) > R_limit) / n_normal_each;
    FDR_stat(d) = sum(R_test(idx_fault) > R_limit) / n_fault_each;
    
    % DMCDL模型阈值
    FAR_dmcdl(d) = sum(R_test(idx_normal) > Rtr_model) / n_normal_each;
    FDR_dmcdl(d) = sum(R_test(idx_fault) > Rtr_model) / n_fault_each;
    
    fprintf('   F%d数据集: 正常样本%d个, 故障样本%d个\n', d, n_normal_each, n_fault_each);
    fprintf('   F%d统计控制限: FAR=%.4f, FDR=%.4f\n', d, FAR_stat(d), FDR_stat(d));
    fprintf('   F%dDMCDL阈值: FAR=%.4f, FDR=%.4f\n', d, FAR_dmcdl(d), FDR_dmcdl(d));
end

% 总体性能
FAR_stat_overall = mean(FAR_stat);
FDR_stat_overall = mean(FDR_stat);
FAR_dmcdl_overall = mean(FAR_dmcdl);
FDR_dmcdl_overall = mean(FDR_dmcdl);

%% 7. 保存监测结果
fprintf('\n7. 保存监测结果...\n');

dmcdl_cuihua_monitoring_results = struct();
dmcdl_cuihua_monitoring_results.method = 'DMCDL_Cuihua';
dmcdl_cuihua_monitoring_results.dictionary_size = size(D_K);
dmcdl_cuihua_monitoring_results.sparsity = sparsity;
dmcdl_cuihua_monitoring_results.R_limit_stat = R_limit;
dmcdl_cuihua_monitoring_results.R_limit_dmcdl = Rtr_model;
dmcdl_cuihua_monitoring_results.R_train = R_train;
dmcdl_cuihua_monitoring_results.R_test = R_test;
dmcdl_cuihua_monitoring_results.FAR_stat = FAR_stat_overall;
dmcdl_cuihua_monitoring_results.FDR_stat = FDR_stat_overall;
dmcdl_cuihua_monitoring_results.FAR_dmcdl = FAR_dmcdl_overall;
dmcdl_cuihua_monitoring_results.FDR_dmcdl = FDR_dmcdl_overall;
dmcdl_cuihua_monitoring_results.alpha = alpha;

save('dmcdl_cuihua_monitoring_results.mat', 'dmcdl_cuihua_monitoring_results');
fprintf('   监测结果已保存到: dmcdl_cuihua_monitoring_results.mat\n');

%% 8. 结果总结
fprintf('\n========== DMCDL催化数据集监测结果总结 ==========\n');
fprintf('📊 基本信息:\n');
fprintf('   方法: DMCDL (催化数据集)\n');
fprintf('   数据集: 催化数据 (F1, F2, F3)\n');
fprintf('   字典大小: %dx%d\n', size(D_K));
fprintf('   稀疏度: %d\n', sparsity);

fprintf('\n📈 阈值比较:\n');
fprintf('   统计控制限: %.6f\n', R_limit);
fprintf('   DMCDL模型阈值: %.6f\n', Rtr_model);

fprintf('\n🎯 检测性能比较:\n');
fprintf('   方法           FAR      FDR\n');
fprintf('   -----------   ------   ------\n');
fprintf('   统计控制限    %.4f   %.4f\n', FAR_stat_overall, FDR_stat_overall);
fprintf('   DMCDL阈值     %.4f   %.4f\n', FAR_dmcdl_overall, FDR_dmcdl_overall);

% 性能评估
fprintf('\n⭐ 性能评估:\n');
if FAR_stat_overall < 0.05 && FDR_stat_overall > 0.8
    fprintf('   统计控制限: 优秀 (FAR=%.4f, FDR=%.4f)\n', FAR_stat_overall, FDR_stat_overall);
elseif FAR_stat_overall < 0.1 && FDR_stat_overall > 0.7
    fprintf('   统计控制限: 良好 (FAR=%.4f, FDR=%.4f)\n', FAR_stat_overall, FDR_stat_overall);
else
    fprintf('   统计控制限: 需改进 (FAR=%.4f, FDR=%.4f)\n', FAR_stat_overall, FDR_stat_overall);
end

if FAR_dmcdl_overall < 0.05 && FDR_dmcdl_overall > 0.8
    fprintf('   DMCDL阈值: 优秀 (FAR=%.4f, FDR=%.4f)\n', FAR_dmcdl_overall, FDR_dmcdl_overall);
elseif FAR_dmcdl_overall < 0.1 && FDR_dmcdl_overall > 0.7
    fprintf('   DMCDL阈值: 良好 (FAR=%.4f, FDR=%.4f)\n', FAR_dmcdl_overall, FDR_dmcdl_overall);
else
    fprintf('   DMCDL阈值: 需改进 (FAR=%.4f, FDR=%.4f)\n', FAR_dmcdl_overall, FDR_dmcdl_overall);
end

fprintf('\n🎉 DMCDL催化数据集监测分析完成！\n');

%% 辅助函数
function X = omp(D, Y, T)
    [~,N] = size(Y);  
    l = size(D,2);  
    X = zeros(l,N);
    
    for j = 1:N
        r = Y(:,j);  
        S = [];
        for t = 1:T
            if norm(r) < 1e-10, break; end
            
            proj = abs(D' * r);
            [~,k] = max(proj);  
            S = unique([S k]);
            
            if length(S) <= size(D,1)
                a = pinv(D(:,S)) * Y(:,j);
                r = Y(:,j) - D(:,S) * a;
            else
                break;
            end
        end
        if ~isempty(S) && exist('a', 'var')
            X(S,j) = a;
        end
    end
end
