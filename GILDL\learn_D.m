%======================================================================
%  GI‑LDL 主脚本  (按论文最严谨实现)
%  1) mode1                : K‑SVD 训练 (列级 SVD)
%  2) mode2…S (持续学习)   : OMP + 闭式整体更新 (无列级 SVD)
%  3) 递归累加 Ws = Ws + λ*diag(w_tilde)
%  4) 指标: NMSC, 主空间最大夹角
%----------------------------------------------------------------------
%  依赖: omp.m, ksvd_simple.m, 数据文件 data_selected_F*.mat
%======================================================================

clc; clear; close all;
rng(42);                                   % 复现实验

%% -------------------------- 参数设置 -------------------------------
all_modes      = 3;                        % 模式数量 (F1,F2,...,F3)
n_atoms        = 20;                       % 字典原子数
sparsity       = 2;                        % OMP 稀疏度
iter_KSVD      = 50;                       % Mode1 K‑SVD 迭代次数
iter_gildl     = 30;                       % 持续学习交替迭代次数
lambdaProtect  = 1e-6;                      % 主空间保护 λ (可调)
eps_norm       = 1e-6;                     % 防零除

Dictionary_history = cell(all_modes,1);    % 保存每个 mode 的字典
Ws = zeros(n_atoms);                       % 初始权重矩阵 W1 = 0
% ---------------------------------------------------------------------

%% ============ Mode 1 : 经典 K‑SVD 训练初始字典 & 计算 W1 =============
fprintf('\n=== Mode‑1:  K‑SVD 训练初始字典 & 计算 W1 ===\n');

load('mode1_train.mat');              % 变量 train_data
Y1 = train_data';             % [m × N]  (m=观测维)

[m,~] = size(Y1);

% 随机初始化字典
D0 = randn(m, n_atoms);  D0 = D0 ./ vecnorm(D0);

% K‑SVD (含列级 SVD 更新)
[Dict1, ~] = ksvd_simple(Y1, D0, sparsity, iter_KSVD);
Dictionary_history{1} = Dict1;

% -------- 计算 W1 ---------------------------------------------------
% 真实实现应在 K‑SVD 内累加 ω_i；这里用"单位贡献"近似
zeta = 1e-2;                          % 论文 ζ 参数
omega_1  = ones(n_atoms,1);           % 简洁做法：假设均等贡献
w_tilde1 = omega_1 ./ (vecnorm(Dict1-D0).^2 + zeta);
Ws = lambdaProtect * diag(w_tilde1);  % W1
fprintf('W1 已计算完成 (diag 最大值 %.2e)\n',max(diag(Ws)));

% 保存前一轮变量
D_prev = Dict1;

%% ============ Mode 2…S :  Lifelong Dictionary Learning ============
for s = 2:all_modes
    fprintf('\n=== Mode‑%d: Lifelong 更新 ===\n',s);
    
    % -------- 读取新模式数据 --------
    load(['mode',num2str(s),'_train.mat']);
    Y_new = train_data';       % 当前模式样本
    [m, N] = size(Y_new);
    
    % -------- 初始化 ----------
    D = D_prev;                             % 当前可变字典
    omega_s = zeros(n_atoms,1);             % ΔL 累加器
    
    for it = 1:iter_gildl
        % ==== 1. OMP 编码 ===========================================
        X = zeros(n_atoms, N);
        for j = 1:N
            X(:,j) = omp(D, Y_new(:,j), sparsity);
        end
        
        % ==== 2. 闭式整体更新 (式 24) ===============================
        D_old = D;                          % 保存旧字典用于 ΔL
        D = (D_prev*Ws + Y_new*X') / ...
             (X*X' + Ws + eps_norm*eye(n_atoms));
        D = D ./ vecnorm(D);                % 列归一化
        
        % ==== 3. 整体损失差 ΔL (简化版) =============================
        L_before = norm(Y_new - D_old*X,'fro')^2;
        L_after  = norm(Y_new - D    *X,'fro')^2;
        deltaL   = L_before - L_after;
        omega_s  = omega_s + deltaL;        % 均匀加到每原子
    end
    
    % -------- 4. 计算 w_tilde (公式 13) -----------------------------
    w_tilde = omega_s ./ (vecnorm(D - D_prev).^2 + eps_norm);
    
    % -------- 5. 权重递归 Ws = Ws + λ·diag(w_tilde) ---------------
    Ws = Ws + lambdaProtect * diag(w_tilde);
    
    % -------- 6. 保存并准备下轮 ------------------------------------
    Dictionary_history{s} = D;
    D_prev  = D;
end

%% ===================== 可视化字典变化 =============================
D_first = Dictionary_history{1};
D_last  = Dictionary_history{end};
save('Dlast_num.mat',"D_last");
deltaD  = D_last - D_first;

figure; imagesc(deltaD); colorbar;
xlabel('字典原子'); ylabel('观测维');
title('最终字典 − Mode1 字典 (差分)');
set(gca,'FontSize',12);

figure; imagesc(abs(deltaD)); colorbar;
xlabel('字典原子'); ylabel('观测维');
title('字典原子绝对变化热图');
set(gca,'FontSize',12);

%% ====================== NMSC 指标 ================================
nmsc = mean( (deltaD(:).^2) ./ (D_first(:).^2 + eps_norm) );
fprintf('\n归一化均方变化度 NMSC = %.4f\n', nmsc);

%% =================== 主空间最大夹角 ==============================
[U1,S1,~] = svd(D_first,'econ');
[U2,~,~]  = svd(D_last ,'econ');

% 取能量覆盖 ≥90% 的主空间维 r
energy_ratio = cumsum(diag(S1).^2)/sum(diag(S1).^2);
r = find(energy_ratio >= 0.9,1,'first');

U1r = U1(:,1:r);  U2r = U2(:,1:r);
s = svd(U1r' * U2r);
max_angle_rad = max( acos( min(max(s,-1),1) ) );

fprintf('主空间维数 r = %d\n', r);
fprintf('主空间最大夹角 = %.4f 弧度 (%.2f°)\n', ...
        max_angle_rad, rad2deg(max_angle_rad));
