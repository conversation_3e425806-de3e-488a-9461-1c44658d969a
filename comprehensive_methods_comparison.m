%% 三种字典学习方法全面对比分析
% GILDL vs DMCDL vs SVD-DL 综合性能评估

clc; clear; close all;
rng(42);

fprintf('========== 三种字典学习方法全面对比分析 ==========\n');
fprintf('方法: GILDL, DMCDL, SVD-DL\n');
fprintf('评估维度: 字典演化, 监测性能, 计算效率\n\n');

%% 1. 加载各方法结果
fprintf('🔍 加载各方法分析结果...\n');

methods_data = struct();
methods_names = {'GILDL', 'DMCDL', 'SVD_DL'};
methods_colors = {[0.2, 0.6, 0.8], [0.8, 0.4, 0.2], [0.4, 0.8, 0.3]};

%% 1.1 加载GILDL结果
fprintf('   加载GILDL结果...\n');
try
    if exist('GILDL/optimal_parameter_analysis.mat', 'file')
        load('GILDL/optimal_parameter_analysis.mat');
        methods_data.GILDL = results_gildl;  % 或对应的变量名
        fprintf('     ✓ GILDL最优参数分析结果已加载\n');
    else
        fprintf('     ❌ GILDL结果文件未找到\n');
        methods_data.GILDL = [];
    end
catch ME
    fprintf('     ❌ GILDL结果加载失败: %s\n', ME.message);
    methods_data.GILDL = [];
end

%% 1.2 加载DMCDL结果
fprintf('   加载DMCDL结果...\n');
try
    if exist('dmcdl_comprehensive_results.mat', 'file')
        load('dmcdl_comprehensive_results.mat');
        methods_data.DMCDL = comprehensive_results;
        fprintf('     ✓ DMCDL综合结果已加载\n');
    elseif exist('dmcdl_analysis_results.mat', 'file')
        load('dmcdl_analysis_results.mat');
        methods_data.DMCDL = dmcdl_results;
        fprintf('     ✓ DMCDL分析结果已加载\n');
    elseif exist('dmcdl_cuihua_results.mat', 'file')
        load('dmcdl_cuihua_results.mat');
        methods_data.DMCDL = dmcdl_cuihua_results;
        fprintf('     ✓ DMCDL催化数据结果已加载\n');
    else
        fprintf('     ❌ DMCDL结果文件未找到\n');
        methods_data.DMCDL = [];
    end
catch ME
    fprintf('     ❌ DMCDL结果加载失败: %s\n', ME.message);
    methods_data.DMCDL = [];
end

%% 1.3 加载SVD-DL结果
fprintf('   加载SVD-DL结果...\n');
try
    if exist('SVD_DL-optimal_parameter_analysis.mat', 'file')
        load('SVD_DL-optimal_parameter_analysis.mat');
        methods_data.SVD_DL = results_optimal;  % 或对应的变量名
        fprintf('     ✓ SVD-DL最优参数分析结果已加载\n');
    else
        fprintf('     ❌ SVD-DL结果文件未找到\n');
        methods_data.SVD_DL = [];
    end
catch ME
    fprintf('     ❌ SVD-DL结果加载失败: %s\n', ME.message);
    methods_data.SVD_DL = [];
end

%% 2. 提取关键性能指标...\n
for i = 1:length(methods_namesresults_)  % 或对应的变量名
    method = methods_names{i};
    data = methods_data.(method);

    if isempty(data)
        fprintf('   ⚠️  %s数据缺失，跳过\n', method);
        continue;
    end

    fprintf('   处理%s数据...\n', method);

    % 提取字典演化指标
    try
        if isfield(data, 'learning_results') && isfield(data.learning_results, 'Dictionary_history')
            dict_history = data.learning_results.Dictionary_history;
        elseif isfield(data, 'Dictionary_history')
            dict_history = data.Dictionary_history;
        else
            dict_history = [];
        end

        if ~isempty(dict_history)
            % 计算NMSC
            nmsc_values = calculate_nmsc(dict_history);
            comparison_metrics.(method).NMSC_mean = mean(nmsc_values);
            comparison_metrics.(method).NMSC_std = std(nmsc_values);
            comparison_metrics.(method).NMSC_values = nmsc_values;

            % 字典大小
            comparison_metrics.(method).dict_size = size(dict_history{1});
            comparison_metrics.(method).n_modes = length(dict_history);
        else
            comparison_metrics.(method).NMSC_mean = NaN;
            comparison_metrics.(method).NMSC_std = NaN;
            comparison_metrics.(method).dict_size = [NaN, NaN];
            comparison_metrics.(method).n_modes = NaN;
        end

        % 提取监测性能指标
        if isfield(data, 'monitoring_results')
            mon_data = data.monitoring_results;
            if isfield(mon_data, 'FAR_overall')
                comparison_metrics.(method).FAR = mon_data.FAR_overall;
            elseif isfield(mon_data, 'FAR')
                comparison_metrics.(method).FAR = mon_data.FAR;
            else
                comparison_metrics.(method).FAR = NaN;
            end

            if isfield(mon_data, 'FDR_overall')
                comparison_metrics.(method).FDR = mon_data.FDR_overall;
            elseif isfield(mon_data, 'FDR')
                comparison_metrics.(method).FDR = mon_data.FDR;
            else
                comparison_metrics.(method).FDR = NaN;
            end
        else
            comparison_metrics.(method).FAR = NaN;
            comparison_metrics.(method).FDR = NaN;
        end

        % 提取计算时间
        if isfield(data, 'analysis_time')
            if isfield(data.analysis_time, 'total_time')
                comparison_metrics.(method).total_time = data.analysis_time.total_time;
            elseif isfield(data.analysis_time, 'learning_time')
                comparison_metrics.(method).total_time = data.analysis_time.learning_time;
            else
                comparison_metrics.(method).total_time = NaN;
            end
        else
            comparison_metrics.(method).total_time = NaN;
        end

        fprintf('     ✓ %s指标提取完成\n', method);

    catch ME
        fprintf('     ❌ %s指标提取失败: %s\n', method, ME.message);
        comparison_metrics.(method) = struct('NMSC_mean', NaN, 'FAR', NaN, 'FDR', NaN, 'total_time', NaN);
    end
end

%% 3. 生成对比报告
fprintf('\n📄 生成综合对比报告...\n');

fprintf('\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
fprintf('📊 三种字典学习方法综合对比报告\n');
fprintf('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');

%% 3.1 基本信息对比
fprintf('\n🔧 基本配置对比:\n');
fprintf('   方法        字典大小    模式数    算法特色\n');
fprintf('   --------   --------   ------   ------------------\n');

for i = 1:length(methods_names)
    method = methods_names{i};
    if isfield(comparison_metrics, method)
        metrics = comparison_metrics.(method);
        dict_size_str = sprintf('%dx%d', metrics.dict_size(1), metrics.dict_size(2));

        % 算法特色描述
        switch method
            case 'GILDL'
                feature = '增量学习+权重保护';
            case 'DMCDL'
                feature = '双重记忆机制';
            case 'SVD_DL'
                feature = '主成分分析';
            otherwise
                feature = '未知';
        end

        fprintf('   %-8s   %-8s   %-6d   %s\n', method, dict_size_str, metrics.n_modes, feature);
    else
        fprintf('   %-8s   %-8s   %-6s   %s\n', method, 'N/A', 'N/A', '数据缺失');
    end
end

%% 3.2 字典稳定性对比
fprintf('\n📈 字典稳定性对比 (NMSC指标):\n');
fprintf('   方法        平均NMSC    标准差     稳定性评级\n');
fprintf('   --------   ---------   -------   ------------\n');

for i = 1:length(methods_names)
    method = methods_names{i};
    if isfield(comparison_metrics, method) && ~isnan(comparison_metrics.(method).NMSC_mean)
        metrics = comparison_metrics.(method);
        nmsc_mean = metrics.NMSC_mean;
        nmsc_std = metrics.NMSC_std;

        % 稳定性评级
        if nmsc_mean < 0.1
            stability = '优秀';
        elseif nmsc_mean < 0.5
            stability = '良好';
        elseif nmsc_mean < 2.0
            stability = '一般';
        else
            stability = '较差';
        end

        fprintf('   %-8s   %9.6f   %7.4f   %s\n', method, nmsc_mean, nmsc_std, stability);
    else
        fprintf('   %-8s   %9s   %7s   %s\n', method, 'N/A', 'N/A', '数据缺失');
    end
end

%% 3.3 监测性能对比
fprintf('\n🎯 过程监测性能对比:\n');
fprintf('   方法        FAR        FDR        监测性能评级\n');
fprintf('   --------   --------   --------   --------------\n');

for i = 1:length(methods_names)
    method = methods_names{i};
    if isfield(comparison_metrics, method) && ~isnan(comparison_metrics.(method).FAR)
        metrics = comparison_metrics.(method);
        far_val = metrics.FAR;
        fdr_val = metrics.FDR;

        % 监测性能评级
        if far_val < 0.05 && fdr_val > 0.8
            monitoring = '优秀';
        elseif far_val < 0.1 && fdr_val > 0.7
            monitoring = '良好';
        elseif far_val < 0.2 && fdr_val > 0.6
            monitoring = '一般';
        else
            monitoring = '较差';
        end

        fprintf('   %-8s   %8.4f   %8.4f   %s\n', method, far_val, fdr_val, monitoring);
    else
        fprintf('   %-8s   %8s   %8s   %s\n', method, 'N/A', 'N/A', '数据缺失');
    end
end

%% 3.4 计算效率对比
fprintf('\n⏱️  计算效率对比:\n');
fprintf('   方法        总耗时(秒)   效率评级\n');
fprintf('   --------   ----------   ----------\n');

for i = 1:length(methods_names)
    method = methods_names{i};
    if isfield(comparison_metrics, method) && ~isnan(comparison_metrics.(method).total_time)
        metrics = comparison_metrics.(method);
        total_time = metrics.total_time;

        % 效率评级
        if total_time < 30
            efficiency = '很快';
        elseif total_time < 60
            efficiency = '较快';
        elseif total_time < 120
            efficiency = '一般';
        else
            efficiency = '较慢';
        end

        fprintf('   %-8s   %10.1f   %s\n', method, total_time, efficiency);
    else
        fprintf('   %-8s   %10s   %s\n', method, 'N/A', '数据缺失');
    end
end

%% 4. 可视化对比
fprintf('\n📊 生成可视化对比图表...\n');

figure('Position', [100, 100, 1400, 1000]);

% 准备数据
valid_methods = {};
nmsc_data = [];
far_data = [];
fdr_data = [];
time_data = [];

for i = 1:length(methods_names)
    method = methods_names{i};
    if isfield(comparison_metrics, method)
        metrics = comparison_metrics.(method);
        if ~isnan(metrics.NMSC_mean)
            valid_methods{end+1} = method;
            nmsc_data(end+1) = metrics.NMSC_mean;
            far_data(end+1) = metrics.FAR;
            fdr_data(end+1) = metrics.FDR;
            time_data(end+1) = metrics.total_time;
        end
    end
end

n_valid = length(valid_methods);

if n_valid > 0
    % 子图1: NMSC对比
    subplot(2,3,1);
    bar_colors = cell2mat(methods_colors(1:n_valid)');
    b1 = bar(nmsc_data, 'FaceColor', 'flat');
    b1.CData = bar_colors;
    set(gca, 'XTickLabel', valid_methods);
    ylabel('NMSC (越小越好)', 'FontSize', 12);
    title('字典稳定性对比', 'FontSize', 14);
    grid on;
    for i = 1:n_valid
        text(i, nmsc_data(i), sprintf('%.3f', nmsc_data(i)), ...
             'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom');
    end

    % 子图2: FAR对比
    subplot(2,3,2);
    b2 = bar(far_data, 'FaceColor', 'flat');
    b2.CData = bar_colors;
    set(gca, 'XTickLabel', valid_methods);
    ylabel('FAR (越小越好)', 'FontSize', 12);
    title('误报率对比', 'FontSize', 14);
    grid on;
    for i = 1:n_valid
        if ~isnan(far_data(i))
            text(i, far_data(i), sprintf('%.3f', far_data(i)), ...
                 'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom');
        end
    end

    % 子图3: FDR对比
    subplot(2,3,3);
    b3 = bar(fdr_data, 'FaceColor', 'flat');
    b3.CData = bar_colors;
    set(gca, 'XTickLabel', valid_methods);
    ylabel('FDR (越大越好)', 'FontSize', 12);
    title('检出率对比', 'FontSize', 14);
    grid on;
    for i = 1:n_valid
        if ~isnan(fdr_data(i))
            text(i, fdr_data(i), sprintf('%.3f', fdr_data(i)), ...
                 'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom');
        end
    end

    % 子图4: 计算时间对比
    subplot(2,3,4);
    b4 = bar(time_data, 'FaceColor', 'flat');
    b4.CData = bar_colors;
    set(gca, 'XTickLabel', valid_methods);
    ylabel('计算时间 (秒)', 'FontSize', 12);
    title('计算效率对比', 'FontSize', 14);
    grid on;
    for i = 1:n_valid
        if ~isnan(time_data(i))
            text(i, time_data(i), sprintf('%.1f', time_data(i)), ...
                 'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom');
        end
    end

    % 子图5: 综合性能雷达图
    subplot(2,3,[5,6]);
    if n_valid >= 2
        % 归一化指标 (0-1范围)
        nmsc_norm = 1 - (nmsc_data - min(nmsc_data)) / (max(nmsc_data) - min(nmsc_data) + 1e-6);
        far_norm = 1 - far_data;  % FAR越小越好
        fdr_norm = fdr_data;      % FDR越大越好
        time_norm = 1 - (time_data - min(time_data)) / (max(time_data) - min(time_data) + 1e-6);

        % 雷达图数据
        radar_data = [nmsc_norm; far_norm; fdr_norm; time_norm]';
        radar_labels = {'字典稳定性', '误报率', '检出率', '计算效率'};

        % 简化雷达图
        angles = linspace(0, 2*pi, length(radar_labels)+1);
        for i = 1:n_valid
            values = [radar_data(i,:), radar_data(i,1)];  % 闭合
            polarplot(angles, values, 'o-', 'LineWidth', 2, ...
                     'Color', bar_colors(i,:), 'DisplayName', valid_methods{i});
            hold on;
        end
        title('综合性能雷达图', 'FontSize', 14);
        legend('Location', 'best');
    else
        text(0.5, 0.5, '数据不足，无法生成雷达图', 'HorizontalAlignment', 'center');
    end

    sgtitle('三种字典学习方法综合对比', 'FontSize', 16);

    % 保存图像
    savefig('methods_comprehensive_comparison.fig');
    fprintf('   ✓ 对比图表已保存到: methods_comprehensive_comparison.fig\n');
else
    fprintf('   ⚠️  有效数据不足，无法生成对比图表\n');
end

%% 5. 综合评估和排名
fprintf('\n🏆 综合评估和排名...\n');

if n_valid > 1
    % 计算综合得分 (加权平均)
    weights = struct('stability', 0.3, 'monitoring', 0.4, 'efficiency', 0.3);

    scores = struct();
    for i = 1:n_valid
        method = valid_methods{i};

        % 稳定性得分 (NMSC越小越好，转换为0-100分)
        if nmsc_data(i) < 0.1
            stability_score = 100;
        elseif nmsc_data(i) < 0.5
            stability_score = 80;
        elseif nmsc_data(i) < 2.0
            stability_score = 60;
        else
            stability_score = 40;
        end

        % 监测性能得分
        if ~isnan(far_data(i)) && ~isnan(fdr_data(i))
            if far_data(i) < 0.05 && fdr_data(i) > 0.8
                monitoring_score = 100;
            elseif far_data(i) < 0.1 && fdr_data(i) > 0.7
                monitoring_score = 80;
            elseif far_data(i) < 0.2 && fdr_data(i) > 0.6
                monitoring_score = 60;
            else
                monitoring_score = 40;
            end
        else
            monitoring_score = 50;  % 缺失数据给中等分
        end

        % 效率得分
        if ~isnan(time_data(i))
            if time_data(i) < 30
                efficiency_score = 100;
            elseif time_data(i) < 60
                efficiency_score = 80;
            elseif time_data(i) < 120
                efficiency_score = 60;
            else
                efficiency_score = 40;
            end
        else
            efficiency_score = 50;  % 缺失数据给中等分
        end

        % 综合得分
        total_score = weights.stability * stability_score + ...
                     weights.monitoring * monitoring_score + ...
                     weights.efficiency * efficiency_score;

        scores.(method) = struct('stability', stability_score, ...
                               'monitoring', monitoring_score, ...
                               'efficiency', efficiency_score, ...
                               'total', total_score);
    end

    % 排序
    method_scores = [];
    for i = 1:n_valid
        method_scores(i) = scores.(valid_methods{i}).total;
    end
    [sorted_scores, sort_idx] = sort(method_scores, 'descend');

    fprintf('\n📊 综合评估结果 (满分100分):\n');
    fprintf('   排名  方法      稳定性  监测性能  计算效率  综合得分\n');
    fprintf('   ----  ------   ------  --------  --------  --------\n');

    for i = 1:n_valid
        idx = sort_idx(i);
        method = valid_methods{idx};
        score = scores.(method);
        fprintf('   %2d    %-6s   %6.0f  %8.0f  %8.0f  %8.1f\n', ...
                i, method, score.stability, score.monitoring, score.efficiency, score.total);
    end

    % 最佳方法推荐
    best_method = valid_methods{sort_idx(1)};
    fprintf('\n🥇 推荐方法: %s (综合得分: %.1f分)\n', best_method, sorted_scores(1));

    % 各维度最佳
    [~, best_stability_idx] = max([scores.(valid_methods{:}).stability]);
    [~, best_monitoring_idx] = max([scores.(valid_methods{:}).monitoring]);
    [~, best_efficiency_idx] = max([scores.(valid_methods{:}).efficiency]);

    fprintf('\n🎯 各维度最佳:\n');
    fprintf('   字典稳定性最佳: %s\n', valid_methods{best_stability_idx});
    fprintf('   监测性能最佳: %s\n', valid_methods{best_monitoring_idx});
    fprintf('   计算效率最佳: %s\n', valid_methods{best_efficiency_idx});

else
    fprintf('   ⚠️  有效方法数量不足，无法进行综合评估\n');
end

%% 6. 保存对比结果
fprintf('\n💾 保存对比分析结果...\n');

comprehensive_comparison = struct();
comprehensive_comparison.methods_data = methods_data;
comprehensive_comparison.comparison_metrics = comparison_metrics;
comprehensive_comparison.analysis_time = datetime('now');

if exist('scores', 'var')
    comprehensive_comparison.scores = scores;
    comprehensive_comparison.ranking = valid_methods(sort_idx);
end

save('comprehensive_methods_comparison_results.mat', 'comprehensive_comparison');
fprintf('   ✓ 对比结果已保存到: comprehensive_methods_comparison_results.mat\n');

%% 7. 使用建议
fprintf('\n💡 使用建议:\n');
fprintf('   1. 查看对比图表: open(''methods_comprehensive_comparison.fig'')\n');
fprintf('   2. 加载详细数据: load(''comprehensive_methods_comparison_results.mat'')\n');
fprintf('   3. 根据应用场景选择最适合的方法\n');
fprintf('   4. 考虑方法组合使用以发挥各自优势\n');

fprintf('\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
fprintf('🎉 三种字典学习方法全面对比分析完成！\n');
fprintf('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');

%% 辅助函数
function nmsc_values = calculate_nmsc(dict_history)
% 计算NMSC值
    n_modes = length(dict_history);
    nmsc_values = zeros(n_modes-1, 1);
    epsilon = 1e-8;

    for i = 2:n_modes
        if ~isempty(dict_history{i-1}) && ~isempty(dict_history{i})
            D_prev = dict_history{i-1};
            D_curr = dict_history{i};
            deltaD = D_curr - D_prev;
            nmsc_values(i-1) = mean((deltaD(:).^2) ./ (D_prev(:).^2 + epsilon));
        else
            nmsc_values(i-1) = NaN;
        end
    end
end


