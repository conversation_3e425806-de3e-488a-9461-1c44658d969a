% ==== 准备数据 ====
modes = [1, 2, 3, 4, 5];

% NMSC 数据
NMSC_GILDL   = [0,78.754,  2.056, 5.149, 10.915];
NMSC_DGCDL  = [0, 0.178, 64.253, 6.768,  8.523];   % λ=5,n=20,s=2
NMSC_DMCDL   = [0,81.724,  0.000, 1.631,  0.000];   % 字典 8×20

% 主空间夹角数据（度）
ang_GILDL    = [0,6.42, 6.48, 5.62, 4.30];
ang_DGCDL   = [0,0.02, 0.09, 0.06, 0.08];
ang_DMCDL    = [0,8.36, 0.00, 0.39, 0.00];

% 计算平均值（排除 Mode 1 的零值）
avgNMSC = [mean(NMSC_GILDL(2:end)), mean(NMSC_DGCDL(2:end)), mean(NMSC_DMCDL(2:end))];
avgAng  = [mean(ang_GILDL(2:end)),    mean(ang_DGCDL(2:end)),    mean(ang_DMCDL(2:end))];

% 方法名称分类
methods = categorical({'GILDL','DGCDL','DMCDL'});
methods = reordercats(methods, {'GILDL','DGCDL','DMCDL'});

% ==== 绘图 ====

% 图1：NMSC 演化
figure('Position',[100 100 800 600]);
h1 = plot(modes, NMSC_GILDL,  '-o','LineWidth',2,'MarkerSize',8); hold on;
h2 = plot(modes, NMSC_SVD_DL, '-s','LineWidth',2,'MarkerSize',8);
h3 = plot(modes, NMSC_DMCDL,  '-^','LineWidth',2,'MarkerSize',8);
hold off;
set(gca,'XTick',modes,'FontName','Times New Roman','FontSize',18,'FontWeight','bold');
xlabel('Mode','FontSize',18,'FontName','Times New Roman','FontWeight','bold');
ylabel('NMSC','FontSize',18,'FontName','Times New Roman','FontWeight','bold');
legend([h1,h2,h3],{'GILDL','DGCDL','DMCDL'},'Location','northeast','FontSize',12);
for i = 1:numel(modes)
    text(modes(i), NMSC_GILDL(i),  sprintf('%.3f',NMSC_GILDL(i)),  ...
        'VerticalAlignment','bottom','HorizontalAlignment','center', ...
        'Color', h1.Color,'FontSize',12);
    text(modes(i), NMSC_SVD_DL(i), sprintf('%.3f',NMSC_SVD_DL(i)), ...
        'VerticalAlignment','bottom','HorizontalAlignment','center', ...
        'Color', h2.Color,'FontSize',12);
    text(modes(i), NMSC_DMCDL(i),  sprintf('%.3f',NMSC_DMCDL(i)),  ...
        'VerticalAlignment','bottom','HorizontalAlignment','center', ...
        'Color', h3.Color,'FontSize',12);
end
saveas(gcf, 'NMSC_evolution.pdf');

% 图2：主空间夹角演化
figure('Position',[100 100 800 600]);
h4 = plot(modes, ang_GILDL,  '-o','LineWidth',2,'MarkerSize',8); hold on;
h5 = plot(modes, ang_SVD_DL, '-s','LineWidth',2,'MarkerSize',8);
h6 = plot(modes, ang_DMCDL,  '-^','LineWidth',2,'MarkerSize',8);
hold off;
set(gca,'XTick',modes,'FontName','Times New Roman','FontSize',18,'FontWeight','bold');
xlabel('Mode','FontSize',18,'FontName','Times New Roman','FontWeight','bold');
ylabel('SD (°)','FontSize',18,'FontName','Times New Roman','FontWeight','bold');
legend([h4,h5,h6],{'GILDL','DGCDL','DMCDL'},'Location','northeast','FontSize',12);
for i = 1:numel(modes)
    text(modes(i), ang_GILDL(i),  sprintf('%.2f°',ang_GILDL(i)),  ...
        'VerticalAlignment','bottom','HorizontalAlignment','center', ...
        'Color', h4.Color,'FontSize',12);
    text(modes(i), ang_SVD_DL(i), sprintf('%.2f°',ang_SVD_DL(i)), ...
        'VerticalAlignment','bottom','HorizontalAlignment','center', ...
        'Color', h5.Color,'FontSize',12);
    text(modes(i), ang_DMCDL(i),  sprintf('%.2f°',ang_DMCDL(i)),  ...
        'VerticalAlignment','bottom','HorizontalAlignment','center', ...
        'Color', h6.Color,'FontSize',12);
end
saveas(gcf, 'subspace_angle_evolution.pdf');

% 图3：平均NMSC和平均SD的组合图
figure('Position',[100 100 800 600]);

% 创建左侧y轴
yyaxis left
hBar1 = barh(methods, avgNMSC', 'BarWidth',0.3,'FaceColor','#99d98c');
hBar1.EdgeColor = 'none';
ylabel('Methods','FontSize',18,'FontName','Times New Roman','FontWeight','bold');
xlabel('Average NMSC','FontSize',18,'FontName','Times New Roman','FontWeight','bold');
ax1 = gca;
ax1.YColor = '#99d98c';

% 创建右侧y轴
yyaxis right
hBar2 = barh(methods, avgAng', 'BarWidth',0.3,'FaceColor','#52b69a');
hBar2.EdgeColor = 'none';
ylabel('Average SD (°)','FontSize',18,'FontName','Times New Roman','FontWeight','bold');
ax2 = gca;
ax2.YColor = '#52b69a';

% 通用设置
set(gca,'FontName','Times New Roman','FontSize',18,'FontWeight','bold');
y = double(methods);

% 标注NMSC数值
yyaxis left
for i = 1:length(avgNMSC)
    text(avgNMSC(i), y(i), sprintf('%.3f', avgNMSC(i)), ...
        'VerticalAlignment','middle', 'HorizontalAlignment','left', ...
        'Color', '#99d98c', 'FontSize',12);
end

% 标注SD数值
yyaxis right
for i = 1:length(avgAng)
    text(avgAng(i), y(i), sprintf('%.2f°', avgAng(i)), ...
        'VerticalAlignment','middle', 'HorizontalAlignment','right', ...
        'Color', '#52b69a', 'FontSize',12);
end

% 添加图例
legend([hBar1, hBar2], {'Average NMSC', 'Average SD'}, ...
    'Location', 'southoutside', 'Orientation', 'horizontal', ...
    'FontSize', 12);

saveas(gcf, 'average_metrics.pdf');
