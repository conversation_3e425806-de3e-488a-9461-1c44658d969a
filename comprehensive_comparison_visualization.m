%% 综合对比可视化 - 表格数据 + 实际监测统计量
% 绘制对比数据.md中的表格数据和实际监测统计量

clc; clear; close all;

fprintf('========== 综合对比可视化分析 ==========\n');

%% 1. 对比数据.md中的表格数据
fprintf('1. 处理对比数据表格...\n');

% 总体性能数据
methods = {'GILDL', 'SVD-DL', 'DMCDL'};
colors = [0.2, 0.6, 0.8;    % GILDL - 蓝色
          0.4, 0.8, 0.3;    % SVD-DL - 绿色
          0.8, 0.4, 0.2];   % DMCDL - 橙色

% 总体性能指标
overall_data = struct();
overall_data.NMSC = [21.8686, 20.8155, 20.8388];
overall_data.angle = [5.70, 0.06, 2.19];
overall_data.FAR = [0.0116, 0.0084, 0.0100];
overall_data.FDR = [0.9428, 1.0000, 0.9680];

% 各模式详细数据
modes = 2:5;
detailed_data = struct();

% GILDL详细数据
detailed_data.GILDL.NMSC = [78.754, 2.056, 5.149, 10.915];
detailed_data.GILDL.angle = [6.42, 6.48, 5.62, 4.30];

% SVD-DL详细数据
detailed_data.SVD_DL.NMSC = [0.178, 64.253, 6.768, 8.523];
detailed_data.SVD_DL.angle = [0.02, 0.09, 0.06, 0.08];

% DMCDL详细数据
detailed_data.DMCDL.NMSC = [81.724, 0.000, 1.631, 0.000];
detailed_data.DMCDL.angle = [8.36, 0.00, 0.39, 0.00];

%% 2. 加载实际监测统计量数据
fprintf('2. 加载实际监测统计量数据...\n');

monitoring_data = struct();

% 加载DMCDL监测数据
try
    if exist('DMCDL/DMCDL_R_sta.mat', 'file') && exist('DMCDL/DMCDL_R_th.mat', 'file')
        load('DMCDL/DMCDL_R_sta.mat');
        load('DMCDL/DMCDL_R_th.mat');
        monitoring_data.DMCDL.R_sta = R_sta;
        monitoring_data.DMCDL.R_th = R_th;
        fprintf('   ✓ DMCDL监测数据已加载\n');
    else
        fprintf('   ❌ DMCDL监测数据文件未找到\n');
        monitoring_data.DMCDL = [];
    end
catch
    fprintf('   ❌ DMCDL监测数据加载失败\n');
    monitoring_data.DMCDL = [];
end

% 加载GILDL监测数据
try
    if exist('GILDL/GILDL_R_sta.mat', 'file') && exist('GILDL/GILDL_R_th.mat', 'file')
        load('GILDL/GILDL_R_sta.mat');
        load('GILDL/GILDL_R_th.mat');
        monitoring_data.GILDL.R_sta = R_sta;
        monitoring_data.GILDL.R_th = R_th;
        fprintf('   ✓ GILDL监测数据已加载\n');
    else
        fprintf('   ❌ GILDL监测数据文件未找到\n');
        monitoring_data.GILDL = [];
    end
catch
    fprintf('   ❌ GILDL监测数据加载失败\n');
    monitoring_data.GILDL = [];
end

% 加载SVD-DL监测数据
try
    if exist('SVD_DL/SVD-DL_R_sta.mat', 'file') && exist('SVD_DL/SVD_DL_R_th.mat', 'file')
        load('SVD_DL/SVD-DL_R_sta.mat');
        load('SVD_DL/SVD_DL_R_th.mat');
        monitoring_data.SVD_DL.R_sta = R_sta;
        monitoring_data.SVD_DL.R_th = R_th;
        fprintf('   ✓ SVD-DL监测数据已加载\n');
    else
        fprintf('   ❌ SVD-DL监测数据文件未找到\n');
        monitoring_data.SVD_DL = [];
    end
catch
    fprintf('   ❌ SVD-DL监测数据加载失败\n');
    monitoring_data.SVD_DL = [];
end

%% 3. 绘制对比数据表格可视化
fprintf('\n3. 生成对比数据表格可视化...\n');

figure('Position', [50, 50, 1600, 1200]);

% 子图1: 总体NMSC对比
subplot(3,4,1);
b1 = bar(overall_data.NMSC, 'FaceColor', 'flat');
b1.CData = colors;
set(gca, 'XTickLabel', methods);
ylabel('平均NMSC', 'FontSize', 12);
title('总体NMSC对比', 'FontSize', 14, 'FontWeight', 'bold');
grid on;
for i = 1:3
    text(i, overall_data.NMSC(i), sprintf('%.2f', overall_data.NMSC(i)), ...
         'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', 'FontSize', 10);
end

% 子图2: 总体主空间夹角对比
subplot(3,4,2);
b2 = bar(overall_data.angle, 'FaceColor', 'flat');
b2.CData = colors;
set(gca, 'XTickLabel', methods);
ylabel('平均主空间夹角 (°)', 'FontSize', 12);
title('总体主空间夹角对比', 'FontSize', 14, 'FontWeight', 'bold');
grid on;
for i = 1:3
    text(i, overall_data.angle(i), sprintf('%.2f°', overall_data.angle(i)), ...
         'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', 'FontSize', 10);
end

% 子图3: FAR对比
subplot(3,4,3);
b3 = bar(overall_data.FAR, 'FaceColor', 'flat');
b3.CData = colors;
set(gca, 'XTickLabel', methods);
ylabel('FAR (误报率)', 'FontSize', 12);
title('FAR对比', 'FontSize', 14, 'FontWeight', 'bold');
grid on;
for i = 1:3
    text(i, overall_data.FAR(i), sprintf('%.4f', overall_data.FAR(i)), ...
         'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', 'FontSize', 10);
end

% 子图4: FDR对比
subplot(3,4,4);
b4 = bar(overall_data.FDR, 'FaceColor', 'flat');
b4.CData = colors;
set(gca, 'XTickLabel', methods);
ylabel('FDR (检出率)', 'FontSize', 12);
title('FDR对比', 'FontSize', 14, 'FontWeight', 'bold');
grid on;
for i = 1:3
    text(i, overall_data.FDR(i), sprintf('%.4f', overall_data.FDR(i)), ...
         'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', 'FontSize', 10);
end

% 子图5: 各模式NMSC演化
subplot(3,4,5);
hold on;
for i = 1:3
    method = methods{i};
    method_key = strrep(method, '-', '_');
    plot(modes, detailed_data.(method_key).NMSC, 'o-', 'Color', colors(i,:), ...
         'LineWidth', 2.5, 'MarkerSize', 8, 'MarkerFaceColor', colors(i,:), ...
         'DisplayName', method);
end
xlabel('模式', 'FontSize', 12);
ylabel('NMSC', 'FontSize', 12);
title('各模式NMSC演化', 'FontSize', 14, 'FontWeight', 'bold');
legend('Location', 'best', 'FontSize', 10);
grid on;

% 子图6: 各模式主空间夹角演化
subplot(3,4,6);
hold on;
for i = 1:3
    method = methods{i};
    method_key = strrep(method, '-', '_');
    plot(modes, detailed_data.(method_key).angle, 's-', 'Color', colors(i,:), ...
         'LineWidth', 2.5, 'MarkerSize', 8, 'MarkerFaceColor', colors(i,:), ...
         'DisplayName', method);
end
xlabel('模式', 'FontSize', 12);
ylabel('主空间夹角 (°)', 'FontSize', 12);
title('各模式主空间夹角演化', 'FontSize', 14, 'FontWeight', 'bold');
legend('Location', 'best', 'FontSize', 10);
grid on;

% 子图7-9: 实际监测统计量 (如果数据可用)
subplot_idx = 7;
for i = 1:3
    method = methods{i};
    method_key = strrep(method, '-', '_');
    
    subplot(3,4,subplot_idx);
    
    if ~isempty(monitoring_data.(method_key))
        R_sta = monitoring_data.(method_key).R_sta;
        R_th = monitoring_data.(method_key).R_th;
        
        % 绘制R统计量
        plot(1:length(R_sta), R_sta, 'Color', colors(i,:), 'LineWidth', 1.2);
        hold on;
        
        % 绘制控制限
        if isscalar(R_th)
            yline(R_th, '--r', 'LineWidth', 2);
        else
            plot(1:length(R_th), R_th, '--r', 'LineWidth', 2);
        end
        
        xlabel('样本编号', 'FontSize', 12);
        ylabel('R统计量', 'FontSize', 12);
        title(sprintf('%s 监测统计量', method), 'FontSize', 14, 'FontWeight', 'bold');
        legend('R统计量', '控制限', 'Location', 'best', 'FontSize', 10);
        grid on;
    else
        text(0.5, 0.5, sprintf('%s\n监测数据未找到', method), ...
             'HorizontalAlignment', 'center', 'VerticalAlignment', 'middle', ...
             'FontSize', 12, 'FontWeight', 'bold');
        title(sprintf('%s 监测统计量', method), 'FontSize', 14, 'FontWeight', 'bold');
    end
    
    subplot_idx = subplot_idx + 1;
end

% 子图10: 综合性能雷达图
subplot(3,4,10);
% 归一化数据用于雷达图
nmsc_norm = 1 - (overall_data.NMSC - min(overall_data.NMSC)) / (max(overall_data.NMSC) - min(overall_data.NMSC) + 1e-6);
angle_norm = 1 - (overall_data.angle - min(overall_data.angle)) / (max(overall_data.angle) - min(overall_data.angle) + 1e-6);
far_norm = 1 - (overall_data.FAR - min(overall_data.FAR)) / (max(overall_data.FAR) - min(overall_data.FAR) + 1e-6);
fdr_norm = (overall_data.FDR - min(overall_data.FDR)) / (max(overall_data.FDR) - min(overall_data.FDR) + 1e-6);

% 简化的性能对比
performance_matrix = [nmsc_norm; angle_norm; far_norm; fdr_norm]' * 100;
b_perf = bar(performance_matrix, 'grouped');
colors_perf = [0.8, 0.3, 0.3; 0.3, 0.8, 0.3; 0.3, 0.3, 0.8; 0.8, 0.8, 0.3];
for j = 1:4
    b_perf(j).FaceColor = colors_perf(j,:);
end

set(gca, 'XTickLabel', methods);
ylabel('归一化性能得分 (%)', 'FontSize', 12);
title('综合性能对比', 'FontSize', 14, 'FontWeight', 'bold');
legend({'NMSC性能', '角度性能', 'FAR性能', 'FDR性能'}, 'Location', 'best', 'FontSize', 9);
grid on;

% 子图11: 性能排名表
subplot(3,4,11);
axis off;

% 计算综合得分
weights = [0.2, 0.2, 0.3, 0.3];  % [NMSC, 角度, FAR, FDR]
total_scores = weights(1) * nmsc_norm + weights(2) * angle_norm + ...
               weights(3) * far_norm + weights(4) * fdr_norm;

[sorted_scores, rank_idx] = sort(total_scores, 'descend');

ranking_text = {'性能排名:', '', ...
                sprintf('1. %s: %.3f', methods{rank_idx(1)}, sorted_scores(1)), ...
                sprintf('2. %s: %.3f', methods{rank_idx(2)}, sorted_scores(2)), ...
                sprintf('3. %s: %.3f', methods{rank_idx(3)}, sorted_scores(3)), '', ...
                '最佳指标:', ...
                sprintf('最低FAR: %s', methods{find(overall_data.FAR == min(overall_data.FAR), 1)}), ...
                sprintf('最高FDR: %s', methods{find(overall_data.FDR == max(overall_data.FDR), 1)}), ...
                sprintf('最小角度: %s', methods{find(overall_data.angle == min(overall_data.angle), 1)})};

text(0.05, 0.95, ranking_text, 'FontSize', 11, 'VerticalAlignment', 'top', ...
     'FontWeight', 'bold');

% 子图12: 数据来源说明
subplot(3,4,12);
axis off;

source_text = {'数据来源:', '', ...
               '表格数据:', ...
               '• 对比数据.md', '', ...
               '监测统计量:', ...
               '• DMCDL/DMCDL_R_sta.mat', ...
               '• GILDL/GILDL_R_sta.mat', ...
               '• SVD_DL/SVD_DL_R_sta.mat', '', ...
               '控制限:', ...
               '• DMCDL/DMCDL_R_th.mat', ...
               '• GILDL/GILDL_R_th.mat', ...
               '• SVD_DL/SVD_DL_R_th.mat'};

text(0.05, 0.95, source_text, 'FontSize', 10, 'VerticalAlignment', 'top');

sgtitle('三种字典学习方法综合对比分析 (表格数据 + 实际监测统计量)', 'FontSize', 16, 'FontWeight', 'bold');

%% 4. 专门的实际监测统计量对比图
fprintf('\n4. 生成专门的监测统计量对比图...\n');

% 检查有多少方法有实际监测数据
available_methods = {};
available_colors = [];
for i = 1:3
    method = methods{i};
    method_key = strrep(method, '-', '_');
    if ~isempty(monitoring_data.(method_key))
        available_methods{end+1} = method;
        available_colors(end+1,:) = colors(i,:);
    end
end

if ~isempty(available_methods)
    figure('Position', [100, 100, 1400, 800]);

    % 子图1: 所有方法的监测统计量在一张图上
    subplot(2,2,1);
    hold on;

    legend_entries = {};
    y_offset = 0;

    for i = 1:length(available_methods)
        method = available_methods{i};
        method_key = strrep(method, '-', '_');

        R_sta = monitoring_data.(method_key).R_sta + y_offset;
        R_th = monitoring_data.(method_key).R_th;

        % 绘制R统计量
        plot(1:length(R_sta), R_sta, 'Color', available_colors(i,:), 'LineWidth', 1.5);

        % 绘制控制限
        if isscalar(R_th)
            yline(R_th + y_offset, '--', 'Color', available_colors(i,:), 'LineWidth', 2);
        else
            plot(1:length(R_th), R_th + y_offset, '--', 'Color', available_colors(i,:), 'LineWidth', 2);
        end

        legend_entries{end+1} = sprintf('%s R统计量', method);
        legend_entries{end+1} = sprintf('%s 控制限', method);

        y_offset = y_offset + 2;  % 垂直偏移
    end

    xlabel('样本编号', 'FontSize', 14);
    ylabel('R统计量 (垂直偏移)', 'FontSize', 14);
    title('三种方法监测统计量对比 (垂直偏移)', 'FontSize', 16, 'FontWeight', 'bold');
    legend(legend_entries, 'Location', 'best', 'FontSize', 10);
    grid on;

    % 子图2: 不带偏移的重叠显示
    subplot(2,2,2);
    hold on;

    for i = 1:length(available_methods)
        method = available_methods{i};
        method_key = strrep(method, '-', '_');

        R_sta = monitoring_data.(method_key).R_sta;

        plot(1:length(R_sta), R_sta, 'Color', available_colors(i,:), 'LineWidth', 1.5, ...
             'DisplayName', method);
    end

    xlabel('样本编号', 'FontSize', 14);
    ylabel('R统计量', 'FontSize', 14);
    title('三种方法监测统计量重叠对比', 'FontSize', 16, 'FontWeight', 'bold');
    legend('Location', 'best', 'FontSize', 12);
    grid on;

    % 子图3: 控制限对比
    subplot(2,2,3);

    control_limits = [];
    for i = 1:length(available_methods)
        method = available_methods{i};
        method_key = strrep(method, '-', '_');

        R_th = monitoring_data.(method_key).R_th;
        if isscalar(R_th)
            control_limits(i) = R_th;
        else
            control_limits(i) = mean(R_th);  % 如果是向量，取平均值
        end
    end

    b_limits = bar(control_limits, 'FaceColor', 'flat');
    b_limits.CData = available_colors;
    set(gca, 'XTickLabel', available_methods);
    ylabel('控制限', 'FontSize', 14);
    title('控制限对比', 'FontSize', 16, 'FontWeight', 'bold');
    grid on;

    for i = 1:length(control_limits)
        text(i, control_limits(i), sprintf('%.4f', control_limits(i)), ...
             'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', ...
             'FontSize', 12, 'FontWeight', 'bold');
    end

    % 子图4: 统计量分布对比
    subplot(2,2,4);
    hold on;

    for i = 1:length(available_methods)
        method = available_methods{i};
        method_key = strrep(method, '-', '_');

        R_sta = monitoring_data.(method_key).R_sta;

        % 计算概率密度
        [f, x] = ksdensity(R_sta);
        plot(x, f, 'Color', available_colors(i,:), 'LineWidth', 2.5, ...
             'DisplayName', method);
    end

    xlabel('R统计量', 'FontSize', 14);
    ylabel('概率密度', 'FontSize', 14);
    title('R统计量分布对比', 'FontSize', 16, 'FontWeight', 'bold');
    legend('Location', 'best', 'FontSize', 12);
    grid on;

    sgtitle('实际监测统计量详细对比分析', 'FontSize', 18, 'FontWeight', 'bold');

    % 保存监测统计量对比图
    savefig('monitoring_statistics_comparison.fig');
    print('monitoring_statistics_comparison.png', '-dpng', '-r300');
    fprintf('   ✓ 监测统计量对比图已保存\n');

else
    fprintf('   ⚠️  没有找到实际监测统计量数据\n');
end

%% 5. 保存主要对比图
savefig('comprehensive_comparison_visualization.fig');
print('comprehensive_comparison_visualization.png', '-dpng', '-r300');

fprintf('✓ 综合对比图已保存:\n');
fprintf('  - comprehensive_comparison_visualization.fig\n');
fprintf('  - comprehensive_comparison_visualization.png\n');
if ~isempty(available_methods)
    fprintf('  - monitoring_statistics_comparison.fig\n');
    fprintf('  - monitoring_statistics_comparison.png\n');
end

%% 6. 输出分析总结
fprintf('\n📊 综合对比分析总结:\n');
fprintf('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');

fprintf('\n📈 表格数据对比结果:\n');
fprintf('   方法      平均NMSC   主空间夹角(°)    FAR      FDR\n');
fprintf('   ------   --------   -----------   ------   ------\n');
for i = 1:3
    fprintf('   %-6s   %8.2f   %11.2f   %.4f   %.4f\n', methods{i}, ...
            overall_data.NMSC(i), overall_data.angle(i), overall_data.FAR(i), overall_data.FDR(i));
end

fprintf('\n🏆 最佳性能:\n');
[~, best_nmsc] = min(overall_data.NMSC);
[~, best_angle] = min(overall_data.angle);
[~, best_far] = min(overall_data.FAR);
[~, best_fdr] = max(overall_data.FDR);

fprintf('   最低NMSC: %s (%.2f)\n', methods{best_nmsc}, overall_data.NMSC(best_nmsc));
fprintf('   最小角度: %s (%.2f°)\n', methods{best_angle}, overall_data.angle(best_angle));
fprintf('   最低FAR: %s (%.4f)\n', methods{best_far}, overall_data.FAR(best_far));
fprintf('   最高FDR: %s (%.4f)\n', methods{best_fdr}, overall_data.FDR(best_fdr));

if ~isempty(available_methods)
    fprintf('\n📊 实际监测统计量数据:\n');
    fprintf('   可用方法: %s\n', strjoin(available_methods, ', '));

    for i = 1:length(available_methods)
        method = available_methods{i};
        method_key = strrep(method, '-', '_');

        R_sta = monitoring_data.(method_key).R_sta;
        R_th = monitoring_data.(method_key).R_th;

        if isscalar(R_th)
            control_limit_val = R_th;
        else
            control_limit_val = mean(R_th);
        end
        fprintf('   %s: 样本数=%d, 控制限=%.4f\n', method, length(R_sta), control_limit_val);
    end
else
    fprintf('\n⚠️  未找到实际监测统计量数据文件\n');
end

fprintf('\n🎉 综合对比可视化分析完成！\n');
fprintf('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
