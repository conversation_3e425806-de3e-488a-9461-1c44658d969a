% 主原子锁定与主方向空间保护示意图
figure; clf; hold on; axis equal
set(gcf, 'Color', 'w');
axis([-2 2 -2 2]);
set(gca,'xtick',[],'ytick',[]);

% 主方向空间（椭圆区域）
theta = linspace(0, 2*pi, 100);
a = 1.6; b = 1.0;        % 椭圆长/短轴
x_ell = a*cos(theta);
y_ell = b*sin(theta);
fill(x_ell, y_ell, [0.8 0.93 1], 'EdgeColor',[0.2 0.5 0.9],'LineWidth',2);
text(-1.6,1.0,'主方向空间（U_{locked})','FontSize',12,'Color',[0.2 0.5 0.9]);

% 主方向（两根长箭头）
quiver(0, 0, a*0.8, 0, 0, 'Color',[0.1 0.3 0.7],'LineWidth',2,'MaxHeadSize',0.8)
quiver(0, 0, 0, b*0.8, 0, 'Color',[0.1 0.3 0.7],'LineWidth',2,'MaxHeadSize',0.8)

% 主原子（椭圆内的彩色小箭头，加锁）
main_atom_dir = [cosd(15) sind(15); cosd(-25) sind(-25); cosd(50) sind(50)];
main_atom_col = [0.9 0.3 0.2; 0.2 0.7 0.2; 0.85 0.65 0.15];
for i = 1:3
    quiver(0,0, main_atom_dir(i,1)*1.2, main_atom_dir(i,2)*0.7, 0,...
        'Color',main_atom_col(i,:),'LineWidth',3,'MaxHeadSize',0.7)
    % 画锁(用小黑色圆圈模拟)
    plot(main_atom_dir(i,1)*1.3, main_atom_dir(i,2)*0.75, 'ko', ...
        'MarkerFaceColor',[0.1 0.1 0.1],'MarkerSize',7)
end
text(1.1, 0.3, '主原子锁定', 'FontSize', 12, 'Color', [0.8 0.2 0.1]);

% 非主原子（椭圆外的灰色小箭头）
other_atom_dir = [cosd(110) sind(110); cosd(-110) sind(-110); cosd(140) sind(140)];
for i = 1:3
    quiver(0, 0, other_atom_dir(i,1)*1.6, other_atom_dir(i,2)*1.0, 0, ...
        'Color',[0.5 0.5 0.5],'LineWidth',2,'MaxHeadSize',0.7,'LineStyle','--')
end
text(-1.8, -1.2, '非主原子（可自适应）', 'FontSize', 11, 'Color', [0.5 0.5 0.5]);

% 椭圆虚线轮廓（表示主空间可略变动）
plot(1.05*x_ell, 1.05*y_ell, '--','Color',[0.2 0.5 0.9 0.4],'LineWidth',1.5);

% 空间保护标注（箭头+文字）
annotation('arrow',[0.38 0.43],[0.85 0.7],'Color',[0.2 0.5 0.9],'LineWidth',2);
text(0.0, 1.5, '主空间保护（空间正则）','FontSize',11,'Color',[0.2 0.5 0.9]);

% 高维空间边框
rectangle('Position',[-2 -2 4 4],'EdgeColor',[0.6 0.6 0.6],'LineStyle','-.');

% 全局说明
text(-2,2.1,'主原子锁定 & 主方向空间保护 - 示意图', ...
    'FontSize',13,'FontWeight','bold','Color',[0.1 0.2 0.4]);

hold off;
