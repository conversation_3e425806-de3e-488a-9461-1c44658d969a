%% CSTR数据集过程检测任务测试
% 使用learn_DL_CSTR.m计算出的最终字典进行过程检测

rng(42);
clear; close all;

% 添加上级目录到路径，以便调用omp函数
addpath('..');

fprintf('========== CSTR过程检测任务测试 ==========\n');

%% ========== 1. 加载最终融合字典 ==========
fprintf('1. 加载最终融合字典...\n');

% 检查是否存在learn_DL_CSTR的结果
if exist('CSTR_SVD_DL_results.mat', 'file')
    load('CSTR_SVD_DL_results.mat', 'Dictionary_history_CSTR');
    D_K = Dictionary_history_CSTR{end};  % 使用最终字典（模式3）
    fprintf('   ✓ 成功加载CSTR双重保护SVD_DL最终字典\n');
    fprintf('   字典大小: %dx%d\n', size(D_K,1), size(D_K,2));
else
    fprintf('   ❌ 未找到CSTR_SVD_DL_results.mat，请先运行learn_DL_CSTR.m\n');
    return;
end

sparsity = 2;  % 稀疏度设置

%% ========== 2. 拼接CSTR训练数据 ==========
fprintf('\n2. 加载CSTR训练数据...\n');

% 加载CSTR训练数据
load('CSTR_3modes_train_data.mat');

% 提取3个模式的训练数据
train_data_mode1 = simout(2:400, :);      % 模式1训练数据
train_data_mode2 = simout(406:801, :);    % 模式2训练数据
train_data_mode3 = simout(806:1201, :);   % 模式3训练数据

% 拼接所有训练数据
Y_train = [train_data_mode1; train_data_mode2; train_data_mode3];
Y_train = Y_train';  % 转置为 [特征维度, 样本数]

fprintf('   训练数据大小: %dx%d\n', size(Y_train,1), size(Y_train,2));

%% ========== 3. 拼接CSTR测试数据（包含故障） ==========
fprintf('\n3. 加载CSTR测试数据（包含故障）...\n');

Y_test = [];

% 加载模式1测试数据（包含故障F6）
load('CSTR_mode1_withF6.mat');
test_data_mode1 = simout';
Y_test = [Y_test, test_data_mode1];
fprintf('   模式1测试数据: %d样本\n', size(test_data_mode1,2));

% 加载模式2测试数据（包含故障F4-10）
load('CSTR_mode2_withF4-10.mat');
test_data_mode2 = simout';
Y_test = [Y_test, test_data_mode2];
fprintf('   模式2测试数据: %d样本\n', size(test_data_mode2,2));

% 加载模式3测试数据（包含故障F4F5）
load('CSTR_mode3_withF4F5.mat');
test_data_mode3 = simout';
Y_test = [Y_test, test_data_mode3];
fprintf('   模式3测试数据: %d样本\n', size(test_data_mode3,2));

fprintf('   总测试数据大小: %dx%d\n', size(Y_test,1), size(Y_test,2));

%% ========== 4. 训练数据：OMP编码+R统计量 ==========
fprintf('\n4. 计算训练数据R统计量...\n');

R_train = zeros(1, size(Y_train,2));
for i = 1:size(Y_train,2)
    y = Y_train(:,i);
    x = omp(D_K, y, sparsity);
    R_train(i) = norm(y - D_K*x, 2)^2;
end

% 使用KDE估计统计量的分布并计算控制限
[f_R, xi_R] = ksdensity(R_train, 'Function', 'cdf');
% 找到 1-alpha 分位数 (alpha = 0.01, 即99%置信度)
alpha = 0.01;
idx_R = find(f_R >= 1 - alpha, 1, 'first');
R_limit = xi_R(idx_R);

fprintf('   训练样本数: %d\n', length(R_train));
fprintf('   R统计量范围: [%.6f, %.6f]\n', min(R_train), max(R_train));
fprintf('   控制限 (99%%置信度): %.6f\n', R_limit);

%% ========== 5. 测试集：OMP编码+R统计量 ==========
fprintf('\n5. 计算测试数据R统计量...\n');

R_test = zeros(1, size(Y_test,2));
for i = 1:size(Y_test,2)
    y = Y_test(:,i);
    x = omp(D_K, y, sparsity);
    R_test(i) = norm(y - D_K*x, 2)^2;
end

fprintf('   测试样本数: %d\n', length(R_test));
fprintf('   R统计量范围: [%.6f, %.6f]\n', min(R_test), max(R_test));

%% ========== 6. 可视化统计量与控制限 ==========
fprintf('\n6. 生成监测图表...\n');

figure('Position', [100, 100, 1200, 800]);

% 主监测图
subplot(2,2,[1,2]);
plot(R_test, 'b-', 'LineWidth', 1);
hold on;
yline(R_limit, '--r', 'LineWidth', 2);

% 标记不同模式的分界线
n_mode1 = size(test_data_mode1, 2);
n_mode2 = size(test_data_mode2, 2);
xline(n_mode1, '--g', '模式1|模式2', 'LineWidth', 1.5);
xline(n_mode1 + n_mode2, '--g', '模式2|模式3', 'LineWidth', 1.5);

xlabel('样本编号', 'FontSize', 12);
ylabel('R统计量', 'FontSize', 12);
title('CSTR过程监测 - R统计量与控制限', 'FontSize', 14);
legend('测试样本', '控制限 (99%)', 'Location', 'best');
grid on;

% 训练数据分布
subplot(2,2,3);
histogram(R_train, 50, 'Normalization', 'pdf', 'FaceAlpha', 0.7);
hold on;
xline(R_limit, '--r', 'LineWidth', 2);
xlabel('R统计量', 'FontSize', 12);
ylabel('概率密度', 'FontSize', 12);
title('训练数据R统计量分布', 'FontSize', 12);
legend('训练数据', '控制限', 'Location', 'best');
grid on;

% 测试数据分布
subplot(2,2,4);
histogram(R_test, 50, 'Normalization', 'pdf', 'FaceAlpha', 0.7);
hold on;
xline(R_limit, '--r', 'LineWidth', 2);
xlabel('R统计量', 'FontSize', 12);
ylabel('概率密度', 'FontSize', 12);
title('测试数据R统计量分布', 'FontSize', 12);
legend('测试数据', '控制限', 'Location', 'best');
grid on;

sgtitle('CSTR数据集 - 双重保护SVD_DL过程监测结果', 'FontSize', 16);

%% ========== 7. 计算FAR和FDR ==========
fprintf('\n7. 计算检测性能指标...\n');

% CSTR数据集的故障模式分析
% 假设每个模式的测试数据中，前半部分为正常，后半部分为故障
n_mode = 3;
mode_sizes = [size(test_data_mode1,2), size(test_data_mode2,2), size(test_data_mode3,2)];

FAR_all = zeros(n_mode, 1);
FDR_all = zeros(n_mode, 1);

idx_start = 1;
for m = 1:n_mode
    n_samples = mode_sizes(m);
    n_normal = floor(n_samples / 2);  % 前半部分为正常
    n_fault = n_samples - n_normal;   % 后半部分为故障

    idx_normal = idx_start : idx_start + n_normal - 1;
    idx_fault = idx_start + n_normal : idx_start + n_samples - 1;

    % 计算FAR (误报率) 和 FDR (检出率)
    FAR_all(m) = sum(R_test(idx_normal) > R_limit) / n_normal;
    FDR_all(m) = sum(R_test(idx_fault) > R_limit) / n_fault;

    fprintf('   模式%d: 正常样本%d个, 故障样本%d个\n', m, n_normal, n_fault);
    fprintf('   模式%d: FAR=%.4f, FDR=%.4f\n', m, FAR_all(m), FDR_all(m));

    idx_start = idx_start + n_samples;
end

% 总体性能
FAR_overall = mean(FAR_all);
FDR_overall = mean(FDR_all);

fprintf('\n========== 检测性能总结 ==========\n');
fprintf('各模式FAR: %s\n', num2str(FAR_all', '%.4f '));
fprintf('各模式FDR: %s\n', num2str(FDR_all', '%.4f '));
fprintf('平均FAR = %.4f (误报率，越小越好)\n', FAR_overall);
fprintf('平均FDR = %.4f (检出率，越大越好)\n', FDR_overall);

% 性能评估
if FAR_overall < 0.05 && FDR_overall > 0.8
    fprintf('✅ 检测性能优秀\n');
elseif FAR_overall < 0.1 && FDR_overall > 0.7
    fprintf('✅ 检测性能良好\n');
else
    fprintf('⚠️  检测性能需要改进\n');
end

%% ========== 8. 保存监测结果 ==========
fprintf('\n8. 保存监测结果...\n');

monitoring_results = struct();
monitoring_results.method = 'CSTR_SVD_DL_Dual_Protection';
monitoring_results.dictionary_size = size(D_K);
monitoring_results.sparsity = sparsity;
monitoring_results.R_limit = R_limit;
monitoring_results.R_train = R_train;
monitoring_results.R_test = R_test;
monitoring_results.FAR_all = FAR_all;
monitoring_results.FDR_all = FDR_all;
monitoring_results.FAR_overall = FAR_overall;
monitoring_results.FDR_overall = FDR_overall;
monitoring_results.alpha = alpha;

save('CSTR_monitoring_results.mat', 'monitoring_results');
fprintf('   监测结果已保存到: CSTR_monitoring_results.mat\n');

fprintf('\n🎉 CSTR过程检测任务测试完成！\n');

