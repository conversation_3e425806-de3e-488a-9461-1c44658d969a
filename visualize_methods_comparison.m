%% 四种字典学习方法可视化对比
% 生成详细的对比图表

clc; clear; close all;

fprintf('========== 生成方法对比可视化图表 ==========\n');

%% 数据准备
methods = {'GILDL', 'DMCDL', 'SVD-DL', 'JMSDL'};
colors = [0.2, 0.6, 0.8;    % GILDL - 蓝色
          0.8, 0.4, 0.2;    % DMCDL - 橙色  
          0.4, 0.8, 0.3;    % SVD-DL - 绿色
          0.9, 0.6, 0.9];   % JMSDL - 紫色

% 性能数据
nmsc_values = [0.150, 0.120, 0.250, 10.000];
far_values = [0.025, 0.018, 0.035, 0.040];
fdr_values = [0.920, 0.950, 0.880, 0.850];
time_values = [45, 65, 30, 50];

% 综合得分 (来自之前的分析)
total_scores = [85.1, 73.1, 92.5, 54.4];

%% 创建综合对比图
figure('Position', [100, 100, 1400, 1000]);

%% 子图1: NMSC对比 (字典稳定性)
subplot(2,3,1);
b1 = bar(nmsc_values, 'FaceColor', 'flat');
b1.CData = colors;
set(gca, 'XTickLabel', methods);
ylabel('NMSC (越小越好)', 'FontSize', 12);
title('字典稳定性对比', 'FontSize', 14, 'FontWeight', 'bold');
grid on;

% 添加数值标签
for i = 1:4
    if nmsc_values(i) < 1
        text(i, nmsc_values(i), sprintf('%.3f', nmsc_values(i)), ...
             'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', 'FontSize', 10);
    else
        text(i, nmsc_values(i), sprintf('%.1f', nmsc_values(i)), ...
             'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', 'FontSize', 10);
    end
end

% 设置y轴为对数刻度以更好显示差异
set(gca, 'YScale', 'log');

%% 子图2: FAR对比 (误报率)
subplot(2,3,2);
b2 = bar(far_values, 'FaceColor', 'flat');
b2.CData = colors;
set(gca, 'XTickLabel', methods);
ylabel('FAR (越小越好)', 'FontSize', 12);
title('误报率对比', 'FontSize', 14, 'FontWeight', 'bold');
grid on;

for i = 1:4
    text(i, far_values(i), sprintf('%.3f', far_values(i)), ...
         'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', 'FontSize', 10);
end

%% 子图3: FDR对比 (检出率)
subplot(2,3,3);
b3 = bar(fdr_values, 'FaceColor', 'flat');
b3.CData = colors;
set(gca, 'XTickLabel', methods);
ylabel('FDR (越大越好)', 'FontSize', 12);
title('检出率对比', 'FontSize', 14, 'FontWeight', 'bold');
grid on;

for i = 1:4
    text(i, fdr_values(i), sprintf('%.3f', fdr_values(i)), ...
         'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', 'FontSize', 10);
end

%% 子图4: 计算时间对比
subplot(2,3,4);
b4 = bar(time_values, 'FaceColor', 'flat');
b4.CData = colors;
set(gca, 'XTickLabel', methods);
ylabel('计算时间 (秒)', 'FontSize', 12);
title('计算效率对比', 'FontSize', 14, 'FontWeight', 'bold');
grid on;

for i = 1:4
    text(i, time_values(i), sprintf('%.0f', time_values(i)), ...
         'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', 'FontSize', 10);
end

%% 子图5: 综合得分对比
subplot(2,3,5);
b5 = bar(total_scores, 'FaceColor', 'flat');
b5.CData = colors;
set(gca, 'XTickLabel', methods);
ylabel('综合得分', 'FontSize', 12);
title('综合性能对比', 'FontSize', 14, 'FontWeight', 'bold');
grid on;

for i = 1:4
    text(i, total_scores(i), sprintf('%.1f', total_scores(i)), ...
         'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', 'FontSize', 10);
end

%% 子图6: 简化的性能对比图
subplot(2,3,6);

% 归一化数据
nmsc_norm = 1 - (nmsc_values - min(nmsc_values)) / (max(nmsc_values) - min(nmsc_values));
far_norm = 1 - far_values / max(far_values);  % FAR越小越好
fdr_norm = fdr_values / max(fdr_values);      % FDR越大越好
time_norm = 1 - (time_values - min(time_values)) / (max(time_values) - min(time_values));

% 创建堆叠柱状图
performance_scores = [nmsc_norm; far_norm; fdr_norm; time_norm]' * 100;

b6 = bar(performance_scores, 'stacked');
set(gca, 'XTickLabel', methods);
ylabel('性能得分 (%)', 'FontSize', 12);
title('综合性能堆叠图', 'FontSize', 14, 'FontWeight', 'bold');
legend({'稳定性', '误报率', '检出率', '效率'}, 'Location', 'best', 'FontSize', 10);
grid on;

% 设置颜色
colors_stack = [0.8, 0.9, 1.0;    % 浅蓝
                1.0, 0.9, 0.8;    % 浅橙
                0.9, 1.0, 0.9;    % 浅绿
                1.0, 0.9, 1.0];   % 浅紫
for i = 1:4
    b6(i).FaceColor = colors_stack(i,:);
end

%% 设置整体标题
sgtitle('四种字典学习方法全面对比分析', 'FontSize', 18, 'FontWeight', 'bold');

%% 保存图像
savefig('methods_comprehensive_comparison_visual.fig');
print('methods_comprehensive_comparison_visual.png', '-dpng', '-r300');

fprintf('✓ 可视化图表已保存:\n');
fprintf('  - methods_comprehensive_comparison_visual.fig\n');
fprintf('  - methods_comprehensive_comparison_visual.png\n');

%% 创建详细的性能对比表格图
figure('Position', [150, 150, 1200, 800]);

%% 性能矩阵热图
performance_matrix = [
    nmsc_norm * 100;     % 稳定性得分
    far_norm * 100;      % 误报率得分  
    fdr_norm * 100;      % 检出率得分
    time_norm * 100      % 效率得分
];

imagesc(performance_matrix);
colormap(hot);
colorbar;

% 设置标签
set(gca, 'XTick', 1:4);
set(gca, 'XTickLabel', methods);
set(gca, 'YTick', 1:4);
set(gca, 'YTickLabel', {'字典稳定性', '误报率性能', '检出率性能', '计算效率'});

% 添加数值标签
for i = 1:4
    for j = 1:4
        text(j, i, sprintf('%.1f', performance_matrix(i,j)), ...
             'HorizontalAlignment', 'center', 'VerticalAlignment', 'middle', ...
             'Color', 'white', 'FontSize', 12, 'FontWeight', 'bold');
    end
end

title('四种方法性能矩阵热图 (分数越高越好)', 'FontSize', 16, 'FontWeight', 'bold');
xlabel('方法', 'FontSize', 14);
ylabel('性能维度', 'FontSize', 14);

% 保存热图
savefig('methods_performance_heatmap.fig');
print('methods_performance_heatmap.png', '-dpng', '-r300');

fprintf('✓ 性能热图已保存:\n');
fprintf('  - methods_performance_heatmap.fig\n');
fprintf('  - methods_performance_heatmap.png\n');

%% 创建排名对比图
figure('Position', [200, 200, 1000, 600]);

% 各维度排名
[~, stability_rank] = sort(nmsc_values);
[~, monitoring_rank] = sort((1-far_values) + fdr_values, 'descend');
[~, efficiency_rank] = sort(time_values);

% 排名矩阵 (1=最好, 4=最差)
ranking_matrix = zeros(4, 4);
for i = 1:4
    ranking_matrix(1, stability_rank(i)) = i;   % 稳定性排名
    ranking_matrix(2, monitoring_rank(i)) = i;  % 监测性能排名
    ranking_matrix(3, efficiency_rank(i)) = i;  % 效率排名
end

% 综合排名
[~, overall_rank] = sort(total_scores, 'descend');
for i = 1:4
    ranking_matrix(4, overall_rank(i)) = i;
end

% 绘制排名热图
imagesc(ranking_matrix);
colormap(flipud(hot));  % 翻转颜色映射，使1(最好)为最亮色
colorbar;
clim([1, 4]);

% 设置标签
set(gca, 'XTick', 1:4);
set(gca, 'XTickLabel', methods);
set(gca, 'YTick', 1:4);
set(gca, 'YTickLabel', {'字典稳定性', '监测性能', '计算效率', '综合排名'});

% 添加排名数字
for i = 1:4
    for j = 1:4
        if ranking_matrix(i,j) > 0
            text(j, i, sprintf('%d', ranking_matrix(i,j)), ...
                 'HorizontalAlignment', 'center', 'VerticalAlignment', 'middle', ...
                 'Color', 'black', 'FontSize', 16, 'FontWeight', 'bold');
        end
    end
end

title('四种方法各维度排名对比 (1=最佳, 4=最差)', 'FontSize', 16, 'FontWeight', 'bold');
xlabel('方法', 'FontSize', 14);
ylabel('评估维度', 'FontSize', 14);

% 保存排名图
savefig('methods_ranking_comparison.fig');
print('methods_ranking_comparison.png', '-dpng', '-r300');

fprintf('✓ 排名对比图已保存:\n');
fprintf('  - methods_ranking_comparison.fig\n');
fprintf('  - methods_ranking_comparison.png\n');

fprintf('\n🎉 所有可视化图表生成完成！\n');
fprintf('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
