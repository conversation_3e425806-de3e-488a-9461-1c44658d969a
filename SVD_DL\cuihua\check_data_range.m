%% 检查数据范围
addpath('../');

% 加载字典
load("Dlast.mat");
D_K = Dlast;
sparsity = 3;

% 加载数据
Y_train = [];
Y_test = [];

load("data_selected_F1.mat");Y_train=[Y_train;data_selected];
load("data_selected_F2.mat");Y_train=[Y_train;data_selected];
load("data_selected_F3.mat");Y_train=[Y_train;data_selected];

load("test_data_F1.mat");Y_test=[Y_test;test_data];
load("test_data_F2.mat");Y_test=[Y_test;test_data];
load("test_data_F3.mat");Y_test=[Y_test;test_data];

% 计算训练数据的R统计量
R_train = zeros(1, size(Y_train, 2));
for i = 1:size(Y_train, 2)
    y = Y_train(:, i);
    x = omp(D_K, y, sparsity);
    R_train(i) = norm(y - D_K*x, 2)^2;
end

% 计算控制限
R_limit = quantile(R_train, 0.99);

% 计算测试数据的R统计量
R_test = zeros(1, size(Y_test, 2));
for i = 1:size(Y_test, 2)
    y = Y_test(:, i);
    x = omp(D_K, y, sparsity);
    R_test(i) = norm(y - D_K*x, 2)^2;
end

% 显示数据范围
fprintf('训练数据R统计量范围: %.4e - %.4e\n', min(R_train), max(R_train));
fprintf('测试数据R统计量范围: %.4e - %.4e\n', min(R_test), max(R_test));
fprintf('控制限: %.4e\n', R_limit);
fprintf('超过控制限的测试样本数: %d / %d\n', sum(R_test > R_limit), length(R_test));

% 绘制简单的分布图
figure;
subplot(2,1,1);
histogram(R_train, 50);
title('训练数据R统计量分布');
xlabel('R统计量');
ylabel('频次');

subplot(2,1,2);
histogram(R_test, 50);
hold on;
xline(R_limit, '--r', 'LineWidth', 2);
title('测试数据R统计量分布');
xlabel('R统计量');
ylabel('频次');
legend('测试数据', '控制限');
