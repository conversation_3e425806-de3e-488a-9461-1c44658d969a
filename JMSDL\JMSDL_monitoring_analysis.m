%% JMSDL监测性能分析
% 基于最新算法的JMSDL方法监测性能评估

clc; clear; close all;
rng(42);

fprintf('========== JMSDL监测性能分析 ==========\n');

%% 1. 加载最终字典
fprintf('1. 加载JMSDL最终字典...\n');

if exist('D_final_JMSDL_updated.mat', 'file')
    load('D_final_JMSDL_updated.mat', 'D_n');
    D_K = D_n;
    fprintf('   ✓ 成功加载JMSDL更新版本最终字典\n');
    fprintf('   字典大小: %dx%d\n', size(D_K, 1), size(D_K, 2));
elseif exist('D_final_JMSDL.mat', 'file')
    load('D_final_JMSDL.mat', 'D_n');
    D_K = D_n;
    fprintf('   ✓ 成功加载JMSDL最终字典\n');
    fprintf('   字典大小: %dx%d\n', size(D_K, 1), size(D_K, 2));
else
    fprintf('   ❌ 未找到JMSDL字典，开始训练...\n');
    JMSDL_updated_analysis;
    load('D_final_JMSDL_updated.mat', 'D_n');
    D_K = D_n;
    fprintf('   ✓ JMSDL训练完成，字典大小: %dx%d\n', size(D_K, 1), size(D_K, 2));
end

sparsity = 2;  % 与训练时保持一致

%% 2. 加载训练和测试数据
fprintf('\n2. 加载训练和测试数据...\n');

Y_train = [];
Y_test = [];

% 加载训练数据
fprintf('   加载训练数据:\n');
for mode = 1:5
    load(sprintf('mode%d_train.mat', mode));
    Y_train = [Y_train, train_data'];
    fprintf('     Mode %d训练数据: %d样本\n', mode, size(train_data, 1));
end

% 加载测试数据
fprintf('   加载测试数据:\n');
for mode = 1:5
    % 尝试加载正常和故障测试数据
    normal_file = sprintf('mode%d_test_normal.mat', mode);
    fault_file = sprintf('mode%d_test_fault.mat', mode);
    
    if exist(normal_file, 'file') && exist(fault_file, 'file')
        load(normal_file);
        load(fault_file);
        Y_test = [Y_test, test_normal_data', test_fault_data'];
        fprintf('     Mode %d测试数据: %d正常 + %d故障\n', mode, ...
                size(test_normal_data, 1), size(test_fault_data, 1));
    else
        % 如果没有分离的正常/故障数据，尝试加载通用测试数据
        test_file = sprintf('mode%d_test.mat', mode);
        if exist(test_file, 'file')
            load(test_file);
            Y_test = [Y_test, test_data'];
            fprintf('     Mode %d测试数据: %d样本\n', mode, size(test_data, 1));
        else
            fprintf('     ⚠️  Mode %d测试数据未找到\n', mode);
        end
    end
end

fprintf('   总训练数据: %dx%d\n', size(Y_train, 1), size(Y_train, 2));
fprintf('   总测试数据: %dx%d\n', size(Y_test, 1), size(Y_test, 2));

%% 3. 训练数据R统计量计算
fprintf('\n3. 计算训练数据R统计量...\n');

R_train = zeros(1, size(Y_train, 2));
for i = 1:size(Y_train, 2)
    y = Y_train(:, i);
    x = omp(D_K, y, sparsity);
    R_train(i) = norm(y - D_K*x, 2)^2;
end

% 使用KDE估计控制限
alpha = 0.01;  % 99%置信度
[f_R, xi_R] = ksdensity(R_train, 'Function', 'cdf');
idx_R = find(f_R >= 1 - alpha, 1, 'first');
R_limit = xi_R(idx_R);

fprintf('   训练样本数: %d\n', length(R_train));
fprintf('   R统计量范围: [%.6f, %.6f]\n', min(R_train), max(R_train));
fprintf('   控制限 (99%%置信度): %.6f\n', R_limit);

%% 4. 测试数据R统计量计算
fprintf('\n4. 计算测试数据R统计量...\n');

R_test = zeros(1, size(Y_test, 2));
for i = 1:size(Y_test, 2)
    y = Y_test(:, i);
    x = omp(D_K, y, sparsity);
    R_test(i) = norm(y - D_K*x, 2)^2;
end

fprintf('   测试样本数: %d\n', length(R_test));
fprintf('   R统计量范围: [%.6f, %.6f]\n', min(R_test), max(R_test));

%% 5. 可视化监测结果
fprintf('\n5. 生成监测图表...\n');

figure('Position', [100, 100, 1200, 800]);

% 主监测图
subplot(2,2,[1,2]);
plot(R_test, 'b-', 'LineWidth', 1);
hold on;
yline(R_limit, '--r', 'LineWidth', 2);

% 标记不同模式的分界线
n_each = length(R_test) / 5;
for mode = 1:4
    xline(mode * n_each, '--g', sprintf('Mode%d|Mode%d', mode, mode+1), 'LineWidth', 1.5);
end

xlabel('样本编号', 'FontSize', 12);
ylabel('R统计量', 'FontSize', 12);
title('JMSDL过程监测 - R统计量与控制限', 'FontSize', 14);
legend('测试样本', '控制限 (99%)', 'Location', 'best');
grid on;

% 训练数据分布
subplot(2,2,3);
histogram(R_train, 50, 'Normalization', 'pdf', 'FaceAlpha', 0.7);
hold on;
xline(R_limit, '--r', 'LineWidth', 2);
xlabel('R统计量', 'FontSize', 12);
ylabel('概率密度', 'FontSize', 12);
title('训练数据R统计量分布', 'FontSize', 12);
legend('训练数据', '控制限', 'Location', 'best');
grid on;

% 测试数据分布
subplot(2,2,4);
histogram(R_test, 50, 'Normalization', 'pdf', 'FaceAlpha', 0.7);
hold on;
xline(R_limit, '--r', 'LineWidth', 2);
xlabel('R统计量', 'FontSize', 12);
ylabel('概率密度', 'FontSize', 12);
title('测试数据R统计量分布', 'FontSize', 12);
legend('测试数据', '控制限', 'Location', 'best');
grid on;

sgtitle('JMSDL方法 - 过程监测性能分析', 'FontSize', 16);

% 保存监测图
savefig('JMSDL_monitoring_performance.fig');
fprintf('   监测图已保存到: JMSDL_monitoring_performance.fig\n');

%% 6. 计算FAR和FDR
fprintf('\n6. 计算检测性能指标...\n');

% 假设每个模式的测试数据中，前半部分为正常，后半部分为故障
n_modes = 5;
n_each = length(R_test) / n_modes;
n_normal_each = floor(n_each / 2);
n_fault_each = n_each - n_normal_each;

FAR_all = zeros(n_modes, 1);
FDR_all = zeros(n_modes, 1);

for m = 1:n_modes
    idx_start = (m-1)*n_each + 1;
    idx_normal = idx_start : idx_start + n_normal_each - 1;
    idx_fault = idx_start + n_normal_each : idx_start + n_each - 1;
    
    FAR_all(m) = sum(R_test(idx_normal) > R_limit) / n_normal_each;
    FDR_all(m) = sum(R_test(idx_fault) > R_limit) / n_fault_each;
    
    fprintf('   Mode %d: 正常样本%d个, 故障样本%d个\n', m, n_normal_each, n_fault_each);
    fprintf('   Mode %d: FAR=%.4f, FDR=%.4f\n', m, FAR_all(m), FDR_all(m));
end

FAR_overall = mean(FAR_all);
FDR_overall = mean(FDR_all);

%% 7. 保存监测结果
fprintf('\n7. 保存监测结果...\n');

JMSDL_monitoring_results = struct();
JMSDL_monitoring_results.method = 'JMSDL_Updated';
JMSDL_monitoring_results.dictionary_size = size(D_K);
JMSDL_monitoring_results.sparsity = sparsity;
JMSDL_monitoring_results.R_limit = R_limit;
JMSDL_monitoring_results.R_train = R_train;
JMSDL_monitoring_results.R_test = R_test;
JMSDL_monitoring_results.FAR_all = FAR_all;
JMSDL_monitoring_results.FDR_all = FDR_all;
JMSDL_monitoring_results.FAR_overall = FAR_overall;
JMSDL_monitoring_results.FDR_overall = FDR_overall;
JMSDL_monitoring_results.alpha = alpha;

save('JMSDL_monitoring_results.mat', 'JMSDL_monitoring_results');
fprintf('   监测结果已保存到: JMSDL_monitoring_results.mat\n');

%% 8. 结果总结
fprintf('\n========== JMSDL监测性能总结 ==========\n');
fprintf('📊 基本信息:\n');
fprintf('   方法: JMSDL (更新版本)\n');
fprintf('   字典大小: %dx%d\n', size(D_K));
fprintf('   稀疏度: %d\n', sparsity);
fprintf('   置信度: %.0f%%\n', (1-alpha)*100);

fprintf('\n📈 各模式检测性能:\n');
fprintf('   模式    FAR      FDR\n');
fprintf('   ----   ------   ------\n');
for m = 1:n_modes
    fprintf('   %2d     %.4f   %.4f\n', m, FAR_all(m), FDR_all(m));
end

fprintf('\n🎯 总体性能:\n');
fprintf('   平均FAR = %.4f (误报率，越小越好)\n', FAR_overall);
fprintf('   平均FDR = %.4f (检出率，越大越好)\n', FDR_overall);

% 性能评估
if FAR_overall < 0.05 && FDR_overall > 0.8
    fprintf('   ✅ 检测性能优秀\n');
elseif FAR_overall < 0.1 && FDR_overall > 0.7
    fprintf('   ✅ 检测性能良好\n');
else
    fprintf('   ⚠️  检测性能需要改进\n');
end

fprintf('\n🎉 JMSDL监测性能分析完成！\n');
