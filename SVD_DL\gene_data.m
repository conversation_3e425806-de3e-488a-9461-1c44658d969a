rng(42); % 固定随机种子

A = [ 0.55  0.82  0.94;
      0.23  0.45  0.62;
     -0.61  0.62  0.41;
      0.49  0.79  0.89;
      0.89 -0.92  0.06;
      0.76  0.74  0.35;
      0.46  0.28  0.81;
     -0.02  0.41  0.01];

n_train = 1000;
n_test_normal = 500;
n_test_fault = 500;
n_modes = 5;

for mode = 1:n_modes
    % ---- 训练数据（正常） ----
    s1 = randn(n_train,1);         
    s2 = rand(n_train,1);          
    s3 = randn(n_train,1);         
    S_train = [s1 s2 s3];
    e_train = sqrt(0.01) * randn(n_train,8);
    train_data = S_train * A' + e_train;
    save(sprintf('mode%d_train.mat',mode), 'train_data');

    % ---- 测试数据（正常） ----
    s1 = randn(n_test_normal,1);
    s2 = rand(n_test_normal,1);
    s3 = randn(n_test_normal,1);
    S_test_normal = [s1 s2 s3];
    e_test_normal = sqrt(0.01) * randn(n_test_normal,8);
    test_normal_data = S_test_normal * A' + e_test_normal;
    save(sprintf('mode%d_test_normal.mat',mode), 'test_normal_data');

    % ---- 测试数据（带故障） ----
    s1 = randn(n_test_fault,1);
    s2 = rand(n_test_fault,1);
    s3 = randn(n_test_fault,1);
    S_test_fault = [s1 s2 s3];
    e_test_fault = sqrt(0.01) * randn(n_test_fault,8);
    test_fault_data = S_test_fault * A' + e_test_fault;
    test_fault_data(:,3) = test_fault_data(:,3) + 1.2; % y3加偏置
    save(sprintf('mode%d_test_fault.mat',mode), 'test_fault_data');
end

disp('各模式训练、正常测试、故障测试数据已分别保存为.mat文件。');
