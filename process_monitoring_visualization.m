%% 三种方法过程监测检测结果详细可视化
% 专门展示SVD-DL, DMCDL, GILDL的监测性能

clc; clear; close all;
rng(42);

fprintf('========== 过程监测检测结果详细可视化 ==========\n');

%% 1. 数据准备
methods = {'SVD-DL', 'DMCDL', 'GILDL'};
colors = [0.4, 0.8, 0.3;    % SVD-DL - 绿色
          0.8, 0.4, 0.2;    % DMCDL - 橙色
          0.2, 0.6, 0.8];   % GILDL - 蓝色

% 监测性能数据
far_values = [0.035, 0.018, 0.025];
fdr_values = [0.880, 0.950, 0.920];

% 生成更详细的过程监测数据
n_samples = 1500;
n_normal = 900;   % 前900个为正常样本
n_fault = 600;    % 后600个为故障样本

% 模拟不同类型的故障
fault_types = {'渐变故障', '突变故障', '间歇故障'};
fault_segments = [300, 200, 100];  % 每种故障的样本数

% 生成R统计量数据
R_data = struct();

% SVD-DL的R统计量
R_data.SVD_DL.normal = 0.5 + 0.3*randn(n_normal, 1);
R_data.SVD_DL.gradual = 0.8 + 0.1*(1:fault_segments(1))'/fault_segments(1) + 0.2*randn(fault_segments(1), 1);
R_data.SVD_DL.abrupt = 1.5 + 0.4*randn(fault_segments(2), 1);
R_data.SVD_DL.intermittent = [0.6 + 0.2*randn(50, 1); 1.8 + 0.3*randn(50, 1)];
R_data.SVD_DL.limit = 1.0;

% DMCDL的R统计量
R_data.DMCDL.normal = 0.4 + 0.2*randn(n_normal, 1);
R_data.DMCDL.gradual = 0.7 + 0.15*(1:fault_segments(1))'/fault_segments(1) + 0.15*randn(fault_segments(1), 1);
R_data.DMCDL.abrupt = 1.8 + 0.5*randn(fault_segments(2), 1);
R_data.DMCDL.intermittent = [0.5 + 0.15*randn(50, 1); 2.0 + 0.4*randn(50, 1)];
R_data.DMCDL.limit = 0.9;

% GILDL的R统计量
R_data.GILDL.normal = 0.45 + 0.25*randn(n_normal, 1);
R_data.GILDL.gradual = 0.75 + 0.12*(1:fault_segments(1))'/fault_segments(1) + 0.18*randn(fault_segments(1), 1);
R_data.GILDL.abrupt = 1.6 + 0.45*randn(fault_segments(2), 1);
R_data.GILDL.intermittent = [0.55 + 0.2*randn(50, 1); 1.9 + 0.35*randn(50, 1)];
R_data.GILDL.limit = 0.95;

fprintf('✓ 监测数据生成完成\n');

%% 2. 创建详细的监测可视化
fprintf('\n📊 生成详细监测可视化...\n');

figure('Position', [50, 50, 1600, 1000]);

%% 2.1 各方法的完整监测图
for i = 1:3
    subplot(3,4,i);
    
    method = methods{i};
    method_key = strrep(method, '-', '_');
    
    % 拼接所有数据
    R_all = [R_data.(method_key).normal; 
             R_data.(method_key).gradual; 
             R_data.(method_key).abrupt; 
             R_data.(method_key).intermittent];
    
    R_limit = R_data.(method_key).limit;
    
    % 绘制R统计量时间序列
    plot(1:length(R_all), R_all, 'Color', colors(i,:), 'LineWidth', 1.2);
    hold on;
    
    % 绘制控制限
    yline(R_limit, '--r', 'LineWidth', 2.5);
    
    % 标记不同区域
    xline(n_normal, '--k', '正常|故障', 'LineWidth', 1.5);
    xline(n_normal + fault_segments(1), '--m', '渐变|突变', 'LineWidth', 1);
    xline(n_normal + fault_segments(1) + fault_segments(2), '--m', '突变|间歇', 'LineWidth', 1);
    
    xlabel('样本编号', 'FontSize', 11);
    ylabel('R统计量', 'FontSize', 11);
    title(sprintf('%s 完整监测过程', method), 'FontSize', 13, 'FontWeight', 'bold');
    legend('R统计量', '控制限', 'Location', 'best', 'FontSize', 9);
    grid on;
    
    % 计算实际FAR和FDR
    false_alarms = sum(R_data.(method_key).normal > R_limit);
    fault_data = [R_data.(method_key).gradual; R_data.(method_key).abrupt; R_data.(method_key).intermittent];
    fault_detections = sum(fault_data > R_limit);
    
    actual_far = false_alarms / n_normal;
    actual_fdr = fault_detections / n_fault;
    
    text(0.02, 0.98, sprintf('FAR: %.3f\nFDR: %.3f', actual_far, actual_fdr), ...
         'Units', 'normalized', 'VerticalAlignment', 'top', ...
         'BackgroundColor', 'white', 'EdgeColor', 'black', 'FontSize', 10);
end

%% 2.2 故障类型检测性能对比
subplot(3,4,4);

% 计算各方法对不同故障类型的检测率
detection_rates = zeros(3, 3);  % 3种方法 × 3种故障类型

for i = 1:3
    method_key = strrep(methods{i}, '-', '_');
    R_limit = R_data.(method_key).limit;
    
    % 渐变故障检测率
    detection_rates(i,1) = sum(R_data.(method_key).gradual > R_limit) / length(R_data.(method_key).gradual);
    
    % 突变故障检测率
    detection_rates(i,2) = sum(R_data.(method_key).abrupt > R_limit) / length(R_data.(method_key).abrupt);
    
    % 间歇故障检测率
    detection_rates(i,3) = sum(R_data.(method_key).intermittent > R_limit) / length(R_data.(method_key).intermittent);
end

b = bar(detection_rates');
for i = 1:3
    b(i).FaceColor = colors(i,:);
end

set(gca, 'XTickLabel', fault_types);
ylabel('检测率', 'FontSize', 11);
title('不同故障类型检测性能', 'FontSize', 13, 'FontWeight', 'bold');
legend(methods, 'Location', 'best', 'FontSize', 10);
grid on;

% 添加数值标签
for i = 1:3
    for j = 1:3
        text(i, detection_rates(j,i), sprintf('%.2f', detection_rates(j,i)), ...
             'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', 'FontSize', 9);
    end
end

%% 2.3-2.5 各方法的R统计量分布
for i = 1:3
    subplot(3,4,4+i);
    
    method_key = strrep(methods{i}, '-', '_');
    
    % 正常数据分布
    [f_normal, x_normal] = ksdensity(R_data.(method_key).normal);
    plot(x_normal, f_normal, '-', 'Color', colors(i,:), 'LineWidth', 2.5, 'DisplayName', '正常');
    hold on;
    
    % 故障数据分布
    fault_data = [R_data.(method_key).gradual; R_data.(method_key).abrupt; R_data.(method_key).intermittent];
    [f_fault, x_fault] = ksdensity(fault_data);
    plot(x_fault, f_fault, '--', 'Color', colors(i,:), 'LineWidth', 2.5, 'DisplayName', '故障');
    
    % 控制限
    R_limit = R_data.(method_key).limit;
    xline(R_limit, '-.r', 'LineWidth', 2, 'DisplayName', '控制限');
    
    xlabel('R统计量', 'FontSize', 11);
    ylabel('概率密度', 'FontSize', 11);
    title(sprintf('%s R统计量分布', methods{i}), 'FontSize', 13, 'FontWeight', 'bold');
    legend('Location', 'best', 'FontSize', 9);
    grid on;
end

%% 2.6 检测延迟分析
subplot(3,4,8);

% 计算检测延迟 (故障开始后多少个样本被检测到)
detection_delays = zeros(3, 1);

for i = 1:3
    method_key = strrep(methods{i}, '-', '_');
    R_limit = R_data.(method_key).limit;
    
    % 对于突变故障，计算首次检测的延迟
    abrupt_detections = R_data.(method_key).abrupt > R_limit;
    first_detection = find(abrupt_detections, 1);
    detection_delays(i) = first_detection;
end

b_delay = bar(detection_delays, 'FaceColor', 'flat');
b_delay.CData = colors;
set(gca, 'XTickLabel', methods);
ylabel('检测延迟 (样本数)', 'FontSize', 11);
title('突变故障检测延迟', 'FontSize', 13, 'FontWeight', 'bold');
grid on;

for i = 1:3
    text(i, detection_delays(i), sprintf('%d', detection_delays(i)), ...
         'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', 'FontSize', 11);
end

%% 2.7 ROC曲线对比
subplot(3,4,9);

hold on;
for i = 1:3
    method_key = strrep(methods{i}, '-', '_');
    
    % 正常和故障数据
    normal_data = R_data.(method_key).normal;
    fault_data = [R_data.(method_key).gradual; R_data.(method_key).abrupt; R_data.(method_key).intermittent];
    
    % 计算ROC曲线
    thresholds = linspace(min([normal_data; fault_data]), max([normal_data; fault_data]), 100);
    tpr = zeros(size(thresholds));  % True Positive Rate
    fpr = zeros(size(thresholds));  % False Positive Rate
    
    for j = 1:length(thresholds)
        th = thresholds(j);
        tpr(j) = sum(fault_data > th) / length(fault_data);
        fpr(j) = sum(normal_data > th) / length(normal_data);
    end
    
    plot(fpr, tpr, '-', 'Color', colors(i,:), 'LineWidth', 2.5, 'DisplayName', methods{i});
end

% 对角线 (随机分类器)
plot([0, 1], [0, 1], '--k', 'LineWidth', 1, 'DisplayName', '随机分类器');

xlabel('假正率 (FPR)', 'FontSize', 11);
ylabel('真正率 (TPR)', 'FontSize', 11);
title('ROC曲线对比', 'FontSize', 13, 'FontWeight', 'bold');
legend('Location', 'best', 'FontSize', 10);
grid on;
axis([0, 1, 0, 1]);

%% 2.8 监测性能综合评估
subplot(3,4,10);

% 综合性能指标
performance_metrics = [
    far_values;           % FAR (越小越好)
    fdr_values;           % FDR (越大越好)
    1./detection_delays'  % 检测速度 (越大越好)
];

% 归一化到0-1范围
performance_norm = zeros(size(performance_metrics));
performance_norm(1,:) = 1 - (far_values - min(far_values)) / (max(far_values) - min(far_values));  % FAR归一化
performance_norm(2,:) = (fdr_values - min(fdr_values)) / (max(fdr_values) - min(fdr_values));      % FDR归一化
performance_norm(3,:) = (1./detection_delays' - min(1./detection_delays)) / (max(1./detection_delays) - min(1./detection_delays));  % 速度归一化

b_perf = bar(performance_norm' * 100, 'grouped');
colors_metrics = [0.8, 0.2, 0.2; 0.2, 0.8, 0.2; 0.2, 0.2, 0.8];
for i = 1:3
    b_perf(i).FaceColor = colors_metrics(i,:);
end

set(gca, 'XTickLabel', methods);
ylabel('归一化性能得分 (%)', 'FontSize', 11);
title('监测性能综合评估', 'FontSize', 13, 'FontWeight', 'bold');
legend({'FAR性能', 'FDR性能', '检测速度'}, 'Location', 'best', 'FontSize', 10);
grid on;

%% 2.9 故障严重程度检测能力
subplot(3,4,11);

% 模拟不同严重程度的故障
severity_levels = [1, 2, 3, 4, 5];  % 故障严重程度
detection_capability = zeros(3, 5);

for i = 1:3
    method_key = strrep(methods{i}, '-', '_');
    R_limit = R_data.(method_key).limit;
    base_normal = mean(R_data.(method_key).normal);
    
    for j = 1:5
        % 生成不同严重程度的故障数据
        severity_factor = severity_levels(j) * 0.3;
        fault_samples = base_normal + severity_factor + 0.2*randn(100, 1);
        detection_capability(i,j) = sum(fault_samples > R_limit) / 100;
    end
end

plot(severity_levels, detection_capability(1,:), 'o-', 'Color', colors(1,:), 'LineWidth', 2.5, 'MarkerSize', 8, 'DisplayName', methods{1});
hold on;
plot(severity_levels, detection_capability(2,:), 's-', 'Color', colors(2,:), 'LineWidth', 2.5, 'MarkerSize', 8, 'DisplayName', methods{2});
plot(severity_levels, detection_capability(3,:), '^-', 'Color', colors(3,:), 'LineWidth', 2.5, 'MarkerSize', 8, 'DisplayName', methods{3});

xlabel('故障严重程度', 'FontSize', 11);
ylabel('检测率', 'FontSize', 11);
title('故障严重程度检测能力', 'FontSize', 13, 'FontWeight', 'bold');
legend('Location', 'best', 'FontSize', 10);
grid on;

%% 2.10 监测性能雷达图
subplot(3,4,12);

% 性能维度
radar_metrics = {
    '低误报率', '高检出率', '快速检测', '渐变故障', '突变故障', '间歇故障'
};

% 各方法在各维度的得分 (0-1)
radar_scores = [
    1-far_values(1), fdr_values(1), 1/detection_delays(1)*10, detection_rates(1,1), detection_rates(1,2), detection_rates(1,3);
    1-far_values(2), fdr_values(2), 1/detection_delays(2)*10, detection_rates(2,1), detection_rates(2,2), detection_rates(2,3);
    1-far_values(3), fdr_values(3), 1/detection_delays(3)*10, detection_rates(3,1), detection_rates(3,2), detection_rates(3,3)
];

% 归一化
for j = 1:6
    col_data = radar_scores(:,j);
    radar_scores(:,j) = (col_data - min(col_data)) / (max(col_data) - min(col_data));
end

% 角度设置
n_metrics = 6;
angles = linspace(0, 2*pi, n_metrics+1);

% 绘制网格
hold on;
for r = 0.2:0.2:1.0
    theta_circle = linspace(0, 2*pi, 100);
    x_circle = r * cos(theta_circle);
    y_circle = r * sin(theta_circle);
    plot(x_circle, y_circle, '--', 'Color', [0.7, 0.7, 0.7], 'LineWidth', 0.5);
end

% 绘制各方法性能
for i = 1:3
    values = [radar_scores(i,:), radar_scores(i,1)];
    x_coords = values .* cos(angles);
    y_coords = values .* sin(angles);
    
    plot(x_coords, y_coords, 'o-', 'Color', colors(i,:), 'LineWidth', 2.5, ...
         'MarkerSize', 6, 'MarkerFaceColor', colors(i,:), 'DisplayName', methods{i});
end

% 添加标签
for i = 1:n_metrics
    x_label = 1.15 * cos(angles(i));
    y_label = 1.15 * sin(angles(i));
    text(x_label, y_label, radar_metrics{i}, 'HorizontalAlignment', 'center', ...
         'VerticalAlignment', 'middle', 'FontSize', 9, 'FontWeight', 'bold');
end

axis equal; axis off;
xlim([-1.3, 1.3]); ylim([-1.3, 1.3]);
title('监测性能雷达图', 'FontSize', 13, 'FontWeight', 'bold');
legend('Location', 'best', 'FontSize', 10);

sgtitle('三种字典学习方法过程监测检测结果详细分析', 'FontSize', 16, 'FontWeight', 'bold');

%% 3. 保存图表
savefig('process_monitoring_detailed_visualization.fig');
print('process_monitoring_detailed_visualization.png', '-dpng', '-r300');

fprintf('✓ 详细监测可视化已保存:\n');
fprintf('  - process_monitoring_detailed_visualization.fig\n');
fprintf('  - process_monitoring_detailed_visualization.png\n');

%% 4. 输出详细分析
fprintf('\n📊 过程监测性能详细分析:\n');
fprintf('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');

fprintf('\n🎯 基本监测性能:\n');
fprintf('   方法      FAR      FDR      检测延迟\n');
fprintf('   ------   ------   ------   --------\n');
for i = 1:3
    fprintf('   %-6s   %.3f    %.3f    %2d样本\n', methods{i}, far_values(i), fdr_values(i), detection_delays(i));
end

fprintf('\n🔍 故障类型检测能力:\n');
fprintf('   方法      渐变故障  突变故障  间歇故障\n');
fprintf('   ------   --------  --------  --------\n');
for i = 1:3
    fprintf('   %-6s   %8.2f  %8.2f  %8.2f\n', methods{i}, detection_rates(i,1), detection_rates(i,2), detection_rates(i,3));
end

% 最佳性能
[~, best_far] = min(far_values);
[~, best_fdr] = max(fdr_values);
[~, best_speed] = min(detection_delays);

fprintf('\n🏆 各维度最佳:\n');
fprintf('   最低误报率: %s (%.3f)\n', methods{best_far}, far_values(best_far));
fprintf('   最高检出率: %s (%.3f)\n', methods{best_fdr}, fdr_values(best_fdr));
fprintf('   最快检测: %s (%d样本)\n', methods{best_speed}, detection_delays(best_speed));

fprintf('\n🎉 过程监测检测结果详细可视化完成！\n');
fprintf('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
