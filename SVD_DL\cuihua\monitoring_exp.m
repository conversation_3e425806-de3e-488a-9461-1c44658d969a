%% ========== 1. 加载最终融合字典 ==========
%load('最终融合字典.mat', 'Dictionary_history'); % 假定你有这个文件
load("Dlast_cuihua_SVD.mat");
D_K = Dlast;                 % [8, n_atoms]
sparsity = 3; 

%% ========== 2. 拼接1~5 mode训练/测试数据 ==========
Y_train = [];
Y_test = [];

load("data_selected_F1.mat");Y_train=[Y_train;data_selected];
load("data_selected_F2.mat");Y_train=[Y_train;data_selected];
load("data_selected_F3.mat");Y_train=[Y_train;data_selected];
%load("data_selected_F4.mat");Y_train=[Y_train;data_selected];

load("test_data_F1.mat");Y_test=[Y_test;test_data];
load("test_data_F2.mat");Y_test=[Y_test;test_data];
load("test_data_F3.mat");Y_test=[Y_test;test_data];
%load("test_data_F4.mat");Y_test=[Y_test;test_data];

Y_train = Y_train';          % [特征, 总样本数]
Y_test = Y_test';

Y_train=cell2mat(Y_train);
Y_test=cell2mat(Y_test);

%% ========== 3. 训练数据：OMP编码+R统计量 ==========
R_train = zeros(1, size(Y_train,2));
for i = 1:size(Y_train,2)
    y = Y_train(:,i);
    x = omp(D_K, y, sparsity);
    R_train(i) = norm(y - D_K*x, 2)^2;
end

    % 使用KDE估计统计量的分布并计算控制限
    [f_R, xi_R] = ksdensity(R_train,  'Function', 'cdf');
    % 找到 1-alpha 分位数
    idx_R = find(f_R >= 1 - 0.01, 1, 'first');
    R_limit= xi_R(idx_R);
%% ========== 4. 测试集：OMP编码+R统计量 =========

R_test = zeros(1, size(Y_test,2));
for i = 1:size(Y_test,2)
    y = Y_test(:,i);
    x = omp(D_K, y, sparsity);
    R_test(i) = norm(y - D_K*x, 2)^2;
end

%% ========== 5. 画统计量与控制限 ==========
figure;
plot(R_test, 'b','LineWidth',2);
yline(R_limit, '--k','LineWidth',2,'Color','r');%设置x轴的范围为0-3000
xlim([0, 3003]);
legend('Statistics','Control Limit');
xlabel('Samples'); ylabel('R');
set(gca,'YScale','log','FontSize',14,'FontName','Times New Roman','FontWeight','bold');

%% ========== 6. 计算FAR/FDR ==========
n_test = size(Y_test,2);
n_mode = 3;
n_normal_each = 500; % 假设每个test_normal_data样本数一致
n_fault_each = 500;   % 假设每个test_fault_data样本数一致
n_normal = n_mode * n_normal_each;
n_fault  = n_mode * n_fault_each;

R_test_normal = R_test(1:n_normal);
R_test_fault  = R_test(n_normal+1 : n_normal+n_fault);

FAR = sum(R_test_normal > R_limit) / n_normal;
FDR = sum(R_test_fault  > R_limit) / n_fault;
fprintf('FAR=%.3f, FDR=%.3f\n', FAR, FDR);

