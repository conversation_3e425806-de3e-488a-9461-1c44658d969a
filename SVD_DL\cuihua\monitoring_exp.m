%% ========== 0. 添加必要路径 ==========
addpath('../');  % 添加SVD_DL目录到路径，包含omp函数

%% ========== 1. 加载最终融合字典 ==========
%load('最终融合字典.mat', 'Dictionary_history'); % 假定你有这个文件
load("Dlast.mat");  % 使用当前目录中的文件
D_K = Dlast;                 % [8, n_atoms]
sparsity = 3;

%% ========== 2. 拼接1~5 mode训练/测试数据 ==========
Y_train = [];
Y_test = [];

load("data_selected_F1.mat");Y_train=[Y_train;data_selected];
load("data_selected_F2.mat");Y_train=[Y_train;data_selected];
load("data_selected_F3.mat");Y_train=[Y_train;data_selected];
%load("data_selected_F4.mat");Y_train=[Y_train;data_selected];

load("test_data_F1.mat");Y_test=[Y_test;test_data];
load("test_data_F2.mat");Y_test=[Y_test;test_data];
load("test_data_F3.mat");Y_test=[Y_test;test_data];
%load("test_data_F4.mat");Y_test=[Y_test;test_data];

Y_train = Y_train';          % [特征, 总样本数]
Y_test = Y_test';

Y_train=cell2mat(Y_train);
Y_test=cell2mat(Y_test);

%% ========== 3. 训练数据：OMP编码+R统计量 ==========
R_train = zeros(1, size(Y_train,2));
for i = 1:size(Y_train,2)
    y = Y_train(:,i);
    x = omp(D_K, y, sparsity);
    R_train(i) = norm(y - D_K*x, 2)^2;
end

    % 使用KDE估计统计量的分布并计算控制限
    [f_R, xi_R] = ksdensity(R_train,  'Function', 'cdf');
    % 找到 1-alpha 分位数
    idx_R = find(f_R >= 1 - 0.01, 1, 'first');
    R_limit= xi_R(idx_R);
%% ========== 4. 测试集：OMP编码+R统计量 =========

R_test = zeros(1, size(Y_test,2));
for i = 1:size(Y_test,2)
    y = Y_test(:,i);
    x = omp(D_K, y, sparsity);
    R_test(i) = norm(y - D_K*x, 2)^2;
end

%% ========== 5. 画统计量与控制限 (带纵轴截断) ==========

% 首先查看数据范围
R_max = max(R_test);
R_min = min(R_test);
fprintf('R统计量范围: %.4e - %.4e\n', R_min, R_max);
fprintf('控制限: %.4e\n', R_limit);

%% 纵轴截断参数设置 (根据实际数据调整)
% 设置截断范围为数据范围的中间部分
data_range = R_max - R_min;
y_break_start = R_min + 0.2 * data_range;  % 截断开始：最小值 + 20%范围
y_break_end = R_max - 0.2 * data_range;    % 截断结束：最大值 - 20%范围

% 如果数据范围太小，使用固定的截断范围
if data_range < 1000
    y_break_start = 50;
    y_break_end = R_max - 100;
end

fprintf('截断范围: %.4e - %.4e\n', y_break_start, y_break_end);

% 计算截断调整参数
y_interval = data_range / 10;  % 纵坐标刻度间隔
adjust_value = 0.4 * y_interval;  % 微调截断处y坐标
update_num = y_break_end - y_break_start - y_interval;  % 最高处曲线向下平移大小

% 处理R_test数据：超过截断结束位置的数据向下平移
R_test_adjusted = R_test;
R_test_adjusted(R_test > y_break_end) = R_test(R_test > y_break_end) - update_num;

% 处理控制限：如果控制限在截断范围内也需要调整
R_limit_adjusted = R_limit;
if R_limit > y_break_end
    R_limit_adjusted = R_limit - update_num;
end

%% 绘图
figure('Position', [100, 100, 1000, 600]);
plot(R_test_adjusted, 'b', 'LineWidth', 2);
hold on;
yline(R_limit_adjusted, '--r', 'LineWidth', 2);

% 设置坐标轴
xlim([0, 3000]);
xlabel('Samples', 'FontSize', 12, 'FontName', 'Times New Roman');
ylabel('R Statistics', 'FontSize', 12, 'FontName', 'Times New Roman');
title('Process Monitoring with Axis Break', 'FontSize', 14, 'FontWeight', 'bold');

% 添加截断标记
ylimit = get(gca, 'ylim');
location_Y = (y_break_start + adjust_value - ylimit(1)) / diff(ylimit);

% 在左右两侧添加截断符号
t1 = text(0, location_Y, '//', 'Units', 'normalized', 'BackgroundColor', 'w', ...
          'margin', eps, 'fontsize', 13, 'HorizontalAlignment', 'left');
set(t1, 'rotation', 90);

t2 = text(1, location_Y, '//', 'Units', 'normalized', 'BackgroundColor', 'w', ...
          'margin', eps, 'fontsize', 13, 'HorizontalAlignment', 'right');
set(t2, 'rotation', 90);

% 重新定义纵坐标刻度
y_tick_values = [0:y_interval:y_break_start, (y_break_end+y_interval):y_interval:R_max];
y_tick_positions = y_tick_values;
y_tick_positions(y_tick_values > y_break_start + eps) = ...
    y_tick_positions(y_tick_values > y_break_start + eps) - update_num;

% 设置刻度标签
y_tick_labels = cell(1, length(y_tick_values));
for i = 1:length(y_tick_values)
    if y_tick_values(i) >= 1e6
        y_tick_labels{i} = sprintf('%.1e', y_tick_values(i));
    else
        y_tick_labels{i} = sprintf('%.0f', y_tick_values(i));
    end
end

set(gca, 'ytick', y_tick_positions);
set(gca, 'yTickLabel', y_tick_labels, 'FontSize', 12, 'FontName', 'Times New Roman');

% 图例
legend('R Statistics', 'Control Limit', 'Location', 'best', 'FontSize', 12);

% 设置图形属性
set(gcf, 'color', 'w');
set(gca, 'Layer', 'top', 'FontSize', 12, 'Fontname', 'Times New Roman');

% 保存图片
saveas(gcf, 'SVD_DL_Monitoring_with_Break.png', 'png');
saveas(gcf, 'SVD_DL_Monitoring_with_Break.fig', 'fig');

fprintf('监测图表已保存 (带纵轴截断)\n');
fprintf('截断范围: %.1e - %.1e\n', y_break_start, y_break_end);
fprintf('控制限: %.4e (原始: %.4e)\n', R_limit_adjusted, R_limit);


% %% ========== 6. 计算FAR/FDR ==========
% n_test = size(Y_test,2);
% n_mode = 5;
% n_normal_each = size(test_normal_data,1); % 假设每个test_normal_data样本数一致
% n_fault_each = size(test_fault_data,1);   % 假设每个test_fault_data样本数一致
% n_normal = n_mode * n_normal_each;
% n_fault  = n_mode * n_fault_each;
% 
% R_test_normal = R_test(1:n_normal);
% R_test_fault  = R_test(n_normal+1 : n_normal+n_fault);
% 
% FAR = sum(R_test_normal > R_limit) / n_normal;
% FDR = sum(R_test_fault  > R_limit) / n_fault;
% fprintf('FAR=%.3f, FDR=%.3f\n', FAR, FDR);

