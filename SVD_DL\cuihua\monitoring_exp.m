%% ========== 1. 加载最终融合字典 ==========
%load('最终融合字典.mat', 'Dictionary_history'); % 假定你有这个文件
load("Dlast_cuihua_SVD.mat");
D_K = Dlast;                 % [8, n_atoms]
sparsity = 3; 

%% ========== 2. 拼接1~5 mode训练/测试数据 ==========
Y_train = [];
Y_test = [];

load("data_selected_F1.mat");Y_train=[Y_train;data_selected];
load("data_selected_F2.mat");Y_train=[Y_train;data_selected];
load("data_selected_F3.mat");Y_train=[Y_train;data_selected];
%load("data_selected_F4.mat");Y_train=[Y_train;data_selected];

load("test_data_F1.mat");Y_test=[Y_test;test_data];
load("test_data_F2.mat");Y_test=[Y_test;test_data];
load("test_data_F3.mat");Y_test=[Y_test;test_data];
%load("test_data_F4.mat");Y_test=[Y_test;test_data];

Y_train = Y_train';          % [特征, 总样本数]
Y_test = Y_test';

Y_train=cell2mat(Y_train);
Y_test=cell2mat(Y_test);

%% ========== 3. 训练数据：OMP编码+R统计量 ==========
R_train = zeros(1, size(Y_train,2));
for i = 1:size(Y_train,2)
    y = Y_train(:,i);
    x = omp(D_K, y, sparsity);
    R_train(i) = norm(y - D_K*x, 2)^2;
end

    % 使用KDE估计统计量的分布并计算控制限
    [f_R, xi_R] = ksdensity(R_train,  'Function', 'cdf');
    % 找到 1-alpha 分位数
    idx_R = find(f_R >= 1 - 0.01, 1, 'first');
    R_limit= xi_R(idx_R);
%% ========== 4. 测试集：OMP编码+R统计量 =========

R_test = zeros(1, size(Y_test,2));
for i = 1:size(Y_test,2)
    y = Y_test(:,i);
    x = omp(D_K, y, sparsity);
    R_test(i) = norm(y - D_K*x, 2)^2;
end

%% ========== 5. 画统计量与控制限（带 Y 轴断层：6e5–2e7） ==========
% ---------- 断轴参数 ----------
y_break_start = 6e5;          % 断层起点
y_break_end   = 2e7;          % 断层终点
gap           = y_break_end - y_break_start;   % 被“挤掉”的高度

% ---------- 1) 压缩数据 ----------
R_plot = R_test;                         % 拷贝一份要绘制的数据
idx_big = R_plot > y_break_start;        % 大于断层起点的点
R_plot(idx_big) = R_plot(idx_big) - gap; % 整体下移 gap

% 控制限也要相同处理
if R_limit > y_break_start
    R_limit_plot = R_limit - gap;
else
    R_limit_plot = R_limit;
end

% ---------- 2) 绘图 ----------
figure; hold on;
plot(R_plot,'b','LineWidth',1.5);                % 统计量
yline(R_limit_plot,'--r','LineWidth',1.5);       % 控制限
xlim([1, numel(R_plot)]);
xlabel('Samples','FontSize',12,'FontWeight','bold');
ylabel('R','FontSize',12,'FontWeight','bold');
legend('Statistics','Control Limit','Location','northeast','Box','off');

% ---------- 3) 构造新的 Y 刻度 ----------
%   下半段：0 ~ y_break_start（你可以自己决定刻度间隔）
ylow  = 0 : 1e5 : y_break_start;                 % 0,1e5,2e5,…,6e5
%   上半段：y_break_end ~ max(R_test)
yhigh = linspace(y_break_end, max(R_test), 4);   % 4 个刻度示例
%   把上半段对应到“压缩后坐标”
yticks_new = [ylow, yhigh - gap];
yticklabels_new = [ ...
    arrayfun(@(v)sprintf('%.0e',v), ylow ,'UniformOutput',false), ...
    arrayfun(@(v)sprintf('%.1e',v), yhigh,'UniformOutput',false) ...
];
set(gca,'YTick',yticks_new,'YTickLabel',yticklabels_new, ...
        'FontName','Times New Roman','FontSize',11);

% ---------- 4) 画“//”断层符号 ----------
%   先计算断层在当前坐标系中的归一化 y 位置
ylims = ylim;
normY = (y_break_start - ylims(1)) / diff(ylims);   % 0–1 之间
%   在左侧(0.01) & 右侧(0.05)各放一组斜杠
for xx = [0.01, 0.05]
    text(xx, normY,  '//', 'Units','normalized', ...
         'FontSize',14,'FontWeight','bold','HorizontalAlignment','center');
end
grid on; box off;
title('R Statistics (broken Y axis)','FontSize',14,'FontWeight','bold');


% %% ========== 6. 计算FAR/FDR ==========
% n_test = size(Y_test,2);
% n_mode = 5;
% n_normal_each = size(test_normal_data,1); % 假设每个test_normal_data样本数一致
% n_fault_each = size(test_fault_data,1);   % 假设每个test_fault_data样本数一致
% n_normal = n_mode * n_normal_each;
% n_fault  = n_mode * n_fault_each;
% 
% R_test_normal = R_test(1:n_normal);
% R_test_fault  = R_test(n_normal+1 : n_normal+n_fault);
% 
% FAR = sum(R_test_normal > R_limit) / n_normal;
% FDR = sum(R_test_fault  > R_limit) / n_fault;
% fprintf('FAR=%.3f, FDR=%.3f\n', FAR, FDR);

