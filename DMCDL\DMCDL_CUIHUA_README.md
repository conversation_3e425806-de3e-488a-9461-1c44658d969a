# DMCDL催化数据集应用

## 🎯 项目目标

基于完整的DMCDL脚本，将训练数据替换为催化数据集 (data_selected_F1~F3)，实现DMCDL方法在催化工况数据上的字典学习和监测性能分析。

## 📊 数据集信息

### 训练数据
- `data_selected_F1.mat`: F1催化工况训练数据
- `data_selected_F2.mat`: F2催化工况训练数据  
- `data_selected_F3.mat`: F3催化工况训练数据

### 测试数据
- `test_data_F1.mat`: F1催化工况测试数据
- `test_data_F2.mat`: F2催化工况测试数据
- `test_data_F3.mat`: F3催化工况测试数据

## 🔧 核心文件

### 1. `cuihua_learn_D_dmcdl.m` ⭐**字典学习**⭐

**功能**: 使用DMCDL方法在催化数据集上进行字典学习

**核心流程**:
```matlab
% Mode 1: F1数据初始建模
[D, X, w, W] = dmcdl_initial(Y1, n_atoms, sparsity, n_iter, lambda_1, zeta);
[Rtr, Yh] = build_threshold_and_memory(D, X, Y1, M);

% Mode 2: F2数据增量学习
Y_concat = [Y2, Yh];  % 双重记忆拼接
[D, X, W, w] = dmcdl_incremental(D, W, Y_concat, ...);

% Mode 3: F3数据增量学习
Y_concat = [Y3, Yh];  % 双重记忆拼接
[D, X, W, w] = dmcdl_incremental(D, W, Y_concat, ...);
```

**输出**:
- `D_final_cuihua_dmcdl.mat`: 最终融合字典
- `cuihua_dmcdl_results.mat`: 完整演化历史
- `dmcdl_cuihua_analysis.fig`: 字典演化可视化

### 2. `monitoring_cuihua_exp_dmcdl.m` ⭐**监测性能**⭐

**功能**: 使用DMCDL最终字典进行催化数据集监测性能评估

**核心流程**:
```matlab
% 1. 加载最终字典
load('D_final_cuihua_dmcdl.mat', 'D_final_cuihua');

% 2. 拼接训练/测试数据
Y_train = [F1_train; F2_train; F3_train];
Y_test = [F1_test; F2_test; F3_test];

% 3. 计算R统计量
R = norm(y - D_K*x, 2)^2;

% 4. 计算FAR/FDR
FAR = 误报率, FDR = 检出率
```

**输出**:
- `dmcdl_cuihua_monitoring_results.mat`: 监测性能数据
- `dmcdl_cuihua_monitoring.fig`: 监测性能图表
- `cuihua_methods_comparison.fig`: 与GILDL方法比较

### 3. `run_dmcdl_cuihua_complete.m` ⭐**一键运行**⭐

**功能**: 完整的DMCDL催化数据集分析流程

**特性**:
- 自动文件检查
- 集成学习和监测分析
- 生成综合报告
- 完整的错误处理

## 🚀 使用方法

### 快速开始
```matlab
cd('DMCDL')
run('run_dmcdl_cuihua_complete.m')
```

### 分步执行
```matlab
% 1. 字典学习
cuihua_learn_D_dmcdl

% 2. 监测性能分析
monitoring_cuihua_exp_dmcdl

% 3. 查看结果
load('dmcdl_cuihua_comprehensive_results.mat')
```

### 数据要求
确保DMCDL目录包含：
```
DMCDL/
├── data_selected_F1.mat, data_selected_F2.mat, data_selected_F3.mat
├── test_data_F1.mat, test_data_F2.mat, test_data_F3.mat
├── dmcdl_initial.m, dmcdl_incremental.m
├── build_threshold_and_memory.m
└── omp.m
```

## 📈 DMCDL特有机制

### 1. 双重记忆机制
```matlab
% 记忆池拼接
Y_concat = [Y_new, Yh];  % 新数据 + 历史记忆

% 权重矩阵更新
W = W + lambda_x * (更新项);

% 记忆池更新
[Rtr, Yh] = build_threshold_and_memory(D, X, Y_concat, M);
```

### 2. 阈值自适应
- 基于重构误差动态调整阈值
- 记忆池容量自适应管理
- 权重矩阵渐进式更新

### 3. 增量学习策略
- 保持历史知识的同时学习新模式
- 通过权重矩阵控制遗忘程度
- 记忆池提供代表性样本

## 🆚 与GILDL方法对比

### 相同点
- ✅ 增量学习框架
- ✅ 多模式数据处理
- ✅ R统计量监测
- ✅ FAR/FDR性能评估

### DMCDL特有优势
- 🆕 **双重记忆机制**: 权重矩阵 + 记忆池
- 🆕 **自适应阈值**: 动态调整监测阈值
- 🆕 **渐进式遗忘**: 控制历史信息保留
- 🆕 **样本选择**: 智能记忆池管理

### 算法复杂度
- **GILDL**: 主要计算权重矩阵更新
- **DMCDL**: 额外计算记忆池和阈值更新

## 📊 性能指标

### 1. 字典演化指标
```matlab
% NMSC (归一化均方变化度)
NMSC = mean((D_new - D_old)² ./ (D_old² + ε))

% 权重矩阵演化
weight_norm = norm(W, 'fro')
weight_trace = trace(W)
```

### 2. 记忆机制指标
```matlab
% 记忆池大小
memory_size = size(Yh, 2)

% 阈值演化
threshold_history = [Rtr1, Rtr2, Rtr3]

% 记忆多样性
diversity = mean(pdist(Yh'))
```

### 3. 监测性能指标
```matlab
% 误报率和检出率
FAR = sum(R_normal > R_limit) / n_normal
FDR = sum(R_fault > R_limit) / n_fault
```

## 🎯 参数设置

### 关键参数
```matlab
n_atoms = 20;           % 字典原子数
sparsity = 3;           % 稀疏度
n_iter = 50;            % 迭代次数
lambda_x = 1e-3;        % 稀疏惩罚参数
lambda_1 = 1e-6;        % 初始正则化参数
M = 10;                 % 记忆池容量
```

### 参数调优建议
- **n_atoms**: 根据数据复杂度调整，通常为数据维度的1-3倍
- **sparsity**: 影响重构精度，建议2-5
- **lambda_x**: 控制稀疏性，过大导致欠拟合
- **M**: 记忆池容量，影响历史信息保留

## 📈 预期结果

### 控制台输出示例
```
========== DMCDL催化数据集完整分析 ==========

📈 字典演化性能:
   F1→F2: 0.123456
   F2→F3: 0.098765
   平均值: 0.111111

🧠 记忆机制性能:
   F1: 权重范数=1.2345
   F2: 权重范数=1.5678
   F3: 权重范数=1.8901

🎯 过程监测性能:
   平均FAR: 0.0234 (误报率，越小越好)
   平均FDR: 0.9567 (检出率，越大越好)
   ✅ 检测性能优秀
```

### 可视化结果
- **字典演化图**: 3个模式的字典热图对比
- **监测性能图**: R统计量时间序列和分布
- **方法比较图**: DMCDL vs GILDL性能对比

## 🔍 结果解读

### 字典稳定性
- **NMSC < 0.1**: 字典变化小，稳定性高
- **NMSC 0.1-0.5**: 适度变化，平衡稳定性和适应性
- **NMSC > 0.5**: 变化大，适应性强但稳定性差

### 记忆机制效果
- **权重范数增长**: 表示记忆积累效果
- **记忆池稳定**: 表示代表性样本选择有效
- **阈值适应**: 表示监测一致性

### 监测性能
- **FAR < 0.05 & FDR > 0.8**: 性能优秀
- **FAR < 0.1 & FDR > 0.7**: 性能良好
- **其他**: 需要改进

## 🎉 总结

DMCDL催化数据集应用实现了：

1. **完整的增量学习**: F1→F2→F3渐进式字典演化
2. **双重记忆机制**: 权重矩阵和记忆池协同工作
3. **自适应监测**: 动态阈值调整和R统计量监测
4. **性能比较**: 与GILDL方法的全面对比
5. **自动化流程**: 一键运行完整分析

这为DMCDL方法在实际工业催化过程监测中的应用提供了完整的解决方案！
