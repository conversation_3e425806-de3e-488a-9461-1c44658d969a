%% 快速运行GILDL方法性能分析
% 使用与SVD_DL相同的性能检测方法分析GILDL

fprintf('========== 开始GILDL方法性能分析 ==========\n');
fprintf('分析目标: 使用num_monitoring_exp.m中的FAR/FDR计算方法\n');
fprintf('字典学习方法: GILDL (learn_D.m)\n');
fprintf('性能指标: NMSC、主方向子空间夹角、FAR、FDR\n');
fprintf('FAR/FDR计算: 完全按照num_monitoring_exp.m的实现\n\n');

% 检查必要文件
fprintf('🔍 检查必要文件...\n');

% 检查learn_D_GILDL.m
if ~exist('learn_D_GILDL.m', 'file')
    fprintf('❌ 缺少核心文件: learn_D_GILDL.m\n');
    fprintf('请确保learn_D_GILDL.m在当前目录中。\n');
    return;
else
    fprintf('✓ 找到GILDL字典学习函数: learn_D_GILDL.m\n');
end

% 检查数据文件
data_files = {};
for i = 1:5
    train_file = sprintf('mode%d_train.mat', i);
    normal_file = sprintf('mode%d_test_normal.mat', i);
    fault_file = sprintf('mode%d_test_fault.mat', i);
    
    % 检查当前目录
    if ~exist(train_file, 'file')
        % 检查SVD_DL目录
        train_file_svd = sprintf('../SVD_DL/mode%d_train.mat', i);
        if exist(train_file_svd, 'file')
            fprintf('✓ 找到数据文件: %s\n', train_file_svd);
        else
            data_files{end+1} = train_file;
        end
    else
        fprintf('✓ 找到数据文件: %s\n', train_file);
    end
    
    if ~exist(normal_file, 'file') && ~exist(sprintf('../SVD_DL/%s', normal_file), 'file')
        data_files{end+1} = normal_file;
    end
    
    if ~exist(fault_file, 'file') && ~exist(sprintf('../SVD_DL/%s', fault_file), 'file')
        data_files{end+1} = fault_file;
    end
end

if ~isempty(data_files)
    fprintf('❌ 缺少以下数据文件:\n');
    for i = 1:length(data_files)
        fprintf('   - %s\n', data_files{i});
    end
    fprintf('请确保数据文件在当前目录或../SVD_DL/目录中。\n');
    return;
end

% 检查辅助函数
if ~exist('omp', 'file')
    addpath('../SVD_DL');
    if ~exist('omp', 'file')
        fprintf('⚠️  警告: 未找到OMP函数，将使用最小二乘方法作为备用\n');
    else
        fprintf('✓ 找到OMP函数\n');
    end
else
    fprintf('✓ 找到OMP函数\n');
end

fprintf('✅ 文件检查完成\n\n');

% 运行分析
try
    fprintf('🚀 开始执行GILDL性能分析...\n');
    tic;
    
    % 执行简化分析脚本 (使用learn_D_GILDL函数)
    analyze_gildl_simple;
    
    elapsed_time = toc;
    fprintf('\n✅ GILDL分析完成！\n');
    fprintf('⏱️  总耗时: %.1f 分钟\n', elapsed_time/60);
    
    % 加载结果并显示关键信息
    if exist('gildl_simple_analysis.mat', 'file')
        load('gildl_simple_analysis.mat', 'results_gildl');
    elseif exist('gildl_performance_analysis.mat', 'file')
        load('gildl_performance_analysis.mat', 'results_gildl');
    else
        error('未找到分析结果文件');
    end
    
    fprintf('\n📋 GILDL方法结果总览:\n');
    fprintf('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
    
    % NMSC结果
    if isfield(results_gildl, 'NMSC_history') && ~isempty(results_gildl.NMSC_history)
        NMSC_history = results_gildl.NMSC_history;
        fprintf('📊 NMSC (归一化均方变化度):\n');
        for i = 1:length(NMSC_history)
            fprintf('   Mode %d→%d: %.4f\n', i, i+1, NMSC_history(i));
        end
        fprintf('   平均值: %.4f\n', mean(NMSC_history));
    elseif isfield(results_gildl, 'NMSC')
        fprintf('📊 NMSC (归一化均方变化度): %.4f\n', results_gildl.NMSC);
    end

    % 子空间夹角结果
    if isfield(results_gildl, 'Subspace_angle_history') && ~isempty(results_gildl.Subspace_angle_history)
        Subspace_angle_history = results_gildl.Subspace_angle_history;
        valid_angles = Subspace_angle_history(~isnan(Subspace_angle_history));
        fprintf('\n📐 主方向子空间夹角 (弧度):\n');
        for i = 1:length(Subspace_angle_history)
            if ~isnan(Subspace_angle_history(i))
                fprintf('   Mode %d→%d: %.4f弧度 (%.2f°)\n', i, i+1, ...
                        Subspace_angle_history(i), Subspace_angle_history(i)*180/pi);
            else
                fprintf('   Mode %d→%d: 无法计算\n', i, i+1);
            end
        end
        if ~isempty(valid_angles)
            fprintf('   平均值: %.4f弧度 (%.2f°)\n', mean(valid_angles), mean(valid_angles)*180/pi);
        end
    elseif isfield(results_gildl, 'max_angle_rad')
        fprintf('\n📐 主空间最大夹角: %.4f弧度 (%.2f°)\n', ...
                results_gildl.max_angle_rad, results_gildl.max_angle_rad*180/pi);
    end
    
    % 主空间维度
    if isfield(results_gildl, 'subspace_dims') && ~isempty(results_gildl.subspace_dims)
        subspace_dims = results_gildl.subspace_dims;
        fprintf('\n🔍 主空间维度演化:\n');
        for i = 1:length(subspace_dims)
            fprintf('   Mode %d: %d维\n', i, subspace_dims(i));
        end
    elseif isfield(results_gildl, 'Dictionary_history') && iscell(results_gildl.Dictionary_history)
        % 从字典历史计算主空间维度
        Dictionary_history = results_gildl.Dictionary_history;
        fprintf('\n🔍 主空间维度演化:\n');
        for i = 1:length(Dictionary_history)
            [U, S, ~] = svd(Dictionary_history{i}, 'econ');
            singular_values = diag(S);
            energy = cumsum(singular_values.^2) / sum(singular_values.^2);
            k_locked = find(energy >= 0.9, 1, 'first');
            if isempty(k_locked)
                k_locked = min([3, size(U,2)]);
            end
            fprintf('   Mode %d: %d维\n', i, k_locked);
        end
    elseif isfield(results_gildl, 'final_dict')
        % 单字典情况
        final_dict = results_gildl.final_dict;
        fprintf('\n🔍 字典信息:\n');
        fprintf('   字典大小: %dx%d\n', size(final_dict,1), size(final_dict,2));
    end
    
    % 性能指标
    if isfield(results_gildl, 'FAR_overall') && isfield(results_gildl, 'FDR_overall')
        FAR_overall = results_gildl.FAR_overall;
        FDR_overall = results_gildl.FDR_overall;

        if isfield(results_gildl, 'FAR_vec') && isfield(results_gildl, 'FDR_vec')
            FAR_vec = results_gildl.FAR_vec;
            FDR_vec = results_gildl.FDR_vec;

            fprintf('\n🎯 性能指标 (FAR/FDR):\n');
            for i = 1:min(5, length(FAR_vec))
                if ~isnan(FAR_vec(i)) && ~isnan(FDR_vec(i))
                    fprintf('   Mode %d: FAR=%.4f, FDR=%.4f\n', i, FAR_vec(i), FDR_vec(i));
                end
            end
        end

        if ~isnan(FAR_overall) && ~isnan(FDR_overall)
            fprintf('   总体性能: FAR=%.4f, FDR=%.4f\n', FAR_overall, FDR_overall);
        end
    else
        fprintf('\n🎯 性能指标: 计算失败或未完成\n');
    end
    
    fprintf('\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
    
    % 结果解释
    fprintf('\n💡 GILDL方法结果解释:\n');
    
    % NMSC解释
    if exist('NMSC_history', 'var') && ~isempty(NMSC_history)
        avg_nmsc = mean(NMSC_history);
        fprintf('🔸 NMSC平均值 %.4f 说明 (归一化均方变化度):\n', avg_nmsc);
        if avg_nmsc < 0.1
            fprintf('   字典在模式转换中变化很小，保持了高度稳定性\n');
            fprintf('   GILDL方法能够很好地保持已学习的特征\n');
        elseif avg_nmsc < 0.5
            fprintf('   字典在模式转换中有适度的变化\n');
            fprintf('   GILDL方法在稳定性和适应性之间取得了平衡\n');
        else
            fprintf('   字典在模式转换中变化较大\n');
            fprintf('   GILDL方法具有强适应性，能够学习新的特征\n');
        end
    elseif isfield(results_gildl, 'NMSC') && ~isnan(results_gildl.NMSC)
        fprintf('🔸 NMSC值 %.4f 说明 (归一化均方变化度):\n', results_gildl.NMSC);
        if results_gildl.NMSC < 0.1
            fprintf('   字典变化很小，保持了高度稳定性\n');
        elseif results_gildl.NMSC < 0.5
            fprintf('   字典有适度的变化\n');
        else
            fprintf('   字典变化较大，显示了强适应性\n');
        end
    else
        fprintf('🔸 NMSC信息不可用\n');
    end
    
    % 子空间夹角解释
    if exist('valid_angles', 'var') && ~isempty(valid_angles)
        avg_angle = mean(valid_angles);
        avg_angle_deg = avg_angle * 180 / pi;
        fprintf('\n🔸 子空间夹角平均值 %.4f弧度 (%.2f°) 说明:\n', avg_angle, avg_angle_deg);
        if avg_angle < pi/6  % 30度
            fprintf('   主方向在模式转换中变化较小，保持了良好的稳定性\n');
            fprintf('   GILDL方法有利于保持监测的一致性\n');
        elseif avg_angle < pi/3  % 60度
            fprintf('   主方向在模式转换中有中等程度的变化\n');
            fprintf('   GILDL方法在稳定性和适应性之间取得了平衡\n');
        else
            fprintf('   主方向在模式转换中变化较大\n');
            fprintf('   GILDL方法能够适应不同模式的主要特征\n');
        end
    elseif isfield(results_gildl, 'max_angle_rad') && ~isnan(results_gildl.max_angle_rad)
        max_angle = results_gildl.max_angle_rad;
        max_angle_deg = max_angle * 180 / pi;
        fprintf('\n🔸 主空间最大夹角 %.4f弧度 (%.2f°) 说明:\n', max_angle, max_angle_deg);
        if max_angle < pi/6  % 30度
            fprintf('   主方向变化较小，保持了良好的稳定性\n');
        elseif max_angle < pi/3  % 60度
            fprintf('   主方向有中等程度的变化\n');
        else
            fprintf('   主方向变化较大，显示了强适应性\n');
        end
    else
        fprintf('\n🔸 子空间夹角信息不可用\n');
    end
    
    % 性能指标解释
    fprintf('\n🔸 性能指标 FAR=%.4f, FDR=%.4f 说明:\n', FAR_overall, FDR_overall);
    if FDR_overall > 0.9
        fprintf('   故障检测率很高，GILDL方法能够有效检测故障\n');
    elseif FDR_overall > 0.7
        fprintf('   故障检测率良好，GILDL方法具有较好的检测能力\n');
    else
        fprintf('   故障检测率有待提高，可能需要优化GILDL参数\n');
    end
    
    if FAR_overall < 0.05
        fprintf('   误报率很低，GILDL方法具有良好的稳定性\n');
    elseif FAR_overall < 0.1
        fprintf('   误报率较低，GILDL方法稳定性良好\n');
    else
        fprintf('   误报率偏高，可能需要调整检测阈值\n');
    end
    
    % 生成可视化
    fprintf('\n📊 生成可视化分析...\n');
    visualize_gildl_evolution(results_gildl);
    
    fprintf('\n📁 输出文件:\n');
    fprintf('   - gildl_performance_analysis.mat: 完整分析结果\n');
    fprintf('   - 多个可视化图表窗口已打开\n');
    
    fprintf('\n🎉 GILDL方法性能分析完成！\n');
    fprintf('📊 请查看生成的图表以获得更详细的信息。\n');
    
    % 与SVD_DL方法的比较提示
    fprintf('\n💡 提示: 如需与SVD_DL方法比较，可运行:\n');
    fprintf('   compare_methods_performance.m\n');
    
catch ME
    fprintf('\n❌ GILDL分析过程中出现错误:\n');
    fprintf('错误信息: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    end
    fprintf('\n请检查:\n');
    fprintf('1. learn_D.m函数是否正常工作\n');
    fprintf('2. 所有数据文件是否存在\n');
    fprintf('3. MATLAB版本是否支持所用函数\n');
    fprintf('4. 当前目录权限是否允许读写文件\n');
end
