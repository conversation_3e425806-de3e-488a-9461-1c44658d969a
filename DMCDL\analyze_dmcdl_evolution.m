%% DMCDL字典演化分析
% 仿照SVD_DL方法分析DMCDL的字典原子变化和性能指标

clc; clear; close all;
rng(42);

fprintf('========== DMCDL字典演化分析 ==========\n');
fprintf('分析DMCDL方法的字典原子变化和性能指标\n\n');

%% 1. 运行DMCDL训练并保存字典历史
fprintf('1. 运行DMCDL训练并保存字典历史...\n');

% 检查是否已有训练结果
    fprintf('   开始DMCDL训练并记录字典演化...\n');
    
    % 参数设置
    n_atoms = 20;
    sparsity = 2;
    n_iter = 50;
    lambda_x = 1e-6;
    lambda_1 = 1e-8;
    zeta = 1e-12;
    M = 20;
    
    % 初始化存储
    Dictionary_history = cell(5, 1);
    Weight_history = cell(5, 1);
    Memory_history = cell(5, 1);
    Threshold_history = zeros(5, 1);
    
    % Mode 1: 初始建模
    fprintf('   训练Mode 1...\n');
    load('mode1_train.mat');
    Y = train_data';
    
    [D, X, w, W] = dmcdl_initial(Y, n_atoms, sparsity, n_iter, lambda_1, zeta);
    [Rtr, Yh] = build_threshold_and_memory(D, X, Y, M);
    
    Dictionary_history{1} = D;
    Weight_history{1} = W;
    Memory_history{1} = Yh;
    Threshold_history(1) = Rtr;
    
    % Mode 2-5: 增量学习
    for k = 2:5
        fprintf('   训练Mode %d...\n', k);
        
        % 读取新模式数据
        fname = sprintf('mode%d_train.mat', k);
        load(fname);
        Ynew = train_data';
        
        % 双重记忆拼接
        Y_concat = [Ynew, Yh];
        
        % 增量学习
        [D, X, W, w] = dmcdl_incremental(D, W, Y_concat, ...
                         n_atoms, sparsity, n_iter, lambda_x, zeta, w);
        
        % 更新阈值和记忆池
        [Rtr, Yh] = build_threshold_and_memory(D, X, Y_concat, M);
        
        % 保存历史
        Dictionary_history{k} = D;
        Weight_history{k} = W;
        Memory_history{k} = Yh;
        Threshold_history(k) = Rtr;
    end
    
    % 保存演化结果
    save('dmcdl_evolution_results.mat', 'Dictionary_history', 'Weight_history', ...
         'Memory_history', 'Threshold_history', 'n_atoms', 'sparsity');
    fprintf('   演化结果已保存到: dmcdl_evolution_results.mat\n');

%% 2. 监测性能评估
fprintf('\n2. 计算监测性能指标...\n');

% 使用最终字典进行监测性能评估
D_final = Dictionary_history{end};
Rtr_final = Threshold_history(end);

% 加载所有测试数据
fprintf('   加载测试数据...\n');
Y_test_all = [];
labels_all = [];

for mode = 1:5
    % 加载训练数据作为正常数据
    load(sprintf('mode%d_train.mat', mode));
    Y_normal = train_data';

    % 生成故障数据 (添加噪声和偏移)
    Y_fault = Y_normal + 0.5*randn(size(Y_normal)) + 0.3*mean(Y_normal, 2);

    % 合并数据
    Y_test_all = [Y_test_all, Y_normal(:, 1:200), Y_fault(:, 1:200)];  % 每个模式200正常+200故障
    labels_all = [labels_all, zeros(1, 200), ones(1, 200)];  % 0=正常, 1=故障
end

% 计算R统计量
fprintf('   计算R统计量...\n');
n_test = size(Y_test_all, 2);
R_statistics = zeros(1, n_test);

for i = 1:n_test
    y = Y_test_all(:, i);
    x = omp(D_final, y, sparsity);
    R_statistics(i) = norm(y - D_final*x, 2)^2;
end

% 计算FAR和FDR
normal_indices = (labels_all == 0);
fault_indices = (labels_all == 1);

false_alarms = sum(R_statistics(normal_indices) > Rtr_final);
fault_detections = sum(R_statistics(fault_indices) > Rtr_final);

FAR = false_alarms / sum(normal_indices);
FDR = fault_detections / sum(fault_indices);

fprintf('   监测性能结果:\n');
fprintf('     FAR (误报率): %.4f\n', FAR);
fprintf('     FDR (检出率): %.4f\n', FDR);
fprintf('     控制限: %.6f\n', Rtr_final);

%% 3. 计算字典演化指标
fprintf('\n3. 计算字典演化指标...\n');

n_modes = length(Dictionary_history);
NMSC_history = zeros(n_modes-1, 1);
Subspace_angle_history = zeros(n_modes-1, 1);
Dictionary_change_stats = cell(n_modes-1, 1);

% 计算主空间
U_locked_history = cell(n_modes, 1);
subspace_dims = zeros(n_modes, 1);

for i = 1:n_modes
    D = Dictionary_history{i};
    [U, S, ~] = svd(D, 'econ');
    
    % 计算主空间维度（90%能量）
    singular_values = diag(S);
    energy = cumsum(singular_values.^2) / sum(singular_values.^2);
    k_locked = find(energy >= 0.9, 1, 'first');
    if isempty(k_locked)
        k_locked = min(5, size(U, 2));
    end
    
    U_locked_history{i} = U(:, 1:k_locked);
    subspace_dims(i) = k_locked;
end

% 计算NMSC和子空间夹角
for i = 2:n_modes
    D_prev = Dictionary_history{i-1};
    D_curr = Dictionary_history{i};
    U_prev = U_locked_history{i-1};
    U_curr = U_locked_history{i};
    
    % 使用SVD_DL的计算方法
    [NMSC, subspace_angle, change_stats] = compute_nmsc_and_angles(D_prev, D_curr, U_prev, U_curr);
    
    NMSC_history(i-1) = NMSC;
    Subspace_angle_history(i-1) = subspace_angle;
    Dictionary_change_stats{i-1} = change_stats;
    
    fprintf('   Mode %d→%d: NMSC=%.6f, 子空间夹角=%.4f弧度 (%.2f°)\n', ...
            i-1, i, NMSC, subspace_angle, subspace_angle*180/pi);
end

%% 3. 权重矩阵演化分析
fprintf('\n3. 权重矩阵演化分析...\n');

Weight_norms = zeros(n_modes, 1);
Weight_traces = zeros(n_modes, 1);
Weight_conditions = zeros(n_modes, 1);

for i = 1:n_modes
    W = Weight_history{i};
    Weight_norms(i) = norm(W, 'fro');
    Weight_traces(i) = trace(W);
    Weight_conditions(i) = cond(W + 1e-12*eye(size(W)));
    
    fprintf('   Mode %d: 权重范数=%.4f, 迹=%.4f, 条件数=%.2e\n', ...
            i, Weight_norms(i), Weight_traces(i), Weight_conditions(i));
end

%% 4. 记忆池分析
fprintf('\n4. 记忆池分析...\n');

Memory_sizes = zeros(n_modes, 1);
Memory_diversities = zeros(n_modes, 1);

for i = 1:n_modes
    Yh = Memory_history{i};
    Memory_sizes(i) = size(Yh, 2);
    
    % 计算记忆池多样性（样本间平均距离）
    if size(Yh, 2) > 1
        distances = pdist(Yh', 'euclidean');
        Memory_diversities(i) = mean(distances);
    else
        Memory_diversities(i) = 0;
    end
    
    fprintf('   Mode %d: 记忆池大小=%d, 多样性=%.4f\n', ...
            i, Memory_sizes(i), Memory_diversities(i));
end

%% 5. 可视化分析
fprintf('\n5. 生成可视化分析...\n');

figure('Position', [50, 50, 1400, 1000]);

% 子图1: 字典演化热图
subplot(3,4,1);
imagesc(Dictionary_history{1});
colorbar;
title('Mode 1 字典', 'FontSize', 12);
xlabel('原子索引');
ylabel('特征维度');

subplot(3,4,2);
imagesc(Dictionary_history{end});
colorbar;
title('Mode 5 字典', 'FontSize', 12);
xlabel('原子索引');
ylabel('特征维度');

% 子图3: 字典变化
subplot(3,4,3);
delta_D = Dictionary_history{end} - Dictionary_history{1};
imagesc(delta_D);
colorbar;
title('字典总变化 (Mode5-Mode1)', 'FontSize', 12);
xlabel('原子索引');
ylabel('特征维度');

% 子图4: NMSC趋势
subplot(3,4,4);
plot(2:n_modes, NMSC_history, 'bo-', 'LineWidth', 2, 'MarkerSize', 8);
xlabel('目标模式', 'FontSize', 12);
ylabel('NMSC', 'FontSize', 12);
title('DMCDL: NMSC变化趋势', 'FontSize', 12);
grid on;
for i = 1:length(NMSC_history)
    text(i+1, NMSC_history(i), sprintf('%.4f', NMSC_history(i)), ...
         'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom');
end

% 子图5: 子空间夹角
subplot(3,4,5);
valid_angles = Subspace_angle_history(~isnan(Subspace_angle_history));
valid_modes = find(~isnan(Subspace_angle_history)) + 1;
if ~isempty(valid_angles)
    plot(valid_modes, valid_angles, 'rs-', 'LineWidth', 2, 'MarkerSize', 8);
    xlabel('目标模式', 'FontSize', 12);
    ylabel('子空间夹角 (弧度)', 'FontSize', 12);
    title('DMCDL: 子空间夹角变化', 'FontSize', 12);
    grid on;
end

% 子图6: 主空间维度
subplot(3,4,6);
plot(1:n_modes, subspace_dims, 'go-', 'LineWidth', 2, 'MarkerSize', 8);
xlabel('模式', 'FontSize', 12);
ylabel('主空间维度', 'FontSize', 12);
title('DMCDL: 主空间维度演化', 'FontSize', 12);
grid on;

% 子图7: 权重矩阵范数
subplot(3,4,7);
plot(1:n_modes, Weight_norms, 'mo-', 'LineWidth', 2, 'MarkerSize', 8);
xlabel('模式', 'FontSize', 12);
ylabel('权重矩阵范数', 'FontSize', 12);
title('DMCDL: 权重矩阵演化', 'FontSize', 12);
grid on;

% 子图8: 阈值演化
subplot(3,4,8);
plot(1:n_modes, Threshold_history, 'co-', 'LineWidth', 2, 'MarkerSize', 8);
xlabel('模式', 'FontSize', 12);
ylabel('重构误差阈值', 'FontSize', 12);
title('DMCDL: 阈值演化', 'FontSize', 12);
grid on;

% 子图9: 记忆池大小
subplot(3,4,9);
plot(1:n_modes, Memory_sizes, 'ko-', 'LineWidth', 2, 'MarkerSize', 8);
xlabel('模式', 'FontSize', 12);
ylabel('记忆池大小', 'FontSize', 12);
title('DMCDL: 记忆池演化', 'FontSize', 12);
grid on;

% 子图10: 记忆池多样性
subplot(3,4,10);
plot(1:n_modes, Memory_diversities, 'ro-', 'LineWidth', 2, 'MarkerSize', 8);
xlabel('模式', 'FontSize', 12);
ylabel('记忆池多样性', 'FontSize', 12);
title('DMCDL: 记忆多样性', 'FontSize', 12);
grid on;

% 子图11: 权重条件数
subplot(3,4,11);
semilogy(1:n_modes, Weight_conditions, 'bo-', 'LineWidth', 2, 'MarkerSize', 8);
xlabel('模式', 'FontSize', 12);
ylabel('权重矩阵条件数', 'FontSize', 12);
title('DMCDL: 数值稳定性', 'FontSize', 12);
grid on;

% 子图12: 方法总结
subplot(3,4,12);
axis off;
summary_text = {'DMCDL演化分析总结:', '', ...
                sprintf('模式数: %d', n_modes), ...
                sprintf('字典大小: %dx%d', size(Dictionary_history{1})), ...
                sprintf('平均NMSC: %.6f', mean(NMSC_history))};

if ~isempty(valid_angles)
    summary_text{end+1} = sprintf('平均子空间夹角: %.4f弧度', mean(valid_angles));
end

summary_text{end+1} = sprintf('最终权重范数: %.4f', Weight_norms(end));
summary_text{end+1} = sprintf('最终记忆池大小: %d', Memory_sizes(end));

text(0.1, 0.8, summary_text, 'FontSize', 11, 'VerticalAlignment', 'top');

sgtitle('DMCDL方法 - 字典演化与性能分析', 'FontSize', 16);

% 保存图像
savefig('dmcdl_evolution_analysis.fig');
fprintf('   演化分析图已保存到: dmcdl_evolution_analysis.fig\n');

%% 6. 保存分析结果
fprintf('\n6. 保存分析结果...\n');

dmcdl_analysis_results = struct();
dmcdl_analysis_results.method = 'DMCDL';
dmcdl_analysis_results.Dictionary_history = Dictionary_history;
dmcdl_analysis_results.Weight_history = Weight_history;
dmcdl_analysis_results.Memory_history = Memory_history;
dmcdl_analysis_results.U_locked_history = U_locked_history;
dmcdl_analysis_results.NMSC_history = NMSC_history;
dmcdl_analysis_results.Subspace_angle_history = Subspace_angle_history;
dmcdl_analysis_results.subspace_dims = subspace_dims;
dmcdl_analysis_results.Weight_norms = Weight_norms;
dmcdl_analysis_results.Weight_traces = Weight_traces;
dmcdl_analysis_results.Weight_conditions = Weight_conditions;
dmcdl_analysis_results.Threshold_history = Threshold_history;
dmcdl_analysis_results.Memory_sizes = Memory_sizes;
dmcdl_analysis_results.Memory_diversities = Memory_diversities;
dmcdl_analysis_results.Dictionary_change_stats = Dictionary_change_stats;

save('dmcdl_analysis_results.mat', 'dmcdl_analysis_results');
fprintf('   分析结果已保存到: dmcdl_analysis_results.mat\n');

%% 7. 结果总结
fprintf('\n========== DMCDL演化分析总结 ==========\n');
fprintf('📊 基本信息:\n');
fprintf('   方法: DMCDL (双重记忆持续字典学习)\n');
fprintf('   模式数: %d\n', n_modes);
fprintf('   字典大小: %dx%d\n', size(Dictionary_history{1}));

fprintf('\n📈 字典演化指标:\n');
fprintf('   平均NMSC: %.6f\n', mean(NMSC_history));
if ~isempty(valid_angles)
    fprintf('   平均子空间夹角: %.4f弧度 (%.2f°)\n', mean(valid_angles), mean(valid_angles)*180/pi);
end
fprintf('   主空间维度范围: [%d, %d]\n', min(subspace_dims), max(subspace_dims));

fprintf('\n🧠 记忆机制分析:\n');
fprintf('   最终权重矩阵范数: %.4f\n', Weight_norms(end));
fprintf('   最终记忆池大小: %d\n', Memory_sizes(end));
fprintf('   平均记忆多样性: %.4f\n', mean(Memory_diversities));

fprintf('\n🎯 数值稳定性:\n');
fprintf('   权重矩阵条件数范围: [%.2e, %.2e]\n', min(Weight_conditions), max(Weight_conditions));
fprintf('   阈值变化范围: [%.6f, %.6f]\n', min(Threshold_history), max(Threshold_history));

fprintf('\n🎉 DMCDL字典演化分析完成！\n');

%% 辅助函数：计算NMSC和子空间夹角（复制自SVD_DL）
function [NMSC, subspace_angle, change_stats] = compute_nmsc_and_angles(D_old, D_new, U_old, U_new)
    % 计算NMSC
    epsilon = 1e-8;
    num = (D_new - D_old).^2;
    den = D_old.^2 + epsilon;
    NMSC = mean(num(:) ./ den(:));
    
    % 字典变化统计
    change_stats = struct();
    change_stats.delta_D = D_new - D_old;
    change_stats.abs_delta_D = abs(D_new - D_old);
    change_stats.relative_change = num ./ den;
    change_stats.max_change = max(abs(change_stats.delta_D(:)));
    change_stats.mean_abs_change = mean(abs(change_stats.delta_D(:)));
    change_stats.std_change = std(change_stats.delta_D(:));
    
    % 计算子空间夹角
    if size(U_old, 1) ~= size(U_new, 1)
        subspace_angle = NaN;
        return;
    end
    
    min_dim = min(size(U_old, 2), size(U_new, 2));
    if min_dim == 0
        subspace_angle = NaN;
        return;
    end
    
    U_old_trunc = U_old(:, 1:min_dim);
    U_new_trunc = U_new(:, 1:min_dim);
    
    try
        subspace_angle = subspace(U_old_trunc, U_new_trunc);
    catch
        subspace_angle = NaN;
    end
end

%% 监测性能评估
fprintf('\n计算监测性能指标...\n');

% 使用最终字典进行监测性能评估
D_final = Dictionary_history{end};
Rtr_final = Threshold_history(end);

% 加载所有测试数据
fprintf('   加载测试数据...\n');
Y_test_all = [];
labels_all = [];

for mode = 1:5
    % 加载训练数据作为正常数据
    load(sprintf('mode%d_train.mat', mode));
    Y_normal = train_data';

    % 生成故障数据 (添加噪声和偏移)
    Y_fault = Y_normal + 0.5*randn(size(Y_normal)) + 0.3*mean(Y_normal, 2);

    % 合并数据
    Y_test_all = [Y_test_all, Y_normal(:, 1:200), Y_fault(:, 1:200)];  % 每个模式200正常+200故障
    labels_all = [labels_all, zeros(1, 200), ones(1, 200)];  % 0=正常, 1=故障
end

% 计算R统计量
fprintf('   计算R统计量...\n');
n_test = size(Y_test_all, 2);
R_statistics = zeros(1, n_test);

for i = 1:n_test
    y = Y_test_all(:, i);
    x = omp(D_final, y, sparsity);
    R_statistics(i) = norm(y - D_final*x, 2)^2;
end

% 计算FAR和FDR
normal_indices = (labels_all == 0);
fault_indices = (labels_all == 1);

false_alarms = sum(R_statistics(normal_indices) > Rtr_final);
fault_detections = sum(R_statistics(fault_indices) > Rtr_final);

FAR = false_alarms / sum(normal_indices);
FDR = fault_detections / sum(fault_indices);

fprintf('   监测性能结果:\n');
fprintf('     FAR (误报率): %.4f\n', FAR);
fprintf('     FDR (检出率): %.4f\n', FDR);
fprintf('     控制限: %.6f\n', Rtr_final);

%% 核心指标可视化
fprintf('\n生成核心指标可视化...\n');

figure('Position', [200, 200, 1200, 800]);

% 子图1: NMSC演化
subplot(2,2,1);
plot(2:n_modes, NMSC_history, 'bo-', 'LineWidth', 3, 'MarkerSize', 10, 'MarkerFaceColor', 'b');
xlabel('目标模式', 'FontSize', 14);
ylabel('NMSC', 'FontSize', 14);
title('DMCDL: NMSC演化', 'FontSize', 16, 'FontWeight', 'bold');
grid on;
for i = 1:length(NMSC_history)
    text(i+1, NMSC_history(i), sprintf('%.4f', NMSC_history(i)), ...
         'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', ...
         'FontSize', 12, 'FontWeight', 'bold');
end

% 子图2: 主方向子空间夹角
subplot(2,2,2);
valid_angles = Subspace_angle_history(~isnan(Subspace_angle_history));
valid_modes = find(~isnan(Subspace_angle_history)) + 1;
if ~isempty(valid_angles)
    plot(valid_modes, valid_angles*180/pi, 'rs-', 'LineWidth', 3, 'MarkerSize', 10, 'MarkerFaceColor', 'r');
    xlabel('目标模式', 'FontSize', 14);
    ylabel('主方向子空间夹角 (度)', 'FontSize', 14);
    title('DMCDL: 主方向子空间夹角变化', 'FontSize', 16, 'FontWeight', 'bold');
    grid on;
    for i = 1:length(valid_angles)
        text(valid_modes(i), valid_angles(i)*180/pi, sprintf('%.2f°', valid_angles(i)*180/pi), ...
             'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', ...
             'FontSize', 12, 'FontWeight', 'bold');
    end
else
    text(0.5, 0.5, '无有效子空间夹角数据', 'HorizontalAlignment', 'center', ...
         'VerticalAlignment', 'middle', 'FontSize', 14);
    title('DMCDL: 主方向子空间夹角变化', 'FontSize', 16, 'FontWeight', 'bold');
end

% 子图3: 监测性能 - FAR和FDR
subplot(2,2,3);
monitoring_metrics = [FAR, FDR];
monitoring_labels = {'FAR (误报率)', 'FDR (检出率)'};
colors = [0.8, 0.2, 0.2; 0.2, 0.8, 0.2];

b = bar(monitoring_metrics, 'FaceColor', 'flat');
b.CData = colors;
set(gca, 'XTickLabel', monitoring_labels);
ylabel('比率', 'FontSize', 14);
title('DMCDL: 监测性能', 'FontSize', 16, 'FontWeight', 'bold');
grid on;

% 添加数值标签
for i = 1:2
    text(i, monitoring_metrics(i), sprintf('%.4f', monitoring_metrics(i)), ...
         'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', ...
         'FontSize', 12, 'FontWeight', 'bold');
end

% 子图4: 综合性能总结
subplot(2,2,4);
axis off;

% 性能评估
if mean(NMSC_history) < 0.15
    stability_rating = '优秀';
elseif mean(NMSC_history) < 0.25
    stability_rating = '良好';
else
    stability_rating = '一般';
end

if FAR < 0.05 && FDR > 0.8
    monitoring_rating = '优秀';
elseif FAR < 0.1 && FDR > 0.7
    monitoring_rating = '良好';
else
    monitoring_rating = '一般';
end

if ~isempty(valid_angles)
    angle_consistency = '高一致性';
    if mean(valid_angles)*180/pi >= 45
        angle_consistency = '中等一致性';
    end
    angle_text = sprintf('  平均夹角: %.2f° (%s)', mean(valid_angles)*180/pi, angle_consistency);
else
    angle_text = '  无有效夹角数据';
end

summary_text = {
    'DMCDL核心性能总结:', '', ...
    sprintf('字典大小: %dx%d', size(Dictionary_history{1})), ...
    sprintf('模式数: %d', n_modes), '', ...
    '字典稳定性:', ...
    sprintf('  平均NMSC: %.6f (%s)', mean(NMSC_history), stability_rating), '', ...
    '主方向一致性:', ...
    angle_text, '', ...
    '监测性能:', ...
    sprintf('  FAR: %.4f', FAR), ...
    sprintf('  FDR: %.4f', FDR), ...
    sprintf('  评级: %s', monitoring_rating)
};

text(0.05, 0.95, summary_text, 'FontSize', 12, 'VerticalAlignment', 'top', ...
     'FontWeight', 'bold');

sgtitle('DMCDL方法 - 核心性能指标分析', 'FontSize', 18, 'FontWeight', 'bold');

% 保存核心指标图
savefig('dmcdl_core_metrics_analysis.fig');
print('dmcdl_core_metrics_analysis.png', '-dpng', '-r300');
fprintf('   核心指标图已保存到: dmcdl_core_metrics_analysis.fig\n');

% 保存结果
dmcdl_core_results = struct();
dmcdl_core_results.method = 'DMCDL';
dmcdl_core_results.NMSC_history = NMSC_history;
dmcdl_core_results.NMSC_mean = mean(NMSC_history);
dmcdl_core_results.Subspace_angle_history = Subspace_angle_history;
dmcdl_core_results.valid_angles = valid_angles;
dmcdl_core_results.FAR = FAR;
dmcdl_core_results.FDR = FDR;
dmcdl_core_results.monitoring_threshold = Rtr_final;
dmcdl_core_results.stability_rating = stability_rating;
dmcdl_core_results.monitoring_rating = monitoring_rating;

save('dmcdl_core_results.mat', 'dmcdl_core_results');
fprintf('   核心结果已保存到: dmcdl_core_results.mat\n');

fprintf('\n========== DMCDL核心指标分析总结 ==========\n');
fprintf('📊 核心指标:\n');
fprintf('   NMSC: %.6f (%s)\n', mean(NMSC_history), stability_rating);
if ~isempty(valid_angles)
    fprintf('   主方向夹角: %.2f° (%s)\n', mean(valid_angles)*180/pi, angle_consistency);
end
fprintf('   FAR: %.4f\n', FAR);
fprintf('   FDR: %.4f\n', FDR);
fprintf('   监测性能: %s\n', monitoring_rating);
fprintf('🎉 DMCDL核心指标分析完成！\n');
fprintf('==========================================\n');

%% 辅助函数
function w = omp(D, x, L)
% 正交匹配追踪算法
K = size(D, 2);
idx = false(1, K);
r = x;
w = zeros(K, 1);

for j = 1:L
    [~, kbest] = max(abs(D' * r));
    idx(kbest) = true;
    w(idx) = D(:, idx) \ x;
    r = x - D(:, idx) * w(idx);
    if norm(r) < 1e-6
        break;
    end
end
end
