function X = omp(D,Y,T)
    %---------------------------------------------------------
    %  极简 OMP: 列向量逐条编码，限制稀疏度 T
    %---------------------------------------------------------
    [~,N] = size(Y);
    n_atoms = size(D,2);
    X = zeros(n_atoms,N);
    for j = 1:N
        r = Y(:,j);
        S = [];         % 支持集
        for t = 1:T
            proj = D' * r;
            [~,k] = max(abs(proj));
            S = unique([S k]);
            Ds = D(:,S);
            a  = pinv(Ds)*Y(:,j);
            r  = Y(:,j) - Ds*a;
            if norm(r) < 1e-6, break; end
        end
        X(S,j) = a;
    end
    end
    