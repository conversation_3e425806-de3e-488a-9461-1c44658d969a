%% 最优参数条件下的NMSC和主方向子空间夹角分析
% 基于最优参数: lambda=1e+03, n_atoms=20, sparsity=2

rng(42);

%% ========== 设置最优参数 ==========
lambda = 5;
n_atoms = 20;
sparsity = 2;

fprintf('========== 最优参数条件下的详细分析 ==========\n');
fprintf('最优参数组合: λ=%.0e, n_atoms=%d, sparsity=%d\n', lambda, n_atoms, sparsity);
fprintf('预期性能: FDR=1.0000, FAR=0.0084\n\n');

%% ========== 1. 加载训练数据并训练字典 ==========
fprintf('1. 训练字典序列...\n');

load('mode1_train.mat');  % 变量 train_data，1000x8
Y = train_data';          % [8 x 1000]

% K-SVD训练
n_iter = 50;
D_init = randn(8, n_atoms);
for k = 1:n_atoms
    D_init(:,k) = D_init(:,k) / norm(D_init(:,k));
end

fprintf('   Mode 1 字典训练...\n');
[Dictionary, ~] = ksvd_simple_silent(Y, D_init, sparsity, n_iter);

% 计算主空间
[U, S, V] = svd(Dictionary, 'econ');
singular_values = diag(S);
energy = cumsum(singular_values.^2) / sum(singular_values.^2);
k_locked = find(energy >= 0.9, 1, 'first');
U_locked = U(:, 1:k_locked);

% 存储字典历史和主空间历史
Dictionary_history = cell(5,1);
U_locked_history = cell(5,1);
Principal_subspace_history = cell(5,1);  % 存储主方向子空间
NMSC_history = zeros(4,1);  % 存储NMSC值
Subspace_angle_history = zeros(4,1);  % 存储子空间夹角

Dictionary_history{1} = Dictionary;
U_locked_history{1} = U_locked;
Principal_subspace_history{1} = U_locked;

D_prev = Dictionary;
U_locked_prev = U_locked;
k_locked_prev = k_locked;

fprintf('   Mode 1 完成: 主空间维度 = %d\n', k_locked);

%% ========== 2. 多模式训练并计算NMSC和子空间夹角 ==========
for mode = 2:5
    fprintf('   Mode %d 字典训练...\n', mode);
    
    % === 新mode训练 ===
    fname = sprintf('mode%d_train.mat', mode);
    load(fname); Y_new = train_data';
    n_atoms_current = size(D_prev,2);
    D_init = randn(8, n_atoms_current);
    for k = 1:n_atoms_current
        D_init(:,k) = D_init(:,k) / norm(D_init(:,k));
    end
    [D_new, ~] = ksvd_simple_silent(Y_new, D_init, sparsity, 30);

    % === SVD判定主原子 ===
    [Uo, So, Vo] = svd(D_prev, 'econ');
    S_diag = diag(So);
    V_weight = abs(Vo) * S_diag;
    [~, idx] = sort(V_weight, 'descend');
    V_weight_sorted = V_weight(idx);
    energy_cum = cumsum(V_weight_sorted) / sum(V_weight_sorted);
    k_important = find(energy_cum >= 0.9, 1, 'first');
    imp_idx_old = idx(1:k_important);
    unimp_idx_old = idx(k_important+1:end);

    [~, S_new, V_new] = svd(D_new, 'econ');
    V_weight_new = abs(V_new) * diag(S_new);
    [~, idx_new] = sort(V_weight_new, 'descend');
    V_weight_sorted_new = V_weight_new(idx_new);
    energy_cum_new = cumsum(V_weight_sorted_new) / sum(V_weight_sorted_new);
    k_important_new = find(energy_cum_new >= 0.9, 1, 'first');
    imp_idx_new = idx_new(1:k_important_new);

    D_fused = D_prev;
    replace_cnt = min(numel(imp_idx_new), numel(unimp_idx_old));
    D_fused(:, unimp_idx_old(1:replace_cnt)) = D_new(:, imp_idx_new(1:replace_cnt));

    % === 主空间保护微调 ===
    U_locked = Uo(:, 1:k_locked_prev);
    P_locked = U_locked * U_locked';
    X = zeros(size(D_fused,2), size(Y_new,2));
    for n = 1:size(Y_new,2)
        X(:,n) = omp(D_fused, Y_new(:,n), sparsity);
    end
    A = lambda * (P_locked') * P_locked;
    B = X * X';
    C = lambda * (P_locked') * P_locked * D_prev + Y_new * X';
    D_fused = sylvester(A,B,C);

    for k = 1:size(D_fused,2)
        D_fused(:,k) = D_fused(:,k) / norm(D_fused(:,k));
    end

    % === 计算新的主空间 ===
    [U_new, S_new, ~] = svd(D_fused, 'econ');
    singular_values_new = diag(S_new);
    energy_new = cumsum(singular_values_new.^2) / sum(singular_values_new.^2);
    k_locked_new = find(energy_new >= 0.9, 1, 'first');
    U_locked_new = U_new(:, 1:k_locked_new);

    % === 使用专门函数计算NMSC和子空间夹角 ===
    [NMSC, subspace_angle, change_stats] = compute_nmsc_and_angles(D_prev, D_fused, U_locked_prev, U_locked_new);

    NMSC_history(mode-1) = NMSC;
    Subspace_angle_history(mode-1) = subspace_angle;

    % 存储结果
    Dictionary_history{mode} = D_fused;
    U_locked_history{mode} = U_locked_new;
    Principal_subspace_history{mode} = U_locked_new;
    
    fprintf('     主空间维度: %d → %d\n', k_locked_prev, k_locked_new);
    fprintf('     NMSC: %.4f\n', NMSC);
    fprintf('     子空间夹角: %.2f°\n', Subspace_angle_history(mode-1));

    % 更新变量
    D_prev = D_fused;
    U_locked_prev = U_locked_new;
    k_locked_prev = k_locked_new;
end

%% ========== 3. 结果分析和可视化 ==========
fprintf('\n========== 分析结果 ==========\n');

% 显示NMSC历史
fprintf('📊 NMSC (归一化均方相干性) 历史:\n');
for i = 1:4
    fprintf('   Mode %d→%d: %.4f\n', i, i+1, NMSC_history(i));
end
fprintf('   平均NMSC: %.4f\n', mean(NMSC_history));

% 显示子空间夹角历史
fprintf('\n📐 主方向子空间夹角历史:\n');
for i = 1:4
    if ~isnan(Subspace_angle_history(i))
        fprintf('   Mode %d→%d: %.2f°\n', i, i+1, Subspace_angle_history(i));
    else
        fprintf('   Mode %d→%d: 无法计算\n', i, i+1);
    end
end
valid_angles = Subspace_angle_history(~isnan(Subspace_angle_history));
if ~isempty(valid_angles)
    fprintf('   平均子空间夹角: %.2f°\n', mean(valid_angles));
end

%% ========== 4. 可视化分析 ==========
fprintf('\n4. 生成可视化分析...\n');

%% ========== 5. 保存结果并生成可视化 ==========
subspace_dims = cellfun(@(x) size(x,2), U_locked_history);

results_optimal = struct();
results_optimal.lambda = lambda;
results_optimal.n_atoms = n_atoms;
results_optimal.sparsity = sparsity;
results_optimal.NMSC_history = NMSC_history;
results_optimal.Subspace_angle_history = Subspace_angle_history;
results_optimal.Dictionary_history = Dictionary_history;
results_optimal.U_locked_history = U_locked_history;
results_optimal.Principal_subspace_history = Principal_subspace_history;
results_optimal.subspace_dims = subspace_dims;

save('SVD_DL-optimal_parameter_analysis.mat', 'results_optimal');
save('SVDDL_Dlast.mat','D_fused');

% 生成详细的可视化分析
visualize_dictionary_evolution(results_optimal);

fprintf('\n💾 分析结果已保存到 optimal_parameter_analysis.mat\n');
fprintf('📊 详细可视化图表已生成\n');
fprintf('\n🎯 关键指标总结:\n');
fprintf('   平均NMSC: %.4f (值越小表示字典变化越大)\n', mean(NMSC_history));
valid_angles = Subspace_angle_history(~isnan(Subspace_angle_history));
if ~isempty(valid_angles)
    fprintf('   平均子空间夹角: %.2f° (值越大表示主方向变化越大)\n', mean(valid_angles));
end
fprintf('   主空间维度范围: %d - %d\n', min(subspace_dims), max(subspace_dims));

fprintf('\n📈 NMSC解释 (归一化均方变化度):\n');
fprintf('   NMSC接近0: 字典变化很小，高度稳定\n');
fprintf('   NMSC值较大: 字典变化较大，适应性强\n');
fprintf('   当前平均值%.4f表示: ', mean(NMSC_history));
if mean(NMSC_history) < 0.1
    fprintf('字典变化很小，保持了高度稳定性\n');
elseif mean(NMSC_history) < 0.5
    fprintf('字典有适度的变化，平衡了稳定性和适应性\n');
else
    fprintf('字典变化较大，显示了强适应性\n');
end

fprintf('\n📐 子空间夹角解释 (弧度):\n');
fprintf('   夹角接近0: 主方向基本不变\n');
fprintf('   夹角接近π/2: 主方向完全改变\n');
if ~isempty(valid_angles)
    fprintf('   当前平均值%.4f弧度(%.2f°)表示: ', mean(valid_angles), mean(valid_angles)*180/pi);
    if mean(valid_angles) < pi/6  % 30度
        fprintf('主方向变化较小，保持了较好的稳定性\n');
    elseif mean(valid_angles) < pi/3  % 60度
        fprintf('主方向有中等程度的变化\n');
    else
        fprintf('主方向变化较大，适应性较强\n');
    end
end
