micro_iter_list = [1, 3, 5, 10, 20, 30,50, 100];
n_micro = numel(micro_iter_list);
nmsc_arr = zeros(n_micro, 1);
subspace_dist_arr = zeros(n_micro, 1);

lambda = 1e8;      % 主空间保护强度
modes = 5;
n_atoms = 30;
sparsity = 2;
n_iter = 30;
lr=1.1e-7;

for idx = 1:n_micro
    micro_iter = micro_iter_list(idx);
    [Dict_hist, U_hist, NMSC, sub_dist] = continual_dictionary_learning(...
        lambda, micro_iter, modes, n_atoms, sparsity, n_iter,lr);
    nmsc_arr(idx) = NMSC;
    subspace_dist_arr(idx) = sub_dist;
    fprintf('micro-iter=%d: NMSC=%.4f, SubspaceDist=%.4f\n', ...
        micro_iter, NMSC, sub_dist);
end

figure;
subplot(1,2,1);
semilogx(micro_iter_list, nmsc_arr, '-o','LineWidth',2);
xlabel('micro-iter'); ylabel('归一化均方变化度NMSC');
title('字典整体变化度随micro-iter变化');
grid on;

subplot(1,2,2);
semilogx(micro_iter_list, subspace_dist_arr, '-o','LineWidth',2);
xlabel('micro-iter'); ylabel('主空间夹角距离 (rad)');
title('主空间变化度随micro-iter变化');
grid on;
