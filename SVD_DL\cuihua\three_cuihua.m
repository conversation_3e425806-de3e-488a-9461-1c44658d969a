% 加载数据（假设文件名为 data.mat，变量名为 data）
load('DataF1.mat');  % 确保 data 是一个二维矩阵

% 删除第一行
data_new = Data(2:end, :);

% 指定要保留的列索引 F1 fault:36,37  train_samples:2000-3000 30:1000-2000
% 67:1000-2000  |test:3000-4000
selected_columns = [3  7 9 11  15  17 18 19   25 ...
                    31 36 37 43  53  65 67 71 73 82 145  165 ...
                    173 200 209 259 271 273];


% 指定要保留的列索引 F2 fault:67  train_samples:1000-2000 |test:2000:3000
% selected_columns = [3  7 9 11  15  17 18 19   25 ...
%                     31 36 37 43  53  65 67 71 73 82 145  165 ...
%                     173 200 209 259 271 273];

% % 指定要保留的列索引 F3 fault:30 train_samples:2000-3000  |test:3000-4000
% selected_columns = [3  7 9 11  15  17 18 19   25 ...
%                     31 36 37 43  53  65 67 71 73 82 145  165 ...
%                     173 200 209 259 271 273];

% % 指定要保留的列索引 F4 fault:82 train_samples:1000-2000  |test:3000-4000
% selected_columns = [3  7 9 11  15  17 18 19   25 ...
%                     31 36 37 43  53  65 67 71 73 82 145  165 ...
%                     173 200 209 259 271 273];




% 选出这些列
data_selected = data_new(:, selected_columns);


% %f1
data_selected_new=data_selected(2000:3000, :);
test_data=data_selected(3000:4000, :);
test_data(:,10)=data_selected(1000:2000, 10);
test_data(:,19)=data_selected(500:1500, 19);
data_selected_new(:,16)=data_selected(1000:2000, 16);
data_selected_new(:,10)=data_selected(1000:2000, 10);
data_selected_new(:,19)=data_selected(500:1500, 19);
data_selected=data_selected_new;

% f2
% data_selected_new=data_selected(1000:2000,:);
% test_data=data_selected(1800:2800,:);
% data_selected=data_selected_new;

% f3
% data_selected_new=data_selected(2170:3170,:);
% test_data=data_selected(3000:4000,:);
% test_data(:,16)=data_selected(2000:3000, 16);
% data_selected=data_selected_new;

% f4
% data_selected_new=data_selected(1000:2000,:);
% data_selected_new(:,3)=data_selected(1500:2500,3);
% data_selected_new(:,4)=data_selected(2000:3000,4);
% data_selected_new(:,8)=data_selected(1500:2500,8);
% data_selected_new(:,11)=data_selected(2000:3000,11);
% data_selected_new(:,14)=data_selected(752:1752,14);
% data_selected_new(:,16)=data_selected(951:1951,16);
% data_selected_new(:,20)=data_selected(935:1935,20);
% data_selected_new(:,26)=data_selected(2143:3143,26);
% 
% test_data=data_selected_new;
% test_data(:,19)=data_selected(2442:3442, 19);
% data_selected=data_selected_new;



%test_data = cell2mat(test_data);

% 可选：保存结果
save('data_selected.mat', 'data_selected');
save('test_data.mat', 'test_data');
% 假设已经有 data_selected 变量

data_selected_num = cell2mat(data_selected);





% [nRows, nCols] = size(data_selected_num);
% for col = 1:nCols
%     figure;
%     plot(1:nRows, data_selected_num(:, col), 'LineWidth', 1.2);
%     xlabel('样本编号');
%     ylabel('数值');
%     title(['data\_selected 第' num2str(selected_columns(col)) '列折线图']);
%     grid on;
% end

