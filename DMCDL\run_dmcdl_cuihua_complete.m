%% DMCDL催化数据集完整分析
% 基于compute_dictionary_dmcdl_cuihua.m的一键运行脚本

clc; clear; close all;
rng(42);

fprintf('========== DMCDL催化数据集完整分析 ==========\n');
fprintf('数据集: 催化数据 (data_selected_F1~F3 + test_data_F1~F3)\n');
fprintf('方法: DMCDL (双重记忆持续字典学习)\n');
fprintf('分析内容: 字典学习 + 监测性能 + 双阈值比较\n\n');

%% 1. 检查必要文件
fprintf('🔍 检查必要文件...\n');

required_files = {
    'data_selected_F1.mat', 'data_selected_F2.mat', 'data_selected_F3.mat', ...
    'test_data_F1.mat', 'test_data_F2.mat', 'test_data_F3.mat'
};

missing_files = {};
for i = 1:length(required_files)
    if exist(required_files{i}, 'file')
        fprintf('   ✓ %s\n', required_files{i});
    else
        fprintf('   ❌ %s (缺失)\n', required_files{i});
        missing_files{end+1} = required_files{i};
    end
end

if ~isempty(missing_files)
    fprintf('   请确保所有必要的催化数据文件都在DMCDL目录中。\n');
    return;
end

fprintf('✅ 文件检查完成\n\n');

%% 2. DMCDL字典学习
fprintf('📚 开始DMCDL字典学习...\n');

learning_time = 0;
try
    tic;
    compute_dictionary_dmcdl_cuihua;
    learning_time = toc;
    fprintf('   ✅ DMCDL字典学习完成，耗时: %.1f秒\n', learning_time);
catch ME
    fprintf('   ❌ DMCDL字典学习失败: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('   错误位置: %s 第%d行\n', ME.stack(1).name, ME.stack(1).line);
    end
    return;
end

%% 3. 监测性能分析
fprintf('\n🎯 开始监测性能分析...\n');

monitoring_time = 0;
try
    tic;
    dmcdl_cuihua_monitoring;
    monitoring_time = toc;
    fprintf('   ✅ 监测性能分析完成，耗时: %.1f秒\n', monitoring_time);
catch ME
    fprintf('   ❌ 监测性能分析失败: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('   错误位置: %s 第%d行\n', ME.stack(1).name, ME.stack(1).line);
    end
    return;
end

%% 4. 加载并整合结果
fprintf('\n📋 整合分析结果...\n');

try
    % 加载字典学习结果
    load('dmcdl_cuihua_results.mat', 'dmcdl_cuihua_results');
    learning_results = dmcdl_cuihua_results;

    % 加载监测性能结果
    load('dmcdl_cuihua_monitoring_results.mat', 'dmcdl_cuihua_monitoring_results');
    monitoring_results = dmcdl_cuihua_monitoring_results;
    
    fprintf('   ✓ 成功加载所有分析结果\n');
    
catch ME
    fprintf('   ❌ 结果加载失败: %s\n', ME.message);
    return;
end

%% 5. 生成综合报告
fprintf('\n📄 生成综合分析报告...\n');

fprintf('\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
fprintf('📊 DMCDL催化数据集综合分析报告\n');
fprintf('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');

%% 5.1 基本信息
fprintf('\n🔧 基本配置:\n');
fprintf('   方法名称: %s\n', learning_results.method);
fprintf('   数据集: 催化数据 (F1, F2, F3)\n');
fprintf('   字典大小: %dx%d\n', size(learning_results.Dictionary_history{1}));
fprintf('   模式数量: %d\n', length(learning_results.Dictionary_history));
fprintf('   稀疏度: %d\n', monitoring_results.sparsity);

%% 5.2 字典演化性能
fprintf('\n📈 字典演化性能:\n');

% 计算NMSC
Dictionary_history = learning_results.Dictionary_history;
n_modes = length(Dictionary_history);
NMSC_history = zeros(n_modes-1, 1);
epsilon = 1e-8;

for i = 2:n_modes
    D_prev = Dictionary_history{i-1};
    D_curr = Dictionary_history{i};
    deltaD = D_curr - D_prev;
    NMSC_history(i-1) = mean((deltaD(:).^2) ./ (D_prev(:).^2 + epsilon));
end

fprintf('   NMSC (归一化均方变化度):\n');
for i = 1:length(NMSC_history)
    fprintf('     F%d→F%d: %.6f\n', i, i+1, NMSC_history(i));
end
fprintf('     平均值: %.6f\n', mean(NMSC_history));

%% 5.3 记忆机制分析
fprintf('\n🧠 记忆机制性能:\n');

Weight_history = learning_results.Weight_history;
Memory_history = learning_results.Memory_history;
Threshold_history = learning_results.Threshold_history;

fprintf('   权重矩阵演化:\n');
for i = 1:length(Weight_history)
    W = Weight_history{i};
    weight_norm = norm(W, 'fro');
    fprintf('     F%d: 权重范数=%.4f\n', i, weight_norm);
end

fprintf('   记忆池演化:\n');
for i = 1:length(Memory_history)
    Yh = Memory_history{i};
    memory_size = size(Yh, 2);
    fprintf('     F%d: 大小=%d, 阈值=%.6f\n', i, memory_size, Threshold_history(i));
end

%% 5.4 监测性能
fprintf('\n🎯 过程监测性能:\n');

FAR_all = monitoring_results.FAR_all;
FDR_all = monitoring_results.FDR_all;
FAR_overall = monitoring_results.FAR_overall;
FDR_overall = monitoring_results.FDR_overall;

fprintf('   各数据集检测性能:\n');
fprintf('     数据集    FAR      FDR\n');
fprintf('     ------   ------   ------\n');
for d = 1:length(FAR_all)
    fprintf('     F%d       %.4f   %.4f\n', d, FAR_all(d), FDR_all(d));
end

fprintf('   总体性能:\n');
fprintf('     平均FAR: %.4f (误报率，越小越好)\n', FAR_overall);
fprintf('     平均FDR: %.4f (检出率，越大越好)\n', FDR_overall);

%% 5.5 性能评估
fprintf('\n⭐ 性能评估:\n');

% 字典稳定性评估
avg_nmsc = mean(NMSC_history);
if avg_nmsc < 0.1
    fprintf('   字典稳定性: 优秀 (NMSC=%.6f)\n', avg_nmsc);
elseif avg_nmsc < 0.5
    fprintf('   字典稳定性: 良好 (NMSC=%.6f)\n', avg_nmsc);
else
    fprintf('   字典稳定性: 一般 (NMSC=%.6f)\n', avg_nmsc);
end

% 监测性能评估
if FAR_overall < 0.05 && FDR_overall > 0.8
    fprintf('   监测性能: 优秀 (FAR=%.4f, FDR=%.4f)\n', FAR_overall, FDR_overall);
elseif FAR_overall < 0.1 && FDR_overall > 0.7
    fprintf('   监测性能: 良好 (FAR=%.4f, FDR=%.4f)\n', FAR_overall, FDR_overall);
else
    fprintf('   监测性能: 需改进 (FAR=%.4f, FDR=%.4f)\n', FAR_overall, FDR_overall);
end

% 记忆机制评估
final_memory_size = size(Memory_history{end}, 2);
final_weight_norm = norm(Weight_history{end}, 'fro');
if final_memory_size >= 5 && final_weight_norm > 0.1
    fprintf('   记忆机制: 有效 (大小=%d, 权重范数=%.4f)\n', final_memory_size, final_weight_norm);
else
    fprintf('   记忆机制: 需优化 (大小=%d, 权重范数=%.4f)\n', final_memory_size, final_weight_norm);
end

%% 5.6 方法比较
if isfield(monitoring_results, 'comparison_results') && ...
   length(fieldnames(monitoring_results.comparison_results)) > 1
    
    fprintf('\n🔍 方法比较:\n');
    comparison = monitoring_results.comparison_results;
    methods = fieldnames(comparison);
    
    fprintf('     方法      FAR      FDR\n');
    fprintf('     ------   ------   ------\n');
    for i = 1:length(methods)
        method = methods{i};
        far_val = comparison.(method).FAR;
        fdr_val = comparison.(method).FDR;
        fprintf('     %-8s %.4f   %.4f\n', method, far_val, fdr_val);
    end
    
    % 排名分析
    FAR_values = arrayfun(@(i) comparison.(methods{i}).FAR, 1:length(methods));
    FDR_values = arrayfun(@(i) comparison.(methods{i}).FDR, 1:length(methods));
    
    [~, best_far_idx] = min(FAR_values);
    [~, best_fdr_idx] = max(FDR_values);
    
    fprintf('\n   🏆 性能排名:\n');
    fprintf('     最低FAR: %s (%.4f)\n', methods{best_far_idx}, FAR_values(best_far_idx));
    fprintf('     最高FDR: %s (%.4f)\n', methods{best_fdr_idx}, FDR_values(best_fdr_idx));
end

%% 6. 保存综合结果
fprintf('\n💾 保存综合分析结果...\n');

comprehensive_results = struct();
comprehensive_results.method = 'DMCDL_Cuihua_Complete';
comprehensive_results.learning_results = learning_results;
comprehensive_results.monitoring_results = monitoring_results;
comprehensive_results.analysis_time = struct('learning_time', learning_time, ...
                                            'monitoring_time', monitoring_time, ...
                                            'total_time', learning_time + monitoring_time);

save('dmcdl_cuihua_comprehensive_results.mat', 'comprehensive_results');
fprintf('   综合结果已保存到: dmcdl_cuihua_comprehensive_results.mat\n');

%% 7. 输出文件总结
fprintf('\n📁 生成的文件总结:\n');

output_files = {
    'cuihua_dmcdl_results.mat', '字典学习结果';
    'D_final_cuihua_dmcdl.mat', '最终字典';
    'dmcdl_cuihua_analysis.fig', '字典演化分析图';
    'dmcdl_cuihua_monitoring_results.mat', '监测性能结果';
    'dmcdl_cuihua_monitoring.fig', '监测性能图';
    'cuihua_methods_comparison.fig', '方法比较图';
    'dmcdl_cuihua_comprehensive_results.mat', '综合分析结果'
};

fprintf('   生成的文件:\n');
for i = 1:size(output_files, 1)
    filename = output_files{i, 1};
    description = output_files{i, 2};
    
    if exist(filename, 'file')
        file_info = dir(filename);
        fprintf('     ✓ %s (%.1f KB) - %s\n', filename, file_info.bytes/1024, description);
    else
        fprintf('     ❌ %s - %s (未生成)\n', filename, description);
    end
end

%% 8. 使用建议
fprintf('\n💡 使用建议:\n');
fprintf('   1. 查看字典演化: open(''dmcdl_cuihua_analysis.fig'')\n');
fprintf('   2. 查看监测性能: open(''dmcdl_cuihua_monitoring.fig'')\n');
fprintf('   3. 查看方法比较: open(''cuihua_methods_comparison.fig'')\n');
fprintf('   4. 加载详细数据: load(''dmcdl_cuihua_comprehensive_results.mat'')\n');
fprintf('   5. 参数调优: 修改cuihua_learn_D_dmcdl.m中的参数\n');

fprintf('\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
fprintf('🎉 DMCDL催化数据集完整分析完成！\n');
fprintf('总耗时: %.1f分钟\n', (learning_time + monitoring_time)/60);
fprintf('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
