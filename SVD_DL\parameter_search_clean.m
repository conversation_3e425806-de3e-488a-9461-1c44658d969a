%% 简洁版参数搜索实验 - 优化的日志输出
% 修改自parameter_grid_search.m，减少不必要的输出信息

rng(42);

%% ========== 参数网格搜索设置 ==========
% 定义参数范围 - 细化搜索步长
% lambda: 1e-6到1e4，步长为5倍
lambda_list = [1e-6, 5e-6, 1e-5, 5e-5, 1e-4, 5e-4, 1e-3, 5e-3, 1e-2, 5e-2, 1e-1, 5e-1, ...
                1, 5, 10, 50, 100, 500, 1000, 5000, 10000];  % 19个值
% n_atoms: 30到70，步长为2
n_atoms_list = 20; 
% sparsity: 1,2,3
sparsity_list = [2];  % 1个值

% 计算总的参数组合数
n_lambda = length(lambda_list);
n_atoms_count = length(n_atoms_list);
n_sparsity = length(sparsity_list);
total_combinations = n_lambda * n_atoms_count * n_sparsity;

fprintf('========== 细化参数网格搜索开始 ==========\n');
fprintf('参数范围:\n');
fprintf('  lambda: %d个值 (1e-6 到 10, 步长5倍)\n', n_lambda);
fprintf('  n_atoms: %d个值 (30 到 70, 步长2)\n', n_atoms_count);
fprintf('  sparsity: %d个值 (1, 2, 3)\n', n_sparsity);
fprintf('总共 %d 个参数组合 (%d × %d × %d)\n', total_combinations, n_lambda, n_atoms_count, n_sparsity);
fprintf('预计运行时间: 8-15小时 (取决于计算机性能)\n');
fprintf('进度显示: 每20个组合显示一次\n\n');

% 存储所有结果
results = struct();
results.lambda_vals = [];
results.n_atoms_vals = [];
results.sparsity_vals = [];
results.FAR_vals = [];
results.FDR_vals = [];
results.R_test_all = {};
results.R_limit_vals = [];

combination_idx = 0;
success_count = 0;
start_time = tic;

% 三重嵌套循环进行网格搜索
for ll = 1:n_lambda
    lambda = lambda_list(ll);
    
    for aa = 1:n_atoms_count
        n_atoms = n_atoms_list(aa);
        
        for ss = 1:n_sparsity
            sparsity = sparsity_list(ss);
            combination_idx = combination_idx + 1;
            
            % 简洁的进度显示 - 每20个组合显示一次
            if mod(combination_idx, 20) == 1 || combination_idx == total_combinations
                elapsed = toc(start_time);
                if combination_idx > 1
                    avg_time = elapsed / (combination_idx - 1);
                    remaining = avg_time * (total_combinations - combination_idx + 1);
                    progress_pct = (combination_idx - 1) / total_combinations * 100;
                    fprintf('[%d/%d] %.1f%% | λ=%.0e, n=%d, s=%d | 已用时:%.1f分钟, 预计剩余:%.1f分钟\n', ...
                            combination_idx, total_combinations, progress_pct, lambda, n_atoms, sparsity, ...
                            elapsed/60, remaining/60);
                else
                    fprintf('[%d/%d] 开始: λ=%.0e, n=%d, s=%d\n', ...
                            combination_idx, total_combinations, lambda, n_atoms, sparsity);
                end
            end
            
            try
                %% ========== 1. 加载训练数据 ==========
                load('mode1_train.mat');  % 变量 train_data，1000x8
                Y = train_data';          % [8 x 1000]
                
                %% ========== 2. K-SVD训练 ==========
                n_iter = 50;
                D_init = randn(8, n_atoms);
                for k = 1:n_atoms
                    D_init(:,k) = D_init(:,k) / norm(D_init(:,k));
                end
                
                % 使用静默版本的K-SVD
                [Dictionary, ~] = ksvd_simple_silent(Y, D_init, sparsity, n_iter);

                [U, S, V] = svd(Dictionary, 'econ');
                singular_values = diag(S);
                energy = cumsum(singular_values.^2) / sum(singular_values.^2);
                k_locked = find(energy >= 0.9, 1, 'first');
                U_locked = U(:, 1:k_locked);

                Dictionary_history = cell(5,1);
                U_locked_history = cell(5,1);
                Dictionary_history{1} = Dictionary;
                U_locked_history{1} = U_locked;
                D_prev = Dictionary;
                U_locked_prev = U_locked;
                k_locked_prev = k_locked;
                
                for mode = 2:5
                    % === 新mode训练 ===
                    fname = sprintf('mode%d_train.mat', mode);
                    load(fname); Y_new = train_data';
                    n_atoms_current = size(D_prev,2);  % 避免变量名冲突
                    D_init = randn(8, n_atoms_current);
                    for k = 1:n_atoms_current
                        D_init(:,k) = D_init(:,k) / norm(D_init(:,k));
                    end
                    [D_new, ~] = ksvd_simple_silent(Y_new, D_init, sparsity, 30);

                    % === SVD判定主原子 ===
                    [Uo, So, Vo] = svd(D_prev, 'econ');
                    S_diag = diag(So);
                    V_weight = abs(Vo) * S_diag;
                    [~, idx] = sort(V_weight, 'descend');
                    V_weight_sorted = V_weight(idx);
                    energy_cum = cumsum(V_weight_sorted) / sum(V_weight_sorted);
                    k_important = find(energy_cum >= 0.9, 1, 'first');
                    imp_idx_old = idx(1:k_important);
                    unimp_idx_old = idx(k_important+1:end);

                    [~, S_new, V_new] = svd(D_new, 'econ');
                    V_weight_new = abs(V_new) * diag(S_new);
                    [~, idx_new] = sort(V_weight_new, 'descend');
                    V_weight_sorted_new = V_weight_new(idx_new);
                    energy_cum_new = cumsum(V_weight_sorted_new) / sum(V_weight_sorted_new);
                    k_important_new = find(energy_cum_new >= 0.9, 1, 'first');
                    imp_idx_new = idx_new(1:k_important_new);

                    D_fused = D_prev;
                    replace_cnt = min(numel(imp_idx_new), numel(unimp_idx_old));
                    D_fused(:, unimp_idx_old(1:replace_cnt)) = D_new(:, imp_idx_new(1:replace_cnt));

                    % === 主空间保护微调 ===
                    U_locked = Uo(:, 1:k_locked_prev);
                    P_locked = U_locked * U_locked';
                    X = zeros(size(D_fused,2), size(Y_new,2));
                    for n = 1:size(Y_new,2)
                        X(:,n) = omp(D_fused, Y_new(:,n), sparsity);
                    end
                    A = lambda * (P_locked') * P_locked;
                    B = X * X';
                    C = lambda * (P_locked') * P_locked * D_prev + Y_new * X';
                    D_fused = sylvester(A,B,C);

                    for k = 1:size(D_fused,2)
                        D_fused(:,k) = D_fused(:,k) / norm(D_fused(:,k));
                    end

                    [U_new, S_new, ~] = svd(D_fused, 'econ');
                    singular_values_new = diag(S_new);
                    energy_new = cumsum(singular_values_new.^2) / sum(singular_values_new.^2);
                    k_locked_new = find(energy_new >= 0.9, 1, 'first');
                    U_locked_new = U_new(:, 1:k_locked_new);

                    Dictionary_history{mode} = D_fused;
                    U_locked_history{mode} = U_locked_new;
                    D_prev = D_fused;
                    U_locked_prev = U_locked_new;
                    k_locked_prev = k_locked_new;
                end
                
                %% ========== 字典训练后性能评估 ==========
                D_K = Dictionary_history{end};

                % --- 训练及测试数据准备 ---
                Y_train = []; Y_test = [];
                for mm = 1:5
                    load(sprintf("mode%d_train.mat",mm)); Y_train=[Y_train;train_data];
                    load(sprintf("mode%d_test_normal.mat",mm)); Y_test=[Y_test;test_normal_data];
                    load(sprintf("mode%d_test_fault.mat",mm));  Y_test=[Y_test;test_fault_data];
                end
                Y_train = Y_train'; Y_test = Y_test';

                % === 训练集编码+统计量
                R_train = zeros(1, size(Y_train,2));
                for i = 1:size(Y_train,2)
                    y = Y_train(:,i);
                    x = omp(D_K, y, sparsity);
                    R_train(i) = norm(y - D_K*x, 2)^2;
                end
                
                % 检查并处理 NaN 值
                if any(isnan(R_train)) || any(isinf(R_train))
                    % 存储失败的结果
                    results.lambda_vals(end+1) = lambda;
                    results.n_atoms_vals(end+1) = n_atoms;
                    results.sparsity_vals(end+1) = sparsity;
                    results.FAR_vals(end+1) = NaN;
                    results.FDR_vals(end+1) = NaN;
                    results.R_test_all{end+1} = [];
                    results.R_limit_vals(end+1) = NaN;
                    continue;
                end
                
                % 移除可能的异常值
                R_train_clean = R_train(~isnan(R_train) & ~isinf(R_train));
                if length(R_train_clean) < 10
                    % 存储失败的结果
                    results.lambda_vals(end+1) = lambda;
                    results.n_atoms_vals(end+1) = n_atoms;
                    results.sparsity_vals(end+1) = sparsity;
                    results.FAR_vals(end+1) = NaN;
                    results.FDR_vals(end+1) = NaN;
                    results.R_test_all{end+1} = [];
                    results.R_limit_vals(end+1) = NaN;
                    continue;
                end
                
                [f_R, xi_R] = ksdensity(R_train_clean, 'Function', 'cdf');
                idx_R = find(f_R >= 1 - 0.01, 1, 'first');
                R_limit = xi_R(idx_R);

                % === 测试集编码+统计量
                R_test = zeros(1, size(Y_test,2));
                for i = 1:size(Y_test,2)
                    y = Y_test(:,i);
                    x = omp(D_K, y, sparsity);
                    R_test(i) = norm(y - D_K*x, 2)^2;
                end
                
                % 检查测试集统计量
                if any(isnan(R_test)) || any(isinf(R_test))
                    % 存储失败的结果
                    results.lambda_vals(end+1) = lambda;
                    results.n_atoms_vals(end+1) = n_atoms;
                    results.sparsity_vals(end+1) = sparsity;
                    results.FAR_vals(end+1) = NaN;
                    results.FDR_vals(end+1) = NaN;
                    results.R_test_all{end+1} = [];
                    results.R_limit_vals(end+1) = NaN;
                    continue;
                end

                % === 计算FAR/FDR ===
                n_mode = 5; n_each = 1000; n_normal = 500; n_fault = 500;
                FAR_vec = zeros(n_mode,1); FDR_vec = zeros(n_mode,1);
                for m = 1:n_mode
                    idx_start = (m-1)*n_each + 1;
                    idx_normal = idx_start : idx_start + n_normal - 1;
                    idx_fault  = idx_start + n_normal : idx_start + n_each - 1;
                    FAR_vec(m) = sum(R_test(idx_normal) > R_limit) / n_normal;
                    FDR_vec(m) = sum(R_test(idx_fault)  > R_limit) / n_fault;
                end
                FAR_current = mean(FAR_vec);
                FDR_current = mean(FDR_vec);
                
                % 检查FAR/FDR结果
                if isnan(FAR_current) || isnan(FDR_current) || isinf(FAR_current) || isinf(FDR_current)
                    % 存储失败的结果
                    results.lambda_vals(end+1) = lambda;
                    results.n_atoms_vals(end+1) = n_atoms;
                    results.sparsity_vals(end+1) = sparsity;
                    results.FAR_vals(end+1) = NaN;
                    results.FDR_vals(end+1) = NaN;
                    results.R_test_all{end+1} = [];
                    results.R_limit_vals(end+1) = NaN;
                    continue;
                end

                % 存储当前参数组合的结果
                results.lambda_vals(end+1) = lambda;
                results.n_atoms_vals(end+1) = n_atoms;
                results.sparsity_vals(end+1) = sparsity;
                results.FAR_vals(end+1) = FAR_current;
                results.FDR_vals(end+1) = FDR_current;
                results.R_test_all{end+1} = R_test;
                results.R_limit_vals(end+1) = R_limit;
                
                success_count = success_count + 1;
                
                % 只在特别好的结果时显示详细信息
                if FDR_current > 0.95 || FAR_current < 0.01
                    fprintf('    ★ 优秀结果: FAR=%.4f, FDR=%.4f\n', FAR_current, FDR_current);
                end
                    
            catch ME
                % 存储失败的结果
                results.lambda_vals(end+1) = lambda;
                results.n_atoms_vals(end+1) = n_atoms;
                results.sparsity_vals(end+1) = sparsity;
                results.FAR_vals(end+1) = NaN;
                results.FDR_vals(end+1) = NaN;
                results.R_test_all{end+1} = [];
                results.R_limit_vals(end+1) = NaN;
                
                % 只在每20个错误时显示一次
                if mod(combination_idx, 20) == 0
                    fprintf('    ✗ 错误: %s\n', ME.message);
                end
            end
            
        end  % sparsity loop
    end  % n_atoms loop
end  % lambda loop

%% ========== 分析最佳参数组合 ==========
total_time = toc(start_time);
fprintf('\n========== 参数搜索完成 ==========\n');
fprintf('总运行时间: %.1f 分钟\n', total_time/60);
fprintf('总共测试了 %d 个参数组合\n', length(results.FDR_vals));

% 移除失败的结果
valid_idx = ~isnan(results.FDR_vals);
if sum(valid_idx) == 0
    fprintf('所有参数组合都失败了！请检查数据和参数设置。\n');
    return;
end

fprintf('成功的参数组合: %d 个\n', sum(valid_idx));
fprintf('失败的参数组合: %d 个\n', sum(~valid_idx));

% 找到最佳FDR对应的参数组合
valid_FDR = results.FDR_vals(valid_idx);
valid_lambda = results.lambda_vals(valid_idx);
valid_n_atoms = results.n_atoms_vals(valid_idx);
valid_sparsity = results.sparsity_vals(valid_idx);
valid_FAR = results.FAR_vals(valid_idx);

[maxFDR, max_idx] = max(valid_FDR);
best_lambda = valid_lambda(max_idx);
best_n_atoms = valid_n_atoms(max_idx);
best_sparsity = valid_sparsity(max_idx);
best_FAR = valid_FAR(max_idx);

% 找到在原始数组中的索引
idx_maxFDR = find(results.FDR_vals == maxFDR & results.lambda_vals == best_lambda & ...
                  results.n_atoms_vals == best_n_atoms & results.sparsity_vals == best_sparsity, 1, 'first');

fprintf('\n🎯 最佳FDR = %.4f\n', maxFDR);
fprintf('对应参数组合:\n');
fprintf('  lambda = %.2e\n', best_lambda);
fprintf('  n_atoms = %d\n', best_n_atoms);
fprintf('  sparsity = %d\n', best_sparsity);
fprintf('  对应FAR = %.4f\n', best_FAR);

% 找到最小FAR对应的参数组合
[minFAR, min_idx] = min(valid_FAR);
minFAR_lambda = valid_lambda(min_idx);
minFAR_n_atoms = valid_n_atoms(min_idx);
minFAR_sparsity = valid_sparsity(min_idx);
minFAR_FDR = valid_FDR(min_idx);

% 找到在原始数组中的索引
idx_minFAR = find(results.FAR_vals == minFAR & results.lambda_vals == minFAR_lambda & ...
                  results.n_atoms_vals == minFAR_n_atoms & results.sparsity_vals == minFAR_sparsity, 1, 'first');

fprintf('\n🎯 最小FAR = %.4f\n', minFAR);
fprintf('对应参数组合:\n');
fprintf('  lambda = %.2e\n', minFAR_lambda);
fprintf('  n_atoms = %d\n', minFAR_n_atoms);
fprintf('  sparsity = %d\n', minFAR_sparsity);
fprintf('  对应FDR = %.4f\n', minFAR_FDR);

% 显示前5个最佳FDR结果
fprintf('\n📊 前5个最佳FDR结果:\n');
[sorted_FDR, sort_idx] = sort(valid_FDR, 'descend');
for i = 1:min(5, length(sorted_FDR))
    idx = sort_idx(i);
    fprintf('  %d. FDR=%.4f, FAR=%.4f (λ=%.0e, n=%d, s=%d)\n', ...
            i, sorted_FDR(i), valid_FAR(idx), valid_lambda(idx), valid_n_atoms(idx), valid_sparsity(idx));
end

% 显示前5个最小FAR结果
fprintf('\n📊 前5个最小FAR结果:\n');
[sorted_FAR, sort_idx] = sort(valid_FAR, 'ascend');
for i = 1:min(5, length(sorted_FAR))
    idx = sort_idx(i);
    fprintf('  %d. FAR=%.4f, FDR=%.4f (λ=%.0e, n=%d, s=%d)\n', ...
            i, sorted_FAR(i), valid_FDR(idx), valid_lambda(idx), valid_n_atoms(idx), valid_sparsity(idx));
end

%% ========== 简化的可视化结果 ==========
if sum(valid_idx) > 0
    % 1. 最优FDR对应参数组合的监测统计量
    figure('Position', [100, 100, 800, 400]);
    R_test_opt = results.R_test_all{idx_maxFDR};
    R_limit_opt = results.R_limit_vals(idx_maxFDR);
    if ~isempty(R_test_opt) && ~isnan(R_limit_opt)
        plot(R_test_opt, 'b', 'LineWidth', 1.2); hold on;
        yline(R_limit_opt, '--r', 'LineWidth', 2);
        xlabel('样本编号'); ylabel('R统计量');
        title(sprintf('最优FDR参数组合 (λ=%.2e, n=%d, s=%d) 监测统计量', ...
                      best_lambda, best_n_atoms, best_sparsity));
        legend('R统计量','控制限', 'Location', 'best');
        grid on;
        set(gca,'FontSize',12);
    end

    % 2. 参数对FDR的影响 - 3D散点图
    if sum(valid_idx) > 1
        figure('Position', [100, 100, 1200, 800]);

        % 3D散点图 - 主图
        subplot(2,2,1);
        scatter3(valid_lambda, valid_n_atoms, valid_sparsity, ...
                 60, valid_FDR, 'filled', 'MarkerFaceAlpha', 0.8);
        xlabel('Lambda'); ylabel('N\_atoms'); zlabel('Sparsity');
        title('FDR随三个参数的变化 (3D视图)');
        colorbar;
        colormap('jet');
        set(gca, 'XScale', 'log');
        grid on;
        view(45, 30);  % 设置3D视角

        % 标记最佳点
        hold on;
        scatter3(best_lambda, best_n_atoms, best_sparsity, ...
                 150, maxFDR, 'r', 'filled', '^', 'MarkerEdgeColor', 'k', 'LineWidth', 2);

        % 各参数对FDR的单独影响
        subplot(2,2,2);
        scatter(valid_lambda, valid_FDR, 40, valid_FDR, 'filled', 'MarkerFaceAlpha', 0.7);
        xlabel('Lambda'); ylabel('FDR');
        title('Lambda对FDR的影响');
        set(gca, 'XScale', 'log');
        grid on;
        colorbar;
        hold on;
        scatter(best_lambda, maxFDR, 100, 'r', 'filled', '^', 'MarkerEdgeColor', 'k');

        subplot(2,2,3);
        scatter(valid_n_atoms, valid_FDR, 40, valid_FDR, 'filled', 'MarkerFaceAlpha', 0.7);
        xlabel('N\_atoms'); ylabel('FDR');
        title('N\_atoms对FDR的影响');
        grid on;
        colorbar;
        hold on;
        scatter(best_n_atoms, maxFDR, 100, 'r', 'filled', '^', 'MarkerEdgeColor', 'k');

        subplot(2,2,4);
        scatter(valid_sparsity, valid_FDR, 40, valid_FDR, 'filled', 'MarkerFaceAlpha', 0.7);
        xlabel('Sparsity'); ylabel('FDR');
        title('Sparsity对FDR的影响');
        grid on;
        colorbar;
        hold on;
        scatter(best_sparsity, maxFDR, 100, 'r', 'filled', '^', 'MarkerEdgeColor', 'k');

        % 添加总标题
        sgtitle(sprintf('参数对FDR的影响分析 (最佳: λ=%.0e, n=%d, s=%d, FDR=%.4f)', ...
                       best_lambda, best_n_atoms, best_sparsity, maxFDR), 'FontSize', 14);

        % 额外的3D视图 - 不同角度
        figure('Position', [150, 150, 1000, 600]);

        subplot(1,2,1);
        scatter3(valid_lambda, valid_n_atoms, valid_sparsity, ...
                 60, valid_FDR, 'filled', 'MarkerFaceAlpha', 0.8);
        xlabel('Lambda'); ylabel('N\_atoms'); zlabel('Sparsity');
        title('3D参数空间 - 视角1');
        colorbar;
        colormap('jet');
        set(gca, 'XScale', 'log');
        grid on;
        view(0, 90);  % 俯视图
        hold on;
        scatter3(best_lambda, best_n_atoms, best_sparsity, ...
                 150, maxFDR, 'r', 'filled', '^', 'MarkerEdgeColor', 'k', 'LineWidth', 2);

        subplot(1,2,2);
        scatter3(valid_lambda, valid_n_atoms, valid_sparsity, ...
                 60, valid_FDR, 'filled', 'MarkerFaceAlpha', 0.8);
        xlabel('Lambda'); ylabel('N\_atoms'); zlabel('Sparsity');
        title('3D参数空间 - 视角2');
        colorbar;
        colormap('jet');
        set(gca, 'XScale', 'log');
        grid on;
        view(-45, 15);  % 侧视图
        hold on;
        scatter3(best_lambda, best_n_atoms, best_sparsity, ...
                 150, maxFDR, 'r', 'filled', '^', 'MarkerEdgeColor', 'k', 'LineWidth', 2);

        sgtitle('3D参数空间的不同视角', 'FontSize', 14);

        % 参数热力图分析
        figure('Position', [200, 200, 1200, 400]);

        % Lambda vs N_atoms 热力图 (对每个sparsity)
        for s_idx = 1:length(sparsity_list)
            subplot(1, 3, s_idx);

            % 筛选当前sparsity的数据
            current_sparsity = sparsity_list(s_idx);
            mask = valid_sparsity == current_sparsity;

            if sum(mask) > 0
                current_lambda = valid_lambda(mask);
                current_n_atoms = valid_n_atoms(mask);
                current_FDR = valid_FDR(mask);

                % 创建网格数据
                unique_lambda = unique(current_lambda);
                unique_n_atoms = unique(current_n_atoms);

                if length(unique_lambda) > 1 && length(unique_n_atoms) > 1
                    [Lambda_grid, N_atoms_grid] = meshgrid(unique_lambda, unique_n_atoms);
                    FDR_grid = NaN(size(Lambda_grid));

                    for i = 1:length(current_lambda)
                        lambda_idx = find(unique_lambda == current_lambda(i));
                        n_atoms_idx = find(unique_n_atoms == current_n_atoms(i));
                        if ~isempty(lambda_idx) && ~isempty(n_atoms_idx)
                            FDR_grid(n_atoms_idx, lambda_idx) = current_FDR(i);
                        end
                    end

                    % 绘制热力图
                    imagesc(unique_lambda, unique_n_atoms, FDR_grid);
                    set(gca, 'XScale', 'log');
                    set(gca, 'YDir', 'normal');
                    colorbar;
                    colormap('jet');
                    xlabel('Lambda');
                    ylabel('N\_atoms');
                    title(sprintf('FDR热力图 (sparsity=%d)', current_sparsity));

                    % 标记最佳点
                    if best_sparsity == current_sparsity
                        hold on;
                        scatter(best_lambda, best_n_atoms, 100, 'w', 'filled', '^', ...
                               'MarkerEdgeColor', 'k', 'LineWidth', 2);
                    end
                else
                    % 如果数据不足以创建热力图，显示散点图
                    scatter(current_lambda, current_n_atoms, 60, current_FDR, 'filled');
                    set(gca, 'XScale', 'log');
                    colorbar;
                    xlabel('Lambda');
                    ylabel('N\_atoms');
                    title(sprintf('FDR散点图 (sparsity=%d)', current_sparsity));
                end
            else
                text(0.5, 0.5, '无数据', 'HorizontalAlignment', 'center', ...
                     'VerticalAlignment', 'middle', 'FontSize', 14);
                title(sprintf('sparsity=%d', current_sparsity));
            end
        end

        sgtitle('不同稀疏度下的参数热力图分析', 'FontSize', 14);
    end

    % 3. FAR vs FDR散点图
    if sum(valid_idx) > 1
        figure('Position', [100, 100, 800, 600]);
        scatter(valid_FAR, valid_FDR, 50, 'filled', 'MarkerFaceAlpha', 0.6); hold on;
        scatter(best_FAR, maxFDR, 100, 'r', 'filled', '^');
        scatter(minFAR, minFAR_FDR, 100, 'b', 'filled', 'v');
        xlabel('FAR'); ylabel('FDR');
        title('FAR vs FDR 散点图');
        legend('所有参数组合', '最大FDR', '最小FAR', 'Location', 'best');
        grid on;
        set(gca,'FontSize',12);

        % 添加帕累托前沿
        % 找到帕累托最优点（低FAR，高FDR）
        pareto_idx = [];
        for i = 1:length(valid_FAR)
            is_pareto = true;
            for j = 1:length(valid_FAR)
                if i ~= j && valid_FAR(j) <= valid_FAR(i) && valid_FDR(j) >= valid_FDR(i) && ...
                   (valid_FAR(j) < valid_FAR(i) || valid_FDR(j) > valid_FDR(i))
                    is_pareto = false;
                    break;
                end
            end
            if is_pareto
                pareto_idx(end+1) = i;
            end
        end

        if length(pareto_idx) > 1
            pareto_FAR = valid_FAR(pareto_idx);
            pareto_FDR = valid_FDR(pareto_idx);
            [sorted_pareto_FAR, sort_idx] = sort(pareto_FAR);
            sorted_pareto_FDR = pareto_FDR(sort_idx);
            plot(sorted_pareto_FAR, sorted_pareto_FDR, 'g-', 'LineWidth', 2);
            legend('所有参数组合', '最大FDR', '最小FAR', '帕累托前沿', 'Location', 'best');
        end
    end
else
    fprintf('没有有效的结果可以可视化。\n');
end

%% ========== 保存结果 ==========
% 保存详细的搜索信息
search_info = struct();
search_info.lambda_list = lambda_list;
search_info.n_atoms_list = n_atoms_list;
search_info.sparsity_list = sparsity_list;
search_info.total_combinations = total_combinations;
search_info.success_rate = success_count / total_combinations;

save('parameter_search_clean_results.mat', 'results', 'best_lambda', 'best_n_atoms', ...
     'best_sparsity', 'maxFDR', 'best_FAR', 'minFAR', 'minFAR_lambda', ...
     'minFAR_n_atoms', 'minFAR_sparsity', 'minFAR_FDR', 'idx_maxFDR', 'idx_minFAR', ...
     'total_time', 'success_count', 'search_info');

fprintf('\n💾 结果已保存到 parameter_search_clean_results.mat\n');
fprintf('📊 搜索统计:\n');
fprintf('   总参数组合: %d\n', total_combinations);
fprintf('   成功组合: %d (%.1f%%)\n', success_count, success_count/total_combinations*100);
fprintf('   总运行时间: %.1f小时\n', total_time/3600);
fprintf('   平均每组合: %.1f秒\n', total_time/total_combinations);
fprintf('\n🚀 推荐使用最佳参数组合: λ=%.2e, n_atoms=%d, sparsity=%d\n', ...
        best_lambda, best_n_atoms, best_sparsity);
fprintf('   该组合实现了 FDR=%.4f, FAR=%.4f\n', maxFDR, best_FAR);
fprintf('\n🎯 参数搜索范围:\n');
fprintf('   Lambda: %.0e 到 %.0e (%d个值)\n', min(lambda_list), max(lambda_list), length(lambda_list));
fprintf('   N_atoms: %d 到 %d (%d个值)\n', min(n_atoms_list), max(n_atoms_list), length(n_atoms_list));
fprintf('   Sparsity: %d 到 %d (%d个值)\n', min(sparsity_list), max(sparsity_list), length(sparsity_list));
