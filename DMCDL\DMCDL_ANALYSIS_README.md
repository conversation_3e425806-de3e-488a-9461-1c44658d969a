# DMCDL方法分析系统

## 🎯 项目目标

仿照SVD_DL方法，为DMCDL (双重记忆持续字典学习) 方法创建完整的字典原子分析和监测性能分析系统。

## 📊 分析内容

### 1. 字典演化分析
- **NMSC**: 归一化均方变化度
- **子空间夹角**: 主方向变化程度
- **主空间维度**: 字典主空间演化
- **权重矩阵**: 记忆权重演化
- **记忆池**: 样本记忆机制分析

### 2. 监测性能分析
- **R统计量**: 重构误差监测
- **FAR/FDR**: 误报率和检出率
- **控制限**: 基于KDE的阈值设定
- **方法比较**: 与SVD_DL、GILDL等方法对比

## 🔧 核心文件

### 1. `analyze_dmcdl_evolution.m` ⭐**字典演化分析**⭐

**功能**: 分析DMCDL字典在5个模式中的演化过程

**核心分析**:
```matlab
% DMCDL特有的分析指标
- 权重矩阵演化 (Weight_history)
- 记忆池变化 (Memory_history) 
- 阈值演化 (Threshold_history)
- 记忆多样性分析
- 数值稳定性评估
```

**输出**:
- `dmcdl_evolution_results.mat`: 演化数据
- `dmcdl_analysis_results.mat`: 分析结果
- `dmcdl_evolution_analysis.fig`: 12子图综合分析

### 2. `dmcdl_monitoring_performance.m` ⭐**监测性能分析**⭐

**功能**: 使用DMCDL最终字典进行过程监测性能评估

**核心流程**:
```matlab
% 1. 加载DMCDL最终字典
D_K = D_final;

% 2. 计算R统计量
R = norm(y - D_K*x, 2)^2;

% 3. 计算FAR/FDR
FAR = 误报率, FDR = 检出率

% 4. 方法比较
与SVD_DL、GILDL等方法对比
```

**输出**:
- `dmcdl_monitoring_results.mat`: 监测数据
- `dmcdl_monitoring_performance.fig`: 监测图表
- `methods_monitoring_comparison.fig`: 方法比较

### 3. `run_dmcdl_comprehensive_analysis.m` ⭐**一键运行**⭐

**功能**: 完整的DMCDL分析流程自动化

**特性**:
- 自动文件检查
- 集成演化和监测分析
- 生成综合报告
- 完整的错误处理

## 🚀 使用方法

### 快速开始
```matlab
cd('DMCDL')
run('run_dmcdl_comprehensive_analysis.m')
```

### 分步执行
```matlab
% 1. 字典演化分析
analyze_dmcdl_evolution

% 2. 监测性能分析  
dmcdl_monitoring_performance

% 3. 查看结果
load('dmcdl_comprehensive_results.mat')
```

### 数据要求
确保DMCDL目录包含：
```
DMCDL/
├── mode1_train.mat, mode1_test_normal.mat, mode1_test_fault.mat
├── mode2_train.mat, mode2_test_normal.mat, mode2_test_fault.mat
├── mode3_train.mat, mode3_test_normal.mat, mode3_test_fault.mat
├── mode4_train.mat, mode4_test_normal.mat, mode4_test_fault.mat
├── mode5_train.mat, mode5_test_normal.mat, mode5_test_fault.mat
├── dmcdl_initial.m, dmcdl_incremental.m
├── build_threshold_and_memory.m
└── omp.m
```

## 📈 分析指标

### 1. DMCDL特有指标

#### 权重矩阵分析
```matlab
Weight_norms(i)      % 权重矩阵Frobenius范数
Weight_traces(i)     % 权重矩阵迹
Weight_conditions(i) % 权重矩阵条件数
```

#### 记忆池分析
```matlab
Memory_sizes(i)       % 记忆池大小
Memory_diversities(i) % 记忆池多样性（样本间平均距离）
Threshold_history(i)  % 重构误差阈值演化
```

### 2. 通用字典学习指标

#### NMSC (归一化均方变化度)
```matlab
NMSC = mean((D_new - D_old)² ./ (D_old² + ε))
```

#### 主方向子空间夹角
```matlab
angle = subspace(U_old, U_new)  % 弧度
```

#### 监测性能
```matlab
FAR = 误报率 (False Alarm Rate)
FDR = 检出率 (Fault Detection Rate)
```

## 🔍 结果解读

### 字典演化性能
- **NMSC < 0.1**: 字典变化小，稳定性高
- **NMSC 0.1-0.5**: 适度变化，平衡稳定性和适应性
- **NMSC > 0.5**: 变化大，适应性强但稳定性差

### 子空间夹角
- **< 30°**: 主方向变化小，一致性好
- **30°-60°**: 中等变化，平衡保持和适应
- **> 60°**: 变化大，可能需要调整参数

### 监测性能
- **FAR < 0.05 & FDR > 0.8**: 性能优秀
- **FAR < 0.1 & FDR > 0.7**: 性能良好
- **其他**: 需要改进

### DMCDL记忆机制
- **权重范数增长**: 记忆积累效果
- **记忆池多样性**: 样本代表性
- **阈值稳定性**: 监测一致性

## 📊 可视化输出

### 1. 字典演化分析图 (12子图)
- 字典热图对比
- NMSC/子空间夹角趋势
- 权重矩阵演化
- 记忆池分析
- 数值稳定性

### 2. 监测性能图 (4子图)
- R统计量时间序列
- 训练/测试数据分布
- 控制限可视化

### 3. 方法比较图
- FAR/FDR柱状图比较
- 多方法性能对比

## 🆚 与SVD_DL方法的对比

### 相同分析
- ✅ NMSC计算
- ✅ 子空间夹角分析
- ✅ 主空间维度演化
- ✅ R统计量监测
- ✅ FAR/FDR评估

### DMCDL特有分析
- 🆕 权重矩阵演化分析
- 🆕 记忆池大小和多样性
- 🆕 阈值演化追踪
- 🆕 双重记忆机制评估
- 🆕 数值稳定性监控

### 分析深度
- **SVD_DL**: 主要关注主空间锁定效果
- **DMCDL**: 额外分析记忆机制和权重演化

## 🎯 应用场景

### 1. 算法开发
- 验证DMCDL记忆机制有效性
- 优化权重更新策略
- 调整记忆池参数

### 2. 性能评估
- 与其他字典学习方法比较
- 评估在不同数据集上的表现
- 分析适用场景

### 3. 参数调优
- 基于演化分析调整超参数
- 优化记忆池大小和更新策略
- 平衡稳定性和适应性

## 🔧 参数调优建议

### learn_D.m中的关键参数
```matlab
n_atoms = 20;      % 字典原子数
sparsity = 2;      % 稀疏度
lambda_x = 1e-3;   % 稀疏惩罚
lambda_1 = 1e-6;   % 初始正则系数
M = 10;            % 记忆池容量
```

### 调优策略
- **高NMSC**: 增大lambda_x或减小学习率
- **低FDR**: 增大字典原子数或调整稀疏度
- **记忆效果差**: 调整记忆池大小M
- **数值不稳定**: 增大正则化参数

## 🎉 总结

DMCDL分析系统提供了：

1. **完整的演化分析**: 字典、权重、记忆池全方位追踪
2. **深入的性能评估**: 监测性能和方法比较
3. **自动化流程**: 一键运行完整分析
4. **丰富的可视化**: 多角度图表展示
5. **详细的文档**: 完整的使用和解读指南

这为DMCDL方法的研究、开发和应用提供了强有力的分析工具！
