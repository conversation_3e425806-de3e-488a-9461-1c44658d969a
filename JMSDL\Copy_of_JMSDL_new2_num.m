% ===============================================================
%  JMSDL方法 - 基于最新compute_dictionary_JMSDL.m的修改版本
%  自适应多工况相似保持字典学习  (Huang 2023, Alg.1)
%  --------------------------------------------------------------
%  • 第 1 工况:   K‑SVD 得到初始字典 D1
%  • 后续工况2‑5: JMSDL 交替更新字典并保持相邻两阶段相似
% ===============================================================
clc; clear; rng(42);

%% ---------- 0. 参数设置 ----------
dictSize    = 20;   % 字典原子数  K
sparsityOMP = 2;    % OMP 稀疏度  L
iterKSVD    = 50;   % K‑SVD 迭代轮次
iterJMSDL   = 50;   % 每个新增工况迭代轮次
lambda1     = 0.5;  % 相似保持系数 λ1
epsilon     = 1e-6; % 数值稳定常数

fprintf('========== JMSDL方法参数设置 ==========\n');
fprintf('字典原子数: %d\n', dictSize);
fprintf('OMP稀疏度: %d\n', sparsityOMP);
fprintf('K-SVD迭代次数: %d\n', iterKSVD);
fprintf('JMSDL迭代次数: %d\n', iterJMSDL);
fprintf('相似保持系数λ1: %.2f\n', lambda1);

%% ---------- 1. 读取 5 工况训练数据 ----------
fprintf('\n[1] 读取并归一化训练数据...\n');

try
    modes = cell(1,5);
    for k = 1:5
        fname = sprintf('mode%d_train.mat',k);
        tmp   = load(fname);
        modes{k} = normalize(tmp.train_data');   % (8×1000)
        fprintf('  ✓ 加载%s: %dx%d\n', fname, size(modes{k},1), size(modes{k},2));
    end

    % 便于后续标识
    train_mode1 = modes{1};
    train_mode2 = modes{2};
    train_mode3 = modes{3};
    train_mode4 = modes{4};
    train_mode5 = modes{5};

    fprintf('✓ 5个工况数据均已加载并归一化\n');

catch ME
    error('无法加载训练数据: %s\n请确保mode1_train.mat到mode5_train.mat文件在当前目录中。', ME.message);
end
train_mode5 = normalize(train_mode5);

%% ---------- 2. 在工况 1 上运行 K‑SVD ----------
fprintf('\n[2] K‑SVD 训练初始字典 D1...\n');

% 随机选列初始化
initIdx = randperm(size(train_mode1,2), dictSize);
D_init  = train_mode1(:,initIdx);

% K-SVD配置参数
opts.K               = dictSize;
opts.L               = sparsityOMP;
opts.numIteration    = iterKSVD;
opts.InitialDictionary = D_init;

% 运行K-SVD
[D_o, outK] = KSVD(train_mode1, opts, epsilon);
fprintf('✓ K‑SVD 完成, 最终重构误差 %.4e\n', outK.final_error);

% 初始化字典历史记录
Dictionary_history = cell(5,1);
Dictionary_history{1} = D_o;   % 保存第一工况字典



%% ---------- 3. 逐工况运行 JMSDL ----------
fprintf('\n[3] 逐工况运行 JMSDL 增量学习...\n');

D_n = D_o;  % 从第一工况字典开始

for n = 2:5
    fprintf('\n--- 工况 %d ---\n', n);

    % 当前工况数据
    X_n = modes{n};  % (8×1000)

    % 调用最新的compute_dictionary_JMSDL函数
    [D_n, ~] = compute_dictionary_JMSDL(X_n, D_o, D_n, lambda1, sparsityOMP, iterJMSDL, epsilon);

    % 保存字典
    Dictionary_history{n} = D_n;

    fprintf('✓ 工况 %d 字典更新完成\n', n);
end

%% ---------- 4. 保存结果 ----------
fprintf('\n[4] 保存JMSDL结果...\n');

% 保存最终字典
save('D_final_JMSDL.mat', 'D_n');
fprintf('✓ 最终字典已保存到: D_final_JMSDL.mat\n');

% 保存字典演化历史
save('JMSDL_Dictionary_history.mat', 'Dictionary_history');
fprintf('✓ 字典演化历史已保存到: JMSDL_Dictionary_history.mat\n');

fprintf('\n========== JMSDL方法完成 ==========\n');


function [D, output] = KSVD(data, opts, epsilon)
% KSVD  简易 K-SVD 算法实现
%   [D, output] = KSVD(data, opts, epsilon)
%   data:    D×N 训练矩阵
%   opts.K:  字典原子数
%   opts.L:  OMP 稀疏度
%   opts.numIteration: 最大迭代次数
%   opts.InitialDictionary: 初始 D×K 字典
%   epsilon: 数值稳定性参数
%
%   output.final_error: 最终迭代的重构误差

    if nargin < 3
        epsilon = 1e-6;
    end

    % 取出参数
    [Ddim, N] = size(data);
    K         = opts.K;
    L         = opts.L;
    maxIter   = opts.numIteration;
    D         = opts.InitialDictionary;

    % 主循环
    for iter = 1:maxIter
        % —— 1. 稀疏编码（OMP） —— 
        W = zeros(K, N);
        for i = 1:N
            x = data(:,i);
            W(:,i) = omp(D, x, L);
        end

        % —— 2. 字典更新 —— 
        for k = 1:K
            idx = find(W(k,:)~=0);             % 使用到第 k 个原子的样本索引
            if isempty(idx)
                continue;
            end
            % 计算误差矩阵：去掉第 k 原子的贡献
            E = data(:,idx) - D*W(:,idx) + D(:,k)*W(k,idx);
            % 对 E 做 SVD
            [U,S,V] = svd(E, 'econ');
            D(:,k)  = U(:,1);
            W(k,idx)= S(1,1) * V(:,1)';
        end

        % —— 记录重构误差 —— 
        rec = D*W;
        err = norm(data - rec, 'fro')^2;
        output.error(iter) = err;

        % （可选）收敛判断
        if iter>1 && abs(output.error(iter)-output.error(iter-1)) < 1e-6
            break;
        end
    end

    output.final_error = output.error(iter);
    % 归一化字典列
    for k = 1:K
        D(:,k) = D(:,k) / norm(D(:,k));
    end
end

function w = omp(D, x, L)
% OMP  简易正交匹配追踪
%   w = omp(D, x, L)
%   D: D×K 字典, x: D×1 样本, L: 稀疏度
    K = size(D,2);
    r = x;                   % 初始残差
    idx = false(1,K);        % 已选原子索引
    w = zeros(K,1);
    for j = 1:L
        proj = D' * r;
        [~, kbest] = max(abs(proj));
        idx(kbest) = true;
        % 在已选原子子空间上做最小二乘
        Dsub = D(:,idx);
        a = Dsub \ x;
        r = x - Dsub * a;
        if norm(r) < 1e-6
            break;
        end
    end
    w(idx) = a;
end