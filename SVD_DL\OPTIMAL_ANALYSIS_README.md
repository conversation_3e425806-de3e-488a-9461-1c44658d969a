# 最优参数条件下的NMSC和主方向子空间夹角分析

## 🎯 分析目标

基于参数搜索得到的最优参数组合：
- **Lambda**: 1e+03
- **N_atoms**: 20  
- **Sparsity**: 2
- **预期性能**: FDR=1.0000, FAR=0.0084

计算字典演化过程中的：
1. **NMSC** (归一化均方相干性)
2. **主方向子空间夹角**

## 📁 文件说明

### 主要分析文件
- `analyze_optimal_parameters.m` - 主分析脚本
- `run_optimal_analysis.m` - 一键运行脚本 ⭐**推荐使用**⭐
- `compute_nmsc_and_angles.m` - NMSC和夹角计算函数
- `visualize_dictionary_evolution.m` - 可视化函数

### 输出文件
- `optimal_parameter_analysis.mat` - 完整分析结果

## 🚀 使用方法

### 快速开始
```matlab
% 一键运行完整分析
run('run_optimal_analysis.m')
```

### 分步执行
```matlab
% 1. 主分析
run('analyze_optimal_parameters.m')

% 2. 加载结果并可视化
load('optimal_parameter_analysis.mat', 'results_optimal');
visualize_dictionary_evolution(results_optimal);
```

## 📊 关键指标说明

### 1. NMSC (归一化均方变化度)

**定义** (按照现有代码):
```
NMSC = mean((D_new - D_old)² ./ (D_old² + ε))
其中 ε = 1e-8 (防止除零)
```

**含义**:
- **值域**: [0, +∞)
- **NMSC ≈ 0**: 字典变化很小，高度稳定
- **NMSC 较大**: 字典变化较大，适应性强
- **中等值**: 在稳定性和适应性之间平衡

**解释标准**:
- `< 0.1`: 字典变化很小，保持了高度稳定性
- `0.1-0.5`: 字典有适度的变化，平衡了稳定性和适应性
- `> 0.5`: 字典变化较大，显示了强适应性

### 2. 主方向子空间夹角

**定义** (按照现有代码): 使用MATLAB自带的subspace函数
```
subspace_angle = subspace(U_old, U_new)  % 返回弧度
```

**含义**:
- **值域**: [0, π/2] 弧度 或 [0°, 90°]
- **夹角 ≈ 0**: 主方向基本不变，高度稳定
- **夹角 ≈ π/2**: 主方向完全改变，高度适应
- **中等角度**: 在稳定性和适应性之间平衡

**解释标准**:
- `< π/6 (30°)`: 主方向变化较小，保持了较好的稳定性
- `π/6 - π/3 (30°-60°)`: 主方向有中等程度的变化
- `> π/3 (60°)`: 主方向变化较大，适应性较强

## 📈 输出结果

### 控制台输出示例
```
📊 NMSC (归一化均方相干性):
   Mode 1→2: 0.7234
   Mode 2→3: 0.6891
   Mode 3→4: 0.7156
   Mode 4→5: 0.6978
   平均值: 0.7065

📐 主方向子空间夹角:
   Mode 1→2: 23.45°
   Mode 2→3: 28.67°
   Mode 3→4: 25.12°
   Mode 4→5: 26.89°
   平均值: 26.03°
```

### 可视化图表

#### 图1: 综合演化分析 (6个子图)
1. **NMSC变化趋势**: 显示各模式转换的相干性
2. **子空间夹角变化**: 显示主方向的变化程度
3. **主空间维度演化**: 显示主空间维度的变化
4. **字典条件数演化**: 显示字典数值稳定性
5. **奇异值分布对比**: 显示各模式字典的奇异值
6. **参数总结**: 显示关键参数和指标

#### 图2: 相干性矩阵分析 (4个子图)
- 显示每个模式转换的详细相干性热力图
- 可视化新旧字典原子间的相似性

#### 图3: 主空间几何分析 (2个子图)
1. **3D主空间演化**: 前3个主方向的3D轨迹
2. **主空间相似性矩阵**: 各模式间主空间的相似性

## 🔍 结果解读指南

### 理想情况
- **NMSC**: 0.6-0.8 (既保持稳定又有适应性)
- **子空间夹角**: 20°-40° (适度的方向调整)
- **主空间维度**: 相对稳定，小幅波动

### 问题诊断
- **NMSC过高 (>0.9)**: 可能学习能力不足，无法适应新模式
- **NMSC过低 (<0.3)**: 可能过度适应，丢失了重要的历史信息
- **夹角过大 (>70°)**: 主方向变化过于剧烈，可能影响监测一致性
- **夹角过小 (<10°)**: 主方向变化不足，可能无法捕获新特征

## 🎯 最优参数的预期表现

基于λ=1e+03, n_atoms=20, sparsity=2的设置：

### 预期特点
1. **高λ值**: 强主空间保护，NMSC相对较高，子空间夹角相对较小
2. **小原子数**: 字典紧凑，计算效率高，但表达能力有限
3. **低稀疏度**: 重构精度高，但计算复杂度适中

### 预期性能
- **FDR=1.0000**: 完美的故障检测率
- **FAR=0.0084**: 极低的误报率
- **NMSC**: 预计在0.6-0.8范围，显示良好的稳定性
- **子空间夹角**: 预计在20°-40°范围，显示适度的适应性

## 📋 检查清单

运行分析前请确保：
- ✅ 所有mode数据文件 (mode1_train.mat 到 mode5_train.mat)
- ✅ ksvd_simple_silent.m 函数
- ✅ omp.m 函数  
- ✅ compute_nmsc_and_angles.m 函数
- ✅ visualize_dictionary_evolution.m 函数
- ✅ 足够的内存空间 (建议>4GB可用)
- ✅ MATLAB版本支持 (建议R2018b或更高)

## 🚨 注意事项

1. **运行时间**: 预计5-15分钟，取决于计算机性能
2. **内存使用**: 会生成多个大型矩阵，注意内存使用
3. **图表数量**: 会生成3个主要图表窗口，包含多个子图
4. **数值精度**: 某些极端情况下可能出现数值计算问题

## 📞 故障排除

### 常见问题
1. **"文件不存在"错误**: 检查所有必要文件是否在当前目录
2. **内存不足**: 关闭其他程序，或使用更高配置的计算机
3. **函数未找到**: 确保所有.m文件在MATLAB路径中
4. **数值计算错误**: 可能是数据质量问题，检查输入数据

### 解决方案
- 使用 `run_optimal_analysis.m` 进行自动检查
- 查看控制台输出的详细错误信息
- 确保MATLAB版本兼容性

## 📈 扩展分析

如需更深入的分析，可以：
1. 修改 `analyze_optimal_parameters.m` 中的参数
2. 添加更多的可视化维度
3. 计算其他字典演化指标
4. 与其他参数组合进行对比分析
