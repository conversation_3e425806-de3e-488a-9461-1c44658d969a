function [NMSC, subspace_angle, change_stats] = compute_nmsc_and_angles(D_old, D_new, U_old, U_new)
%% 计算NMSC和主方向子空间夹角的详细函数
% 根据DL_with_close_solution.m中的定义
% 输入:
%   D_old: 旧字典 [m x k]
%   D_new: 新字典 [m x k]
%   U_old: 旧主空间 [m x r1]
%   U_new: 新主空间 [m x r2]
% 输出:
%   NMSC: 归一化均方变化度 (按照现有代码定义)
%   subspace_angle: 主方向子空间夹角 (弧度)
%   change_stats: 字典变化统计信息

%% 1. 计算NMSC (归一化均方变化度)
% 按照现有代码的定义: mean((D_new - D_old)^2 ./ (D_old^2 + epsilon))
epsilon = 1e-8;
num = (D_new - D_old).^2;
den = D_old.^2 + epsilon;
NMSC = mean(num(:) ./ den(:));

% 字典变化统计
change_stats = struct();
change_stats.delta_D = D_new - D_old;
change_stats.abs_delta_D = abs(D_new - D_old);
change_stats.relative_change = num ./ den;
change_stats.max_change = max(abs(change_stats.delta_D(:)));
change_stats.mean_abs_change = mean(abs(change_stats.delta_D(:)));
change_stats.std_change = std(change_stats.delta_D(:));

%% 2. 计算主方向子空间夹角
% 使用MATLAB自带的subspace函数，按照现有代码定义

% 确保输入是列向量且已归一化
if size(U_old, 1) ~= size(U_new, 1)
    error('两个子空间必须在同一个环境空间中');
end

% 处理维度不匹配的情况
min_dim = min(size(U_old, 2), size(U_new, 2));

if min_dim == 0
    subspace_angle = NaN;
    return;
end

% 截取到相同维度进行比较，按照现有代码的方式
U_old_trunc = U_old(:, 1:min_dim);
U_new_trunc = U_new(:, 1:min_dim);

% 使用MATLAB自带的subspace函数计算子空间夹角
try
    subspace_angle = subspace(U_old_trunc, U_new_trunc);  % 返回弧度
catch ME
    warning('MATLAB:subspace', '计算子空间夹角时出错: %s', ME.message);
    subspace_angle = NaN;
end

end
