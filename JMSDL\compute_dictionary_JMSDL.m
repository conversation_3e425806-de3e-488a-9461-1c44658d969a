% ===============================================================
%  compute_dictionary_JMSDL.m
%  自适应多工况相似保持字典学习  (Huang 2023, Alg.1)
%  --------------------------------------------------------------
%  • 第 1 工况:   K‑SVD 得到初始字典 D1
%  • 后续工况2‑5: JMSDL 交替更新字典并保持相邻两阶段相似
% ===============================================================
clc; clear; rng(42);

%% ---------- 0. 参数 ----------
dictSize    = 20;   % 字典原子数  K
sparsityOMP = 2;    % OMP 稀疏度  L
iterKSVD    = 50;   % K‑SVD 迭代轮次
iterJMSDL   = 50;   % 每个新增工况迭代轮次
lambda1     = 0.5;  % 相似保持系数 λ1
epsilon     = 1e-6; % 数值稳定常数

%% ---------- 1. 读取 5 工况训练数据 ----------
fprintf('[1] 读取并归一化训练数据...\n');
modes = cell(1,5);
for k = 1:5
    fname = sprintf('mode%d_train.mat',k);
    tmp   = load(fname);
    modes{k} = normalize(tmp.train_data');   % (8×1000)
end
train_mode1 = modes{1};   % 便于后续标识
fprintf('✓ 5 个工况数据均已加载, 每个大小: %dx%d\n', ...
        size(train_mode1,1), size(train_mode1,2));

%% ---------- 2. 在工况 1 上运行 K‑SVD ----------
fprintf('[2] K‑SVD 训练初始字典 D1...\n');
% 随机选列初始化
initIdx = randperm(size(train_mode1,2), dictSize);
D_init  = train_mode1(:,initIdx);

opts.K               = dictSize;
opts.L               = sparsityOMP;
opts.numIteration    = iterKSVD;
opts.InitialDictionary = D_init;

[D_o, outK] = KSVD(train_mode1, opts, epsilon);
fprintf('✓ K‑SVD 完成, 最终重构误差 %.4e\n', outK.final_error);

Dictionary_history = cell(5,1);
Dictionary_history{1} = D_o;   % 保存第一工况字典

%% ---------- 3. JMSDL 逐工况更新 ----------
D_n = D_o;   % 当前字典
for mi = 2:5
    fprintf('\n[3] JMSDL: 使用工况 %d 数据更新字典...\n', mi);
    X_n = modes{mi};       % 当前工况样本 (D×N)
    [Ddim, ~] = size(D_n);
    Nn        = size(X_n,2);

    % ---- 初始化稀疏系数 W_n (OMP) ----
    W_n = zeros(dictSize, Nn);
    for i = 1:Nn
        W_n(:,i) = omp(D_n, X_n(:,i), sparsityOMP);
    end

    % ---- 交替迭代更新 ----
    for it = 1:iterJMSDL
        % (1) 计算 B, F
        B = W_n * W_n';                       % (K×K)
        F = X_n * W_n' + (lambda1/2) * D_o;   % (D×K)

        % (2) 特征分解 B = M V M'
        [M, V] = eig((B+B')/2);               % 保对称
        sigma  = diag(V);                     % 取对角

        % (3) Q = D_n M,   P = F M
        Q = D_n * M;           % (D×K)
        P = F   * M;

        % (4) Q_ij = P_ij / σ_j
        for j = 1:dictSize
            denom = sigma(j);
            if abs(denom) < epsilon, denom = denom + epsilon; end
            Q(:,j) = P(:,j) / denom;
        end

        % (5) 恢复字典 D_n
        D_n = Q * M';

        % (6) 列归一化
        for k = 1:dictSize
            D_n(:,k) = D_n(:,k) / max(norm(D_n(:,k)), epsilon);
        end

        % (7) 再次 OMP 更新 W_n
        for i = 1:Nn
            W_n(:,i) = omp(D_n, X_n(:,i), sparsityOMP);
        end

        % 可视化重构误差
        recErr = norm(X_n - D_n*W_n, 'fro')^2;
        fprintf('  工况 %d, 迭代 %2d / %2d, 误差 = %.4e\n', mi, it, iterJMSDL, recErr);
    end

    % ---- 保存并为下一工况准备 ----
    Dictionary_history{mi} = D_n;
    D_o = D_n;   % ***关键: 更新旧字典，用于下一工况相似性约束***
end

fprintf('\n[✓] 所有工况更新完成!\n');

%% ---------- 4. 保存结果 ----------
save('JMSDL_final_dictionary.mat', 'D_n');
save('JMSDL_dictionary_history.mat', 'Dictionary_history');
fprintf('结果已保存: JMSDL_final_dictionary.mat, JMSDL_dictionary_history.mat\n');

% -------------------------------------------------------------------------
%                            子函数
% -------------------------------------------------------------------------
function [D, output] = KSVD(data, opts, epsl)
% ---- 简易 K‑SVD，参考 Aharon 2006 ----
[Ddim, N] = size(data);
K        = opts.K; L = opts.L; maxIter = opts.numIteration;
D        = opts.InitialDictionary ./ vecnorm(opts.InitialDictionary);

for it = 1:maxIter
    % (a) 稀疏编码 (OMP)
    W = zeros(K,N);
    for i = 1:N
        W(:,i) = omp(D, data(:,i), L);
    end

    % (b) 字典更新 (列级 SVD)
    for k = 1:K
        idx = find(W(k,:) ~= 0);
        if isempty(idx), continue; end
        E = data(:,idx) - D*W(:,idx) + D(:,k) * W(k,idx);
        [U,S,V] = svd(E,'econ');
        D(:,k)  = U(:,1);
        W(k,idx)= S(1,1) * V(:,1)';
    end

    % (c) 误差
    output.err(it) = norm(data - D*W, 'fro')^2;
    if it>1 && abs(output.err(it)-output.err(it-1)) < 1e-6, break; end
end
output.final_error = output.err(it);

% 正则归一化
for k = 1:K
    D(:,k) = D(:,k) / max(norm(D(:,k)), epsl);
end
end

% -------------------------------------------------------------------------
function w = omp(D, x, L)
% ---- 正交匹配追踪 (OMP) ----
K   = size(D,2);
idx = false(1,K); r = x; w = zeros(K,1);

for j = 1:L
    [~,kbest] = max(abs(D' * r));
    idx(kbest)= true;
    w(idx)    = D(:,idx) \ x;        % 最小二乘
    r         = x - D(:,idx) * w(idx);
    if norm(r) < 1e-6, break; end
end
end
