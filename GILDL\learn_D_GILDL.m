function [Dictionary_history, NMSC, max_angle_rad, final_dict] = learn_D_GILDL(Y_input, varargin)
%% GI-LDL 字典学习函数 (从learn_D.m转换而来)
% 输入:
%   Y_input - 输入数据 [特征维度 x 样本数] (可选，如果不提供则使用默认数据文件)
%   varargin - 可选参数对
% 输出:
%   Dictionary_history - 每个模式的字典历史
%   NMSC - 归一化均方变化度
%   max_angle_rad - 主空间最大夹角 (弧度)
%   final_dict - 最终字典

%% -------------------------- 参数设置 -------------------------------
% 默认参数
all_modes      = 5;                        % 模式数量 (F1,F2,...,F3)
n_atoms        = 20;                       % 字典原子数
sparsity       = 2;                        % OMP 稀疏度
iter_KSVD      = 50;                       % Mode1 K-SVD 迭代次数
iter_gildl     = 30;                       % 持续学习交替迭代次数
lambdaProtect  = 1e-6;                     % 主空间保护 λ (可调)
eps_norm       = 1e-6;                     % 防零除
use_external_data = false;                 % 是否使用外部输入数据

% 解析可选参数
for i = 1:2:length(varargin)
    switch lower(varargin{i})
        case 'all_modes'
            all_modes = varargin{i+1};
        case 'n_atoms'
            n_atoms = varargin{i+1};
        case 'sparsity'
            sparsity = varargin{i+1};
        case 'iter_ksvd'
            iter_KSVD = varargin{i+1};
        case 'iter_gildl'
            iter_gildl = varargin{i+1};
        case 'lambdaprotect'
            lambdaProtect = varargin{i+1};
    end
end

% 如果提供了输入数据，则使用外部数据模式
if nargin >= 1 && ~isempty(Y_input)
    use_external_data = true;
    all_modes = 1;  % 外部数据模式只训练一个字典
end

Dictionary_history = cell(all_modes,1);    % 保存每个 mode 的字典
Ws = zeros(n_atoms);                       % 初始权重矩阵 W1 = 0

%% ============ Mode 1 : 经典 K-SVD 训练初始字典 ======================
fprintf('=== Mode-1: K-SVD 训练初始字典 ===\n');

if use_external_data
    % 使用外部输入数据
    Y1 = Y_input;  % [m × N]  (m=观测维)
    fprintf('使用外部输入数据，大小: %dx%d\n', size(Y1,1), size(Y1,2));
else
    % 使用数据文件
    if exist('mode1_train.mat', 'file')
        load('mode1_train.mat');
    elseif exist('../SVD_DL/mode1_train.mat', 'file')
        load('../SVD_DL/mode1_train.mat');
    else
        error('找不到mode1_train.mat文件');
    end
    Y1 = train_data';             % [m × N]  (m=观测维)
end

[m,~] = size(Y1);

% % 调整原子数以适应数据维度
% if n_atoms > m
%     n_atoms = m;
%     fprintf('调整原子数为 %d (适应数据维度)\n', n_atoms);
% end

% 随机初始化字典
D0 = randn(m, n_atoms);
D0 = D0 ./ vecnorm(D0);

% 检查是否有ksvd_simple函数
if exist('ksvd_simple', 'file')
    % K-SVD (含列级 SVD 更新)
    [Dict1, ~] = ksvd_simple(Y1, D0, sparsity, iter_KSVD);
else
    % 备用方法：简单的字典学习
    fprintf('警告: 未找到ksvd_simple函数，使用备用方法\n');
    Dict1 = simple_dict_learning(Y1, D0, sparsity, iter_KSVD);
end

Dictionary_history{1} = Dict1;

% -------- 计算 W1 ---------------------------------------------------
% 真实实现应在 K‑SVD 内累加 ω_i；这里用"单位贡献"近似
zeta = 1e-2;                          % 论文 ζ 参数
omega_1  = ones(n_atoms,1);           % 简洁做法：假设均等贡献
w_tilde1 = omega_1 ./ (vecnorm(Dict1-D0).^2 + zeta);
Ws = lambdaProtect * diag(w_tilde1);  % W1
fprintf('W1 已计算完成 (diag 最大值 %.2e)\n',max(diag(Ws)));

% 保存前一轮变量
D_prev = Dict1;

% 如果只有一个模式（外部数据），直接返回
if use_external_data
    final_dict = Dict1;
    NMSC = 0;  % 单模式无变化
    max_angle_rad = 0;  % 单模式无角度变化
    return;
end

%% ============ Mode 2…S : Lifelong Dictionary Learning ============
for s = 2:all_modes
    fprintf('=== Mode-%d: Lifelong 更新 ===\n',s);
    
    % -------- 读取新模式数据 --------
    train_file = sprintf('mode%d_train.mat', s);
    if ~exist(train_file, 'file')
        train_file = sprintf('../SVD_DL/mode%d_train.mat', s);
    end
    
    if exist(train_file, 'file')
        load(train_file);
        Y_new = train_data';       % 当前模式样本
    else
        fprintf('警告: 找不到 %s，跳过Mode %d\n', train_file, s);
        Dictionary_history{s} = D_prev;  % 使用前一个字典
        continue;
    end
    
    [m, N] = size(Y_new);
    
    % -------- 初始化 ----------
    D = D_prev;                             % 当前可变字典
    omega_s = zeros(n_atoms,1);             % ΔL 累加器
    
    for it = 1:iter_gildl
        % ==== 1. OMP 编码 ===========================================
        X = zeros(n_atoms, N);
        for j = 1:N
            if exist('omp', 'file')
                X(:,j) = omp(D, Y_new(:,j), sparsity);
            else
                % 备用方法：最小二乘 + 稀疏化
                x_temp = pinv(D) * Y_new(:,j);
                [~, idx] = sort(abs(x_temp), 'descend');
                x_sparse = zeros(size(x_temp));
                x_sparse(idx(1:min(sparsity, length(idx)))) = x_temp(idx(1:min(sparsity, length(idx))));
                X(:,j) = x_sparse;
            end
        end
        
        % ==== 2. 闭式整体更新 (式 24) ===============================
        D_old = D;                          % 保存旧字典用于 ΔL
        D = (D_prev*Ws + Y_new*X') / ...
             (X*X' + Ws + eps_norm*eye(n_atoms));
        D = D ./ vecnorm(D);                % 列归一化
        
        % ==== 3. 整体损失差 ΔL (简化版) =============================
        L_before = norm(Y_new - D_old*X,'fro')^2;
        L_after  = norm(Y_new - D    *X,'fro')^2;
        deltaL   = L_before - L_after;
        omega_s  = omega_s + deltaL;        % 均匀加到每原子
    end
    
    % -------- 4. 计算 w_tilde (公式 13) -----------------------------
    w_tilde = omega_s ./ (vecnorm(D - D_prev).^2 + eps_norm);
    
    % -------- 5. 权重递归 Ws = Ws + λ·diag(w_tilde) ---------------
    Ws = Ws + lambdaProtect * diag(w_tilde);
    
    % -------- 6. 保存并准备下轮 ------------------------------------
    Dictionary_history{s} = D;
    D_prev  = D;
end

%% ====================== NMSC 指标 ================================
D_first = Dictionary_history{1};
D_last  = Dictionary_history{end};
final_dict = D_last;

% 保存最终字典
save('Dlast_num.mat', 'D_last');

deltaD  = D_last - D_first;
NMSC = mean( (deltaD(:).^2) ./ (D_first(:).^2 + eps_norm) );

%% =================== 主空间最大夹角 ==============================
[U1,S1,~] = svd(D_first,'econ');
[U2,~,~]  = svd(D_last ,'econ');

% 取能量覆盖 ≥90% 的主空间维 r
energy_ratio = cumsum(diag(S1).^2)/sum(diag(S1).^2);
r = find(energy_ratio >= 0.9,1,'first');

if isempty(r)
    r = min([3, size(U1,2)]);  % 至少取3维
end

r = min([r, size(U1,2), size(U2,2)]);  % 确保不超过矩阵维度

if r > 0
    U1r = U1(:,1:r);  
    U2r = U2(:,1:r);
    s = svd(U1r' * U2r);
    max_angle_rad = max( acos( min(max(s,-1),1) ) );
else
    max_angle_rad = 0;
end

fprintf('主空间维数 r = %d\n', r);
fprintf('主空间最大夹角 = %.4f 弧度 (%.2f°)\n', ...
        max_angle_rad, rad2deg(max_angle_rad));

end

%% 备用字典学习函数
function Dict = simple_dict_learning(Y, D_init, sparsity, n_iter)
    % 简单的字典学习算法（当ksvd_simple不可用时）
    Dict = D_init;
    [m, n_atoms] = size(Dict);
    N = size(Y, 2);
    
    for iter = 1:n_iter
        % 稀疏编码
        X = zeros(n_atoms, N);
        for i = 1:N
            % 简单的贪婪选择
            residual = Y(:,i);
            for s = 1:sparsity
                [~, best_atom] = max(abs(Dict' * residual));
                X(best_atom, i) = Dict(:, best_atom)' * residual;
                residual = residual - X(best_atom, i) * Dict(:, best_atom);
            end
        end
        
        % 字典更新
        for k = 1:n_atoms
            if sum(X(k,:) ~= 0) > 0
                used_samples = find(X(k,:) ~= 0);
                E = Y(:, used_samples) - Dict * X(:, used_samples) + Dict(:,k) * X(k, used_samples);
                [U, S, V] = svd(E, 'econ');
                Dict(:,k) = U(:,1);
                X(k, used_samples) = S(1,1) * V(:,1)';
            end
        end
        
        % 归一化
        Dict = Dict ./ vecnorm(Dict);
    end
end
