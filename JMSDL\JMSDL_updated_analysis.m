%% JMSDL方法更新版本 - 基于最新compute_dictionary_JMSDL.m
% 使用最新的正确算法实现JMSDL字典学习和性能分析

clc; clear; close all;
rng(42);

fprintf('========== JMSDL方法更新版本分析 ==========\n');
fprintf('基于最新的compute_dictionary_JMSDL.m算法\n\n');

%% 1. 参数设置
dictSize    = 20;   % 字典原子数
sparsityOMP = 2;    % OMP稀疏度
iterKSVD    = 50;   % K-SVD迭代次数
iterJMSDL   = 50;   % JMSDL迭代次数
lambda1     = 0.5;  % 相似保持系数
epsilon     = 1e-6; % 数值稳定性参数

fprintf('参数设置:\n');
fprintf('  字典原子数: %d\n', dictSize);
fprintf('  OMP稀疏度: %d\n', sparsityOMP);
fprintf('  K-SVD迭代次数: %d\n', iterKSVD);
fprintf('  JMSDL迭代次数: %d\n', iterJMSDL);
fprintf('  相似保持系数λ1: %.2f\n', lambda1);

%% 2. 加载训练数据
fprintf('\n加载训练数据...\n');

try
    modes = cell(1,5);
    for k = 1:5
        fname = sprintf('mode%d_train.mat', k);
        tmp = load(fname);
        modes{k} = normalize(tmp.train_data');  % [8×1000]
        fprintf('  ✓ %s: %dx%d\n', fname, size(modes{k},1), size(modes{k},2));
    end
    fprintf('✓ 5个工况数据均已加载并归一化\n');
    
catch ME
    error('无法加载训练数据: %s\n请确保mode1_train.mat到mode5_train.mat文件在当前目录中。', ME.message);
end

%% 3. K-SVD训练初始字典
fprintf('\nK-SVD训练初始字典...\n');

% 随机初始化
initIdx = randperm(size(modes{1},2), dictSize);
D_init = modes{1}(:, initIdx);

% 归一化初始字典
for k = 1:dictSize
    D_init(:,k) = D_init(:,k) / norm(D_init(:,k));
end

% K-SVD配置
opts.K = dictSize;
opts.L = sparsityOMP;
opts.numIteration = iterKSVD;
opts.InitialDictionary = D_init;

% 运行K-SVD
[D_o, outK] = KSVD_updated(modes{1}, opts, epsilon);
fprintf('✓ K-SVD完成，最终重构误差: %.4e\n', outK.final_error);

%% 4. JMSDL增量学习
fprintf('\nJMSDL增量学习...\n');

Dictionary_history = cell(5,1);
Dictionary_history{1} = D_o;
D_n = D_o;

for n = 2:5
    fprintf('  工况 %d...', n);

    % 当前工况数据
    X_n = modes{n};
    [Ddim, ~] = size(D_n);
    Nn = size(X_n, 2);

    % 初始化稀疏系数 W_n (OMP)
    W_n = zeros(dictSize, Nn);
    for i = 1:Nn
        W_n(:,i) = omp(D_n, X_n(:,i), sparsityOMP);
    end

    % 交替迭代更新
    for it = 1:iterJMSDL
        % (1) 计算 B, F
        B = W_n * W_n';                       % (K×K)
        F = X_n * W_n' + (lambda1/2) * D_o;   % (D×K)

        % (2) 特征分解 B = M V M'
        [M, V] = eig((B+B')/2);               % 保对称
        sigma  = diag(V);                     % 取对角

        % (3) Q = D_n M,   P = F M
        Q = D_n * M;           % (D×K)
        P = F   * M;

        % (4) Q_ij = P_ij / σ_j
        for j = 1:dictSize
            denom = sigma(j);
            if abs(denom) < epsilon, denom = denom + epsilon; end
            Q(:,j) = P(:,j) / denom;
        end

        % (5) 恢复字典 D_n
        D_n = Q * M';

        % (6) 列归一化
        for k = 1:dictSize
            D_n(:,k) = D_n(:,k) / max(norm(D_n(:,k)), epsilon);
        end

        % (7) 再次 OMP 更新 W_n
        for i = 1:Nn
            W_n(:,i) = omp(D_n, X_n(:,i), sparsityOMP);
        end
    end

    % 保存字典
    Dictionary_history{n} = D_n;

    % 计算最终重构误差
    final_error = norm(X_n - D_n*W_n, 'fro')^2;
    fprintf(' 完成 (最终误差: %.4e)\n', final_error);

    % 更新旧字典，用于下一工况相似性约束
    D_o = D_n;
end

%% 5. 性能分析
fprintf('\n性能分析...\n');

% 计算NMSC
NMSC_history = zeros(4,1);
epsilon_nmsc = 1e-8;

for i = 2:5
    D_prev = Dictionary_history{i-1};
    D_curr = Dictionary_history{i};
    deltaD = D_curr - D_prev;
    NMSC_history(i-1) = mean((deltaD(:).^2) ./ (D_prev(:).^2 + epsilon_nmsc));
    fprintf('  NMSC (工况%d→%d): %.6f\n', i-1, i, NMSC_history(i-1));
end

% 计算主空间夹角
fprintf('\n主空间夹角分析...\n');
Subspace_angle_history = zeros(4,1);

for i = 2:5
    D_prev = Dictionary_history{i-1};
    D_curr = Dictionary_history{i};
    
    % SVD分解
    [U_prev, ~, ~] = svd(D_prev, 'econ');
    [U_curr, ~, ~] = svd(D_curr, 'econ');
    
    % 计算主空间维度（90%能量）
    try
        k_dim = min(5, min(size(U_prev,2), size(U_curr,2)));
        angle = subspace(U_prev(:,1:k_dim), U_curr(:,1:k_dim));
        Subspace_angle_history(i-1) = angle;
        fprintf('  子空间夹角 (工况%d→%d): %.4f弧度 (%.2f°)\n', ...
                i-1, i, angle, angle*180/pi);
    catch
        Subspace_angle_history(i-1) = NaN;
        fprintf('  子空间夹角 (工况%d→%d): 计算失败\n', i-1, i);
    end
end

%% 6. 可视化
fprintf('\n生成可视化...\n');

figure('Position', [100, 100, 1200, 800]);

% 字典演化热图
for i = 1:5
    subplot(2,3,i);
    imagesc(Dictionary_history{i});
    colorbar;
    title(sprintf('工况%d字典', i), 'FontSize', 12);
    xlabel('原子索引');
    ylabel('特征维度');
end

% NMSC趋势
subplot(2,3,6);
plot(2:5, NMSC_history, 'bo-', 'LineWidth', 2, 'MarkerSize', 8);
xlabel('目标工况');
ylabel('NMSC');
title('JMSDL: NMSC变化趋势', 'FontSize', 12);
grid on;
for i = 1:length(NMSC_history)
    text(i+1, NMSC_history(i), sprintf('%.4f', NMSC_history(i)), ...
         'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom');
end

sgtitle('JMSDL方法更新版本 - 字典演化分析', 'FontSize', 16);

% 保存图像
savefig('JMSDL_updated_analysis.fig');
fprintf('✓ 可视化图已保存到: JMSDL_updated_analysis.fig\n');

%% 7. 保存结果
fprintf('\n保存结果...\n');

% 保存最终字典
save('D_final_JMSDL_updated.mat', 'D_n');

% 保存完整结果
JMSDL_updated_results = struct();
JMSDL_updated_results.method = 'JMSDL_Updated';
JMSDL_updated_results.Dictionary_history = Dictionary_history;
JMSDL_updated_results.NMSC_history = NMSC_history;
JMSDL_updated_results.Subspace_angle_history = Subspace_angle_history;
JMSDL_updated_results.parameters = struct('dictSize', dictSize, 'sparsityOMP', sparsityOMP, ...
                                          'iterKSVD', iterKSVD, 'iterJMSDL', iterJMSDL, ...
                                          'lambda1', lambda1, 'epsilon', epsilon);

save('JMSDL_updated_results.mat', 'JMSDL_updated_results');

fprintf('✓ 最终字典已保存到: D_final_JMSDL_updated.mat\n');
fprintf('✓ 完整结果已保存到: JMSDL_updated_results.mat\n');

%% 8. 结果总结
fprintf('\n========== JMSDL更新版本结果总结 ==========\n');
fprintf('📊 基本信息:\n');
fprintf('   方法: JMSDL (更新版本)\n');
fprintf('   工况数: 5\n');
fprintf('   字典大小: %dx%d\n', size(Dictionary_history{1}));

fprintf('\n📈 字典演化性能:\n');
fprintf('   平均NMSC: %.6f\n', mean(NMSC_history));

valid_angles = Subspace_angle_history(~isnan(Subspace_angle_history));
if ~isempty(valid_angles)
    fprintf('   平均子空间夹角: %.4f弧度 (%.2f°)\n', mean(valid_angles), mean(valid_angles)*180/pi);
end

% 性能评估
avg_nmsc = mean(NMSC_history);
if avg_nmsc < 0.1
    fprintf('   字典稳定性: 优秀 (NMSC=%.6f)\n', avg_nmsc);
elseif avg_nmsc < 0.5
    fprintf('   字典稳定性: 良好 (NMSC=%.6f)\n', avg_nmsc);
else
    fprintf('   字典稳定性: 一般 (NMSC=%.6f)\n', avg_nmsc);
end

fprintf('\n🎉 JMSDL更新版本分析完成！\n');

%% 辅助函数

function [D, output] = KSVD_updated(data, opts, epsilon)
% 更新版本的K-SVD算法
    if nargin < 3
        epsilon = 1e-6;
    end
    
    [~, N] = size(data);
    K = opts.K;
    L = opts.L;
    maxIter = opts.numIteration;
    D = opts.InitialDictionary;
    
    for iter = 1:maxIter
        % 稀疏编码
        W = zeros(K, N);
        for i = 1:N
            W(:,i) = omp(D, data(:,i), L);
        end
        
        % 字典更新
        for k = 1:K
            idx = find(W(k,:) ~= 0);
            if isempty(idx)
                continue;
            end
            
            E_k = data(:,idx) - D*W(:,idx) + D(:,k)*W(k,idx);
            [U, S, V] = svd(E_k, 'econ');
            
            if size(U,2) > 0
                D(:,k) = U(:,1);
                W(k,idx) = S(1,1) * V(:,1)';
            end
        end
        
        % 归一化
        for k = 1:K
            if norm(D(:,k)) > epsilon
                D(:,k) = D(:,k) / norm(D(:,k));
            end
        end
    end
    
    % 计算最终误差
    W_final = zeros(K, N);
    for i = 1:N
        W_final(:,i) = omp(D, data(:,i), L);
    end
    
    output.final_error = norm(data - D*W_final, 'fro')^2;
end

function w = omp(D, x, L)
% ---- 正交匹配追踪 (OMP) ----
K   = size(D,2);
idx = false(1,K); r = x; w = zeros(K,1);

for j = 1:L
    [~,kbest] = max(abs(D' * r));
    idx(kbest)= true;
    w(idx)    = D(:,idx) \ x;        % 最小二乘
    r         = x - D(:,idx) * w(idx);
    if norm(r) < 1e-6, break; end
end
end
