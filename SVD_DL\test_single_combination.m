%% 单个参数组合测试 - 用于调试特定的参数组合
% 测试导致错误的参数组合: lambda=1.00e-04, n_atoms=30, sparsity=1

rng(42);

%% 设置参数
lambda = 1.00e-04;
n_atoms = 30;
sparsity = 1;

fprintf('测试参数组合: lambda=%.2e, n_atoms=%d, sparsity=%d\n', lambda, n_atoms, sparsity);

try
    %% ========== 1. 加载训练数据 ==========
    fprintf('1. 加载训练数据...\n');
    load('mode1_train.mat');  % 变量 train_data，1000x8
    Y = train_data';          % [8 x 1000]
    
    % 检查数据
    fprintf('   训练数据大小: %dx%d\n', size(Y,1), size(Y,2));
    fprintf('   数据范围: [%.4f, %.4f]\n', min(Y(:)), max(Y(:)));
    fprintf('   NaN数量: %d\n', sum(isnan(Y(:))));
    fprintf('   Inf数量: %d\n', sum(isinf(Y(:))));
    
    %% ========== 2. K-SVD训练 ==========
    fprintf('2. K-SVD训练...\n');
    n_iter = 10;  % 减少迭代次数用于快速测试
    D_init = randn(8, n_atoms);
    for k = 1:n_atoms
        D_init(:,k) = D_init(:,k) / norm(D_init(:,k));
    end
    
    fprintf('   初始字典大小: %dx%d\n', size(D_init,1), size(D_init,2));
    
    [Dictionary, ~] = ksvd_simple(Y, D_init, sparsity, n_iter);
    
    fprintf('   训练完成，字典大小: %dx%d\n', size(Dictionary,1), size(Dictionary,2));
    fprintf('   字典NaN数量: %d\n', sum(isnan(Dictionary(:))));
    fprintf('   字典Inf数量: %d\n', sum(isinf(Dictionary(:))));
    
    %% ========== 3. 简化的性能评估 ==========
    fprintf('3. 性能评估...\n');
    
    % 只使用mode1的数据进行测试
    load('mode1_test_normal.mat');
    load('mode1_test_fault.mat');
    Y_test = [test_normal_data; test_fault_data]';
    
    fprintf('   测试数据大小: %dx%d\n', size(Y_test,1), size(Y_test,2));
    
    % 计算重构误差
    R_test = zeros(1, size(Y_test,2));
    omp_errors = 0;
    
    for i = 1:size(Y_test,2)
        y = Y_test(:,i);
        try
            x = omp(Dictionary, y, sparsity);
            R_test(i) = norm(y - Dictionary*x, 2)^2;
        catch
            R_test(i) = norm(y, 2)^2;  % 如果OMP失败，使用原始信号能量
            omp_errors = omp_errors + 1;
        end
        
        if mod(i, 500) == 0
            fprintf('   处理了 %d/%d 个测试样本\n', i, size(Y_test,2));
        end
    end
    
    fprintf('   OMP错误次数: %d\n', omp_errors);
    fprintf('   R_test统计:\n');
    fprintf('     最小值: %.6f\n', min(R_test));
    fprintf('     最大值: %.6f\n', max(R_test));
    fprintf('     均值: %.6f\n', mean(R_test));
    fprintf('     NaN数量: %d\n', sum(isnan(R_test)));
    fprintf('     Inf数量: %d\n', sum(isinf(R_test)));
    
    % 清理异常值
    R_test_clean = R_test(~isnan(R_test) & ~isinf(R_test) & R_test >= 0);
    fprintf('   清理后的R_test数量: %d\n', length(R_test_clean));
    
    if length(R_test_clean) < 10
        error('有效的测试统计量太少');
    end
    
    %% ========== 4. 计算控制限 ==========
    fprintf('4. 计算控制限...\n');
    
    % 使用训练数据计算控制限
    Y_train = train_data';
    R_train = zeros(1, size(Y_train,2));
    
    for i = 1:size(Y_train,2)
        y = Y_train(:,i);
        try
            x = omp(Dictionary, y, sparsity);
            R_train(i) = norm(y - Dictionary*x, 2)^2;
        catch
            R_train(i) = norm(y, 2)^2;
        end
    end
    
    % 清理训练统计量
    R_train_clean = R_train(~isnan(R_train) & ~isinf(R_train) & R_train >= 0);
    fprintf('   训练统计量数量: %d\n', length(R_train_clean));
    
    if length(R_train_clean) < 10
        error('有效的训练统计量太少');
    end
    
    % 尝试不同的控制限计算方法
    try
        % 方法1: 核密度估计
        [f_R, xi_R] = ksdensity(R_train_clean, 'Function', 'cdf');
        idx_R = find(f_R >= 1 - 0.01, 1, 'first');
        R_limit_kde = xi_R(idx_R);
        fprintf('   KDE控制限: %.6f\n', R_limit_kde);
    catch ME
        fprintf('   KDE方法失败: %s\n', ME.message);
        R_limit_kde = NaN;
    end
    
    % 方法2: 分位数方法
    R_limit_quantile = quantile(R_train_clean, 0.99);
    fprintf('   分位数控制限: %.6f\n', R_limit_quantile);
    
    % 方法3: 均值+3倍标准差
    R_limit_3sigma = mean(R_train_clean) + 3 * std(R_train_clean);
    fprintf('   3σ控制限: %.6f\n', R_limit_3sigma);
    
    % 选择有效的控制限
    if ~isnan(R_limit_kde)
        R_limit = R_limit_kde;
        method_used = 'KDE';
    else
        R_limit = R_limit_quantile;
        method_used = '分位数';
    end
    
    fprintf('   使用的控制限: %.6f (%s方法)\n', R_limit, method_used);
    
    %% ========== 5. 计算FAR/FDR ==========
    fprintf('5. 计算FAR/FDR...\n');
    
    n_normal = 500;
    n_fault = 500;
    
    R_normal = R_test_clean(1:min(n_normal, length(R_test_clean)));
    R_fault = R_test_clean(n_normal+1:min(n_normal+n_fault, length(R_test_clean)));
    
    FAR = sum(R_normal > R_limit) / length(R_normal);
    FDR = sum(R_fault > R_limit) / length(R_fault);
    
    fprintf('   FAR: %.4f\n', FAR);
    fprintf('   FDR: %.4f\n', FDR);
    
    %% ========== 6. 可视化结果 ==========
    fprintf('6. 生成可视化...\n');
    
    figure('Position', [100, 100, 1200, 400]);
    
    subplot(1,3,1);
    histogram(R_train_clean, 50);
    xline(R_limit, '--r', 'LineWidth', 2);
    xlabel('R统计量');
    ylabel('频次');
    title('训练数据统计量分布');
    legend('训练数据', '控制限');
    
    subplot(1,3,2);
    histogram(R_normal, 30, 'FaceAlpha', 0.7); hold on;
    histogram(R_fault, 30, 'FaceAlpha', 0.7);
    xline(R_limit, '--r', 'LineWidth', 2);
    xlabel('R统计量');
    ylabel('频次');
    title('测试数据统计量分布');
    legend('正常', '故障', '控制限');
    
    subplot(1,3,3);
    plot(R_test_clean, 'b', 'LineWidth', 1); hold on;
    yline(R_limit, '--r', 'LineWidth', 2);
    xlabel('样本编号');
    ylabel('R统计量');
    title('测试统计量序列');
    legend('R统计量', '控制限');
    
    fprintf('测试完成！\n');
    fprintf('最终结果: lambda=%.2e, n_atoms=%d, sparsity=%d, FAR=%.4f, FDR=%.4f\n', ...
            lambda, n_atoms, sparsity, FAR, FDR);
    
catch ME
    fprintf('测试失败: %s\n', ME.message);
    fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
end
