function visualize_dictionary_evolution(results_optimal)
%% 可视化字典演化过程中的NMSC和子空间夹角
% 输入: results_optimal - 包含分析结果的结构体

%% 提取数据
NMSC_history = results_optimal.NMSC_history;
Subspace_angle_history = results_optimal.Subspace_angle_history;
Dictionary_history = results_optimal.Dictionary_history;
U_locked_history = results_optimal.U_locked_history;
lambda = results_optimal.lambda;
n_atoms = results_optimal.n_atoms;
sparsity = results_optimal.sparsity;

%% 图1: 综合演化分析
figure('Position', [100, 100, 1400, 1000]);

% 子图1: NMSC变化趋势
subplot(2,3,1);
plot(2:5, NMSC_history, 'bo-', 'LineWidth', 3, 'MarkerSize', 10, 'MarkerFaceColor', 'b');
xlabel('目标模式', 'FontSize', 12);
ylabel('NMSC', 'FontSize', 12);
title('归一化均方相干性变化', 'FontSize', 14);
grid on;
set(gca, 'FontSize', 11);
% 添加数值标签
for i = 1:length(NMSC_history)
    text(i+1, NMSC_history(i)+0.02, sprintf('%.3f', NMSC_history(i)), ...
         'HorizontalAlignment', 'center', 'FontSize', 10);
end

% 子图2: 子空间夹角变化趋势
subplot(2,3,2);
valid_idx = ~isnan(Subspace_angle_history);
valid_modes = find(valid_idx) + 1;
valid_angles = Subspace_angle_history(valid_idx);
plot(valid_modes, valid_angles, 'ro-', 'LineWidth', 3, 'MarkerSize', 10, 'MarkerFaceColor', 'r');
xlabel('目标模式', 'FontSize', 12);
ylabel('子空间夹角 (度)', 'FontSize', 12);
title('主方向子空间夹角变化', 'FontSize', 14);
grid on;
ylim([0, 90]);
set(gca, 'FontSize', 11);
% 添加数值标签
for i = 1:length(valid_angles)
    text(valid_modes(i), valid_angles(i)+2, sprintf('%.4f°', valid_angles(i)), ...
         'HorizontalAlignment', 'center', 'FontSize', 10);
end

% 子图3: 主空间维度变化
subplot(2,3,3);
subspace_dims = cellfun(@(x) size(x,2), U_locked_history);
plot(1:5, subspace_dims, 'go-', 'LineWidth', 3, 'MarkerSize', 10, 'MarkerFaceColor', 'g');
xlabel('模式', 'FontSize', 12);
ylabel('主空间维度', 'FontSize', 12);
title('主空间维度演化', 'FontSize', 14);
grid on;
set(gca, 'FontSize', 11);
% 添加数值标签
for i = 1:length(subspace_dims)
    text(i, subspace_dims(i)+0.1, sprintf('%d', subspace_dims(i)), ...
         'HorizontalAlignment', 'center', 'FontSize', 10);
end

% 子图4: 字典条件数变化
subplot(2,3,4);
condition_numbers = zeros(5,1);
for i = 1:5
    D = Dictionary_history{i};
    condition_numbers(i) = cond(D);
end
semilogy(1:5, condition_numbers, 'mo-', 'LineWidth', 3, 'MarkerSize', 10, 'MarkerFaceColor', 'm');
xlabel('模式', 'FontSize', 12);
ylabel('条件数 (对数尺度)', 'FontSize', 12);
title('字典条件数演化', 'FontSize', 14);
grid on;
set(gca, 'FontSize', 11);

% 子图5: 奇异值分布对比
subplot(2,3,5);
colors = ['b', 'r', 'g', 'm', 'c'];
for i = 1:5
    D = Dictionary_history{i};
    [~, S, ~] = svd(D, 'econ');
    singular_vals = diag(S);
    plot(singular_vals, colors(i), 'LineWidth', 2, 'DisplayName', sprintf('Mode %d', i));
    hold on;
end
xlabel('原子索引', 'FontSize', 12);
ylabel('奇异值', 'FontSize', 12);
title('字典奇异值分布对比', 'FontSize', 14);
legend('Location', 'best');
grid on;
set(gca, 'FontSize', 11);

% 子图6: 参数总结和关键指标
subplot(2,3,6);
axis off;
text(0.1, 0.9, '最优参数设置:', 'FontSize', 14, 'FontWeight', 'bold');
text(0.1, 0.8, sprintf('λ = %.0e', lambda), 'FontSize', 12);
text(0.1, 0.7, sprintf('原子数 = %d', n_atoms), 'FontSize', 12);
text(0.1, 0.6, sprintf('稀疏度 = %d', sparsity), 'FontSize', 12);

text(0.1, 0.45, '关键指标:', 'FontSize', 14, 'FontWeight', 'bold');
text(0.1, 0.35, sprintf('平均NMSC = %.4f', mean(NMSC_history)), 'FontSize', 12);
if ~isempty(valid_angles)
    text(0.1, 0.25, sprintf('平均夹角 = %.2f°', mean(valid_angles)), 'FontSize', 12);
end
text(0.1, 0.15, sprintf('主空间维度: %d-%d', min(subspace_dims), max(subspace_dims)), 'FontSize', 12);
text(0.1, 0.05, sprintf('预期性能: FDR=1.0, FAR=0.0084'), 'FontSize', 12);

sgtitle(sprintf('SVD-DL最优参数下的字典演化综合分析 (λ=%.0e, n=%d, s=%d)', lambda, n_atoms, sparsity), 'FontSize', 16);

%% 图2: 字典变化热图分析 (按照现有代码定义)
figure('Position', [150, 150, 1200, 800]);

% 计算第一个字典和最后一个字典的变化
D1 = Dictionary_history{1};
Dlast = Dictionary_history{end};
deltaD = Dlast - D1;

% 子图1: 字典差分热图
subplot(2,3,1);
imagesc(deltaD);
colorbar;
xlabel('字典原子编号', 'FontSize', 12);
ylabel('观测量编号', 'FontSize', 12);
title('最终字典与mode1字典差分热图', 'FontSize', 14);
set(gca,'FontSize',11);

% 子图2: 字典绝对变化热图
subplot(2,3,2);
imagesc(abs(deltaD));
colorbar;
xlabel('字典原子编号', 'FontSize', 12);
ylabel('观测量编号', 'FontSize', 12);
title('字典原子绝对变化热图', 'FontSize', 14);
set(gca,'FontSize',11);

% 子图3: 相对变化热图
subplot(2,3,3);
epsilon = 1e-8;
relative_change = (deltaD).^2 ./ (D1.^2 + epsilon);
imagesc(relative_change);
colorbar;
xlabel('字典原子编号', 'FontSize', 12);
ylabel('观测量编号', 'FontSize', 12);
title('字典相对变化热图', 'FontSize', 14);
set(gca,'FontSize',11);

% 子图4-6: 各模式转换的变化热图
for mode_transition = 1:3
    subplot(2,3,mode_transition+3);

    D_old = Dictionary_history{mode_transition};
    D_new = Dictionary_history{mode_transition + 1};
    delta_mode = D_new - D_old;

    imagesc(abs(delta_mode));
    colorbar;
    xlabel('字典原子编号', 'FontSize', 12);
    ylabel('观测量编号', 'FontSize', 12);
    title(sprintf('Mode %d→%d 变化热图', mode_transition, mode_transition+1), 'FontSize', 14);
    set(gca, 'FontSize', 11);

    % 添加NMSC值
    text(size(delta_mode,2)*0.02, size(delta_mode,1)*0.95, ...
         sprintf('NMSC=%.3f', NMSC_history(mode_transition)), ...
         'Color', 'white', 'FontSize', 12, 'FontWeight', 'bold');
end

sgtitle('字典变化热图分析 (按照现有代码定义)', 'FontSize', 16);

%% 图3: 主空间演化的3D可视化
figure('Position', [200, 200, 1000, 600]);

% 如果主空间维度>=3，可以进行3D可视化
subplot(1,2,1);
if size(U_locked_history{1}, 2) >= 3
    colors = ['b', 'r', 'g', 'm', 'c'];
    for i = 1:5
        U = U_locked_history{i};
        if size(U, 2) >= 3
            % 取前3个主方向进行3D可视化
            scatter3(U(1,1:3), U(2,1:3), U(3,1:3), 100, colors(i), 'filled', ...
                    'DisplayName', sprintf('Mode %d', i));
            hold on;
        end
    end
    xlabel('第1主方向', 'FontSize', 12);
    ylabel('第2主方向', 'FontSize', 12);
    zlabel('第3主方向', 'FontSize', 12);
    title('主空间前3个方向的3D演化', 'FontSize', 14);
    legend('Location', 'best');
    grid on;
else
    text(0.5, 0.5, '主空间维度<3，无法进行3D可视化', 'HorizontalAlignment', 'center');
    title('主空间3D可视化');
end

% 主空间相似性矩阵
subplot(1,2,2);
similarity_matrix = zeros(5, 5);
for i = 1:5
    for j = 1:5
        U_i = U_locked_history{i};
        U_j = U_locked_history{j};
        min_dim = min(size(U_i,2), size(U_j,2));
        if min_dim > 0
            % 计算子空间相似性 (使用Frobenius内积)
            similarity_matrix(i,j) = norm(U_i(:,1:min_dim)' * U_j(:,1:min_dim), 'fro') / min_dim;
        end
    end
end

imagesc(similarity_matrix);
colorbar;
colormap('hot');
xlabel('模式', 'FontSize', 12);
ylabel('模式', 'FontSize', 12);
title('主空间相似性矩阵', 'FontSize', 14);
set(gca, 'XTick', 1:5, 'YTick', 1:5);
set(gca, 'FontSize', 11);

% 添加数值标签
for i = 1:5
    for j = 1:5
        text(j, i, sprintf('%.2f', similarity_matrix(i,j)), ...
             'HorizontalAlignment', 'center', 'Color', 'white', 'FontSize', 10);
    end
end

sgtitle('主空间演化的几何分析', 'FontSize', 16);

%% 输出数值总结
fprintf('\n========== 详细数值分析 (按照现有代码定义) ==========\n');
fprintf('📊 NMSC详细分析 (归一化均方变化度):\n');
for i = 1:4
    fprintf('   Mode %d→%d: NMSC = %.4f\n', i, i+1, NMSC_history(i));
end
fprintf('   NMSC统计: 均值=%.4f, 标准差=%.4f, 范围=[%.4f, %.4f]\n', ...
        mean(NMSC_history), std(NMSC_history), min(NMSC_history), max(NMSC_history));

fprintf('\n📐 子空间夹角详细分析 (弧度):\n');
for i = 1:4
    if ~isnan(Subspace_angle_history(i))
        fprintf('   Mode %d→%d: 夹角 = %.4f弧度 (%.2f°)\n', i, i+1, ...
                Subspace_angle_history(i), Subspace_angle_history(i)*180/pi);
    else
        fprintf('   Mode %d→%d: 无法计算夹角\n', i, i+1);
    end
end
if ~isempty(valid_angles)
    fprintf('   夹角统计: 均值=%.4f弧度(%.2f°), 标准差=%.4f弧度, 范围=[%.4f, %.4f]弧度\n', ...
            mean(valid_angles), mean(valid_angles)*180/pi, std(valid_angles), min(valid_angles), max(valid_angles));
end

fprintf('\n🔍 主空间维度分析:\n');
for i = 1:5
    fprintf('   Mode %d: 维度 = %d\n', i, subspace_dims(i));
end
fprintf('   维度统计: 均值=%.1f, 范围=[%d, %d]\n', ...
        mean(subspace_dims), min(subspace_dims), max(subspace_dims));

end
