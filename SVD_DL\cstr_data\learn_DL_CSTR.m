%% SVD_DL方法应用于CSTR数据集
% 学习每一阶段的字典矩阵

rng(42);
clear; close all;

% 添加上级目录到路径，以便调用ksvd_simple和omp函数
addpath('..');

fprintf('========== SVD_DL方法应用于CSTR数据集 ==========\n');
fprintf('目标: 学习3个模式的字典演化过程\n');
fprintf('方法: SVD_DL (主空间锁定字典学习)\n\n');

%% 1. 加载CSTR数据
fprintf('1. 加载CSTR数据...\n');

% 加载CSTR 3模式训练数据
load('CSTR_3modes_train_data.mat');

% 提取3个模式的训练数据
train_data_mode1 = simout(2:400, :)';      % [特征维度 x 样本数] 模式1
train_data_mode2 = simout(406:801, :)';    % [特征维度 x 样本数] 模式2  
train_data_mode3 = simout(806:1201, :)';   % [特征维度 x 样本数] 模式3

fprintf('   模式1数据: %dx%d\n', size(train_data_mode1,1), size(train_data_mode1,2));
fprintf('   模式2数据: %dx%d\n', size(train_data_mode2,1), size(train_data_mode2,2));
fprintf('   模式3数据: %dx%d\n', size(train_data_mode3,1), size(train_data_mode3,2));

% 数据归一化
train_data_mode1 = normalize(train_data_mode1, 2, 'norm');
train_data_mode2 = normalize(train_data_mode2, 2, 'norm');
train_data_mode3 = normalize(train_data_mode3, 2, 'norm');

fprintf('   数据归一化完成\n');

%% 2. SVD_DL参数设置
fprintf('\n2. SVD_DL参数设置...\n');

% 字典学习参数
K = 20;                    % 字典原子数
sparsity = 2;              % 稀疏度
max_iter = 10;             % 每个模式的最大迭代次数

% 主空间锁定参数
energy_threshold = 0.9;    % 能量阈值
max_locked_dims = 5;       % 最大锁定维度

fprintf('   字典原子数: %d\n', K);
fprintf('   稀疏度: %d\n', sparsity);
fprintf('   能量阈值: %.1f\n', energy_threshold);
fprintf('   最大锁定维度: %d\n', max_locked_dims);

%% 3. 模式1: 初始字典学习
fprintf('\n3. 模式1: 初始字典学习...\n');

Y1 = train_data_mode1;
[m, n1] = size(Y1);

% 初始化字典
D_init = randn(m, K);
for k = 1:K
    D_init(:,k) = D_init(:,k) / norm(D_init(:,k));
end

% 使用ksvd_simple进行K-SVD训练
fprintf('   调用ksvd_simple进行字典学习...\n');
[D1, ~] = ksvd_simple(Y1, D_init, sparsity, max_iter);

fprintf('   模式1字典学习完成\n');

% 计算主空间
[U1, S1, ~] = svd(D1, 'econ');
singular_values1 = diag(S1);
energy1 = cumsum(singular_values1.^2) / sum(singular_values1.^2);
k_locked1 = find(energy1 >= energy_threshold, 1, 'first');
if isempty(k_locked1) || k_locked1 > max_locked_dims
    k_locked1 = min([max_locked_dims, size(U1,2)]);
end
U_locked1 = U1(:, 1:k_locked1);

fprintf('   模式1主空间维度: %d\n', k_locked1);

%% 4. 增量引入模式2-3并持续训练字典（双重保护机制）
fprintf('\n4. 增量引入模式2-3并持续训练字典（双重保护机制）...\n');

% 初始化历史记录
all_modes = 3;
Dictionary_history = cell(all_modes, 1);
imp_idx_history = cell(all_modes, 1);
U_locked_history = cell(all_modes, 1);

Dictionary_history{1} = D1;
U_locked_history{1} = U_locked1;
D_prev = D1;
U_locked_prev = U_locked1;
k_locked_prev = k_locked1;

lambda = 7.90e+01;    % 主空间正则项系数

% 训练数据
train_data_modes = {train_data_mode1, train_data_mode2, train_data_mode3};

for mode = 2:all_modes
    fprintf('\n==== 进入持续学习：模式%d ====\n', mode);

    % === 1. 新模式独立K-SVD训练新字典 ===
    Y_new = train_data_modes{mode};   % [特征维度 x 样本数]
    n_atoms = size(D_prev, 2);
    n_iter = 30;

    % 随机初始化新字典
    D_init = randn(m, n_atoms);
    for k = 1:n_atoms
        D_init(:,k) = D_init(:,k) / norm(D_init(:,k));
    end

    % 使用ksvd_simple进行独立K-SVD训练
    fprintf('   调用ksvd_simple进行新字典训练...\n');
    [D_new, ~] = ksvd_simple(Y_new, D_init, sparsity, n_iter);

    % === 2. SVD分解旧/新字典，主原子判定 ===
    fprintf('   主原子判定...\n');

    % 旧字典主原子判定
    [Uo, So, Vo] = svd(D_prev, 'econ');
    S_diag = diag(So);
    V_weight = abs(Vo) * S_diag;
    [~, idx] = sort(V_weight, 'descend');
    V_weight_sorted = V_weight(idx);
    energy_cum = cumsum(V_weight_sorted) / sum(V_weight_sorted);
    k_important = find(energy_cum >= 0.9, 1, 'first');
    imp_idx_old = idx(1:k_important);       % 旧主原子
    unimp_idx_old = idx(k_important+1:end); % 旧次原子

    % 新字典主原子判定
    [~, S_new, V_new] = svd(D_new, 'econ');
    V_weight_new = abs(V_new) * diag(S_new);
    [~, idx_new] = sort(V_weight_new, 'descend');
    V_weight_sorted_new = V_weight_new(idx_new);
    energy_cum_new = cumsum(V_weight_sorted_new) / sum(V_weight_sorted_new);
    k_important_new = find(energy_cum_new >= 0.9, 1, 'first');
    imp_idx_new = idx_new(1:k_important_new); % 新主原子

    % === 3. 构建融合字典（主原子保护） ===
    fprintf('   构建融合字典（主原子保护）...\n');
    D_fused = D_prev;    % 先拷贝旧字典

    % 新主原子替换旧次原子（按重要度不会顶出的替换）
    replace_cnt = min(numel(imp_idx_new), numel(unimp_idx_old));
    if replace_cnt > 0
        D_fused(:, unimp_idx_old(1:replace_cnt)) = D_new(:, imp_idx_new(1:replace_cnt));
    end

    % === 4. 主方向空间正则化微调（主空间保护） ===
    fprintf('   主方向空间正则化微调（主空间保护）...\n');
    U_locked = Uo(:, 1:k_locked_prev);
    P_locked = U_locked * U_locked';

    % 稀疏编码
    X = zeros(size(D_fused,2), size(Y_new,2));
    for n = 1:size(Y_new,2)
        X(:,n) = omp(D_fused, Y_new(:,n), sparsity);
    end

    % Sylvester方程求解
    A = lambda * (P_locked') * P_locked;
    B = X * X';
    C = lambda * (P_locked') * P_locked * D_prev + Y_new * X';
    D_fused = sylvester(A, B, C);

    % 归一化
    for k = 1:size(D_fused,2)
        D_fused(:,k) = D_fused(:,k) / norm(D_fused(:,k));
    end

    % === 5. 新字典SVD，用于下轮主空间锁定 ===
    [U_new, S_new, ~] = svd(D_fused, 'econ');
    singular_values_new = diag(S_new);
    energy_new = cumsum(singular_values_new.^2) / sum(singular_values_new.^2);
    k_locked_new = find(energy_new >= energy_threshold, 1, 'first');
    if isempty(k_locked_new) || k_locked_new > max_locked_dims
        k_locked_new = min([max_locked_dims, size(U_new,2)]);
    end
    U_locked_new = U_new(:, 1:k_locked_new);

    % 保存历史记录
    Dictionary_history{mode} = D_fused;
    imp_idx_history{mode} = imp_idx_old;
    U_locked_history{mode} = U_locked_new;
    D_prev = D_fused;
    U_locked_prev = U_locked_new;
    k_locked_prev = k_locked_new;

    fprintf('   模式%d主原子数=%d，主空间维度=%d\n', mode, k_important, k_locked_new);
end

fprintf('\n所有模式持续字典学习与双重保护已完成！\n');



%% 5. 保存结果
fprintf('\n5. 保存结果...\n');

% 保存字典演化历史（使用双重保护方法的结果）
Dictionary_history_CSTR = Dictionary_history;
U_locked_history_CSTR = U_locked_history;
imp_idx_history_CSTR = imp_idx_history;

% 计算主空间维度
subspace_dims_CSTR = zeros(all_modes, 1);
for i = 1:all_modes
    subspace_dims_CSTR(i) = size(U_locked_history_CSTR{i}, 2);
end

% 保存到文件
save('CSTR_SVD_DL_results.mat', 'Dictionary_history_CSTR', 'U_locked_history_CSTR', ...
     'subspace_dims_CSTR', 'imp_idx_history_CSTR', 'train_data_mode1', 'train_data_mode2', 'train_data_mode3');

fprintf('   字典演化历史已保存到: CSTR_SVD_DL_results.mat\n');
fprintf('   包含双重保护机制的完整结果\n');

%% 6. 结果分析
fprintf('\n6. 结果分析...\n');

% 计算NMSC (归一化均方变化度)
epsilon = 1e-8;
NMSC_history = zeros(all_modes-1, 1);

for i = 2:all_modes
    D_prev = Dictionary_history_CSTR{i-1};
    D_curr = Dictionary_history_CSTR{i};
    NMSC_history(i-1) = mean((D_curr(:) - D_prev(:)).^2 ./ (D_prev(:).^2 + epsilon));
    fprintf('   NMSC (模式%d→%d): %.6f\n', i-1, i, NMSC_history(i-1));
end

% 计算主方向子空间夹角
Subspace_angle_history = zeros(all_modes-1, 1);
for i = 2:all_modes
    try
        U_prev = U_locked_history_CSTR{i-1};
        U_curr = U_locked_history_CSTR{i};

        % 确保维度匹配
        min_dim = min(size(U_prev,2), size(U_curr,2));
        if min_dim > 0
            Subspace_angle_history(i-1) = subspace(U_prev(:,1:min_dim), U_curr(:,1:min_dim));
            fprintf('   子空间夹角 (模式%d→%d): %.4f弧度 (%.2f°)\n', i-1, i, ...
                    Subspace_angle_history(i-1), Subspace_angle_history(i-1)*180/pi);
        else
            Subspace_angle_history(i-1) = NaN;
            fprintf('   子空间夹角 (模式%d→%d): 无法计算\n', i-1, i);
        end
    catch
        Subspace_angle_history(i-1) = NaN;
        fprintf('   子空间夹角 (模式%d→%d): 计算失败\n', i-1, i);
    end
end

% 显示双重保护效果
fprintf('\n   双重保护机制效果:\n');
for i = 2:all_modes
    fprintf('   模式%d: 保护了%d个主原子，主空间维度=%d\n', ...
            i, length(imp_idx_history_CSTR{i}), subspace_dims_CSTR(i));
end

%% 7. 可视化
fprintf('\n7. 生成可视化...\n');

% 创建字典演化可视化
figure('Position', [100, 100, 1400, 1000]);

% 子图1-3: 字典热图
for i = 1:all_modes
    subplot(3,3,i);
    imagesc(Dictionary_history_CSTR{i});
    colorbar;
    title(sprintf('模式%d字典 D_%d', i, i), 'FontSize', 12);
    xlabel('原子索引');
    ylabel('特征维度');
end

% 子图4-5: 字典变化
for i = 2:all_modes
    subplot(3,3,3+i-1);
    diff_dict = Dictionary_history_CSTR{i} - Dictionary_history_CSTR{i-1};
    imagesc(diff_dict);
    colorbar;
    title(sprintf('字典变化 (D_%d - D_%d)', i, i-1), 'FontSize', 12);
    xlabel('原子索引');
    ylabel('特征维度');
end

% 子图6: 主空间维度演化
subplot(3,3,6);
bar(1:all_modes, subspace_dims_CSTR, 'FaceColor', [0.2, 0.6, 0.8]);
xlabel('模式');
ylabel('主空间维度');
title('主空间维度演化', 'FontSize', 12);
set(gca, 'XTick', 1:all_modes, 'XTickLabel', arrayfun(@(x) sprintf('模式%d', x), 1:all_modes, 'UniformOutput', false));
grid on;

% 添加数值标签
for i = 1:all_modes
    text(i, subspace_dims_CSTR(i) + 0.1, sprintf('%d', subspace_dims_CSTR(i)), ...
         'HorizontalAlignment', 'center', 'FontSize', 10);
end

% 子图7: NMSC变化趋势
subplot(3,3,7);
plot(2:all_modes, NMSC_history, 'bo-', 'LineWidth', 2, 'MarkerSize', 8);
xlabel('目标模式');
ylabel('NMSC');
title('NMSC变化趋势', 'FontSize', 12);
grid on;
for i = 1:length(NMSC_history)
    text(i+1, NMSC_history(i), sprintf('%.4f', NMSC_history(i)), ...
         'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', 'FontSize', 10);
end

% 子图8: 子空间夹角变化
subplot(3,3,8);
valid_angles = Subspace_angle_history(~isnan(Subspace_angle_history));
valid_modes = find(~isnan(Subspace_angle_history)) + 1;
if ~isempty(valid_angles)
    plot(valid_modes, valid_angles, 'rs-', 'LineWidth', 2, 'MarkerSize', 8);
    xlabel('目标模式');
    ylabel('子空间夹角 (弧度)');
    title('子空间夹角变化', 'FontSize', 12);
    grid on;
    for i = 1:length(valid_angles)
        text(valid_modes(i), valid_angles(i), sprintf('%.3f', valid_angles(i)), ...
             'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', 'FontSize', 10);
    end
end

% 子图9: 双重保护效果总结
subplot(3,3,9);
axis off;
summary_text = {'双重保护SVD_DL结果:', '', ...
                sprintf('模式数: %d', all_modes), ...
                sprintf('字典大小: %dx%d', size(Dictionary_history_CSTR{1})), ...
                sprintf('平均NMSC: %.4f', mean(NMSC_history))};

if ~isempty(valid_angles)
    summary_text{end+1} = sprintf('平均子空间夹角: %.4f弧度', mean(valid_angles));
end

summary_text{end+1} = '';
summary_text{end+1} = '双重保护机制:';
summary_text{end+1} = '1. 主原子保护';
summary_text{end+1} = '2. 主空间保护';

text(0.1, 0.8, summary_text, 'FontSize', 11, 'VerticalAlignment', 'top');

sgtitle('CSTR数据集 - 双重保护SVD_DL字典演化分析', 'FontSize', 16);

% 保存图像
savefig('CSTR_SVD_DL_dual_protection_evolution.fig');
fprintf('   可视化图像已保存到: CSTR_SVD_DL_dual_protection_evolution.fig\n');

fprintf('\n========== 双重保护SVD_DL方法应用完成 ==========\n');
fprintf('✅ 成功学习了%d个模式的字典演化过程\n', all_modes);
fprintf('🔒 应用了双重保护机制:\n');
fprintf('   1. 主原子保护: 保护重要原子不被替换\n');
fprintf('   2. 主空间保护: 通过正则化保持主方向稳定\n');
fprintf('📊 结果文件: CSTR_SVD_DL_results.mat\n');
fprintf('📈 可视化文件: CSTR_SVD_DL_dual_protection_evolution.fig\n');
fprintf('🎯 双重保护效果: 在适应新模式的同时保持了字典稳定性\n');

%% 辅助函数定义

function X = sylvester(A, B, C)
% SYLVESTER  求解Sylvester方程 AX + XB = C
%   X = sylvester(A, B, C)
%   求解矩阵方程 AX + XB = C

    % 检查输入维度
    [m, n] = size(C);

    if size(A,1) ~= m || size(A,2) ~= m
        error('矩阵A的维度不匹配');
    end
    if size(B,1) ~= n || size(B,2) ~= n
        error('矩阵B的维度不匹配');
    end

    % 使用Kronecker积方法求解
    % AX + XB = C 等价于 (I⊗A + B^T⊗I)vec(X) = vec(C)
    I_m = eye(m);
    I_n = eye(n);

    % 构造系数矩阵
    K = kron(I_n, A) + kron(B', I_m);

    % 向量化
    c_vec = C(:);

    % 求解线性系统
    x_vec = K \ c_vec;

    % 重新整形为矩阵
    X = reshape(x_vec, m, n);
end
