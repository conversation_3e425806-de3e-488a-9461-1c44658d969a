%% DMCDL催化数据集监测实验
% 模仿GILDL\monitoring_cuihua_exp_gildl.m将test_data_F1~F3应用在DMCDL的监测结果上

clc; clear; close all;
rng(42);

fprintf('========== DMCDL催化数据集监测实验 ==========\n');

%% 1. 加载最终融合字典
fprintf('1. 加载DMCDL最终字典...\n');

% 检查是否存在DMCDL催化数据集的训练结果
if exist('D_final_cuihua_dmcdl.mat', 'file')
    load('D_final_cuihua_dmcdl.mat', 'D_final_cuihua');
    D_K = D_final_cuihua;
    fprintf('   ✓ 成功加载DMCDL催化数据集最终字典\n');
    fprintf('   字典大小: %dx%d\n', size(D_K, 1), size(D_K, 2));
else
    fprintf('   ❌ 未找到D_final_cuihua_dmcdl.mat，开始训练...\n');
    cuihua_learn_D_dmcdl;
    load('D_final_cuihua_dmcdl.mat', 'D_final_cuihua');
    D_K = D_final_cuihua;
    fprintf('   ✓ DMCDL训练完成，字典大小: %dx%d\n', size(D_K, 1), size(D_K, 2));
end

sparsity = 3;  % 与训练时保持一致

%% 2. 拼接F1~F3训练/测试数据
fprintf('\n2. 加载催化数据集...\n');

Y_train = [];
Y_test = [];

% 加载训练数据 (data_selected_F1~F3)
fprintf('   加载训练数据:\n');
load('data_selected_F1.mat'); 
Y_train = [Y_train; data_selected];
fprintf('     F1训练数据: %d样本\n', length(data_selected));

load('data_selected_F2.mat'); 
Y_train = [Y_train; data_selected];
fprintf('     F2训练数据: %d样本\n', length(data_selected));

load('data_selected_F3.mat'); 
Y_train = [Y_train; data_selected];
fprintf('     F3训练数据: %d样本\n', length(data_selected));

% 加载测试数据 (test_data_F1~F3)
fprintf('   加载测试数据:\n');
load('test_data_F1.mat'); 
Y_test = [Y_test; test_data];
fprintf('     F1测试数据: %d样本\n', length(test_data));

load('test_data_F2.mat'); 
Y_test = [Y_test; test_data];
fprintf('     F2测试数据: %d样本\n', length(test_data));

load('test_data_F3.mat'); 
Y_test = [Y_test; test_data];
fprintf('     F3测试数据: %d样本\n', length(test_data));

% 数据格式转换
Y_train = Y_train';          % [特征维度, 总样本数]
Y_test = Y_test';

Y_train = cell2mat(Y_train);
Y_test = cell2mat(Y_test);

fprintf('   总训练数据: %dx%d\n', size(Y_train, 1), size(Y_train, 2));
fprintf('   总测试数据: %dx%d\n', size(Y_test, 1), size(Y_test, 2));

%% 3. 训练数据：OMP编码+R统计量
fprintf('\n3. 计算训练数据R统计量...\n');

R_train = zeros(1, size(Y_train, 2));
for i = 1:size(Y_train, 2)
    y = Y_train(:, i);
    x = omp(D_K, y, sparsity);
    R_train(i) = norm(y - D_K*x, 2)^2;
end

% 使用KDE估计统计量的分布并计算控制限
alpha = 0.01;  % 99%置信度
[f_R, xi_R] = ksdensity(R_train, 'Function', 'cdf');
idx_R = find(f_R >= 1 - alpha, 1, 'first');
R_limit = xi_R(idx_R);

fprintf('   训练样本数: %d\n', length(R_train));
fprintf('   R统计量范围: [%.6f, %.6f]\n', min(R_train), max(R_train));
fprintf('   控制限 (99%%置信度): %.6f\n', R_limit);

%% 4. 测试集：OMP编码+R统计量
fprintf('\n4. 计算测试数据R统计量...\n');

R_test = zeros(1, size(Y_test, 2));
for i = 1:size(Y_test, 2)
    y = Y_test(:, i);
    x = omp(D_K, y, sparsity);
    R_test(i) = norm(y - D_K*x, 2)^2;
end

fprintf('   测试样本数: %d\n', length(R_test));
fprintf('   R统计量范围: [%.6f, %.6f]\n', min(R_test), max(R_test));

%% 5. 可视化统计量与控制限
fprintf('\n5. 生成监测图表...\n');

figure('Position', [100, 100, 1200, 800]);

% 主监测图
subplot(2,2,[1,2]);
plot(R_test, 'b-', 'LineWidth', 1);
hold on;
yline(R_limit, '--r', 'LineWidth', 2);

% 标记不同数据集的分界线 (假设每个数据集样本数相等)
n_each = length(R_test) / 3;  % 每个数据集的样本数
xline(n_each, '--g', 'F1|F2', 'LineWidth', 1.5);
xline(2*n_each, '--g', 'F2|F3', 'LineWidth', 1.5);

xlabel('样本编号', 'FontSize', 12);
ylabel('R统计量', 'FontSize', 12);
title('DMCDL催化数据集监测 - R统计量与控制限', 'FontSize', 14);
legend('测试样本', '控制限 (99%)', 'Location', 'best');
grid on;

% 训练数据分布
subplot(2,2,3);
histogram(R_train, 50, 'Normalization', 'pdf', 'FaceAlpha', 0.7);
hold on;
xline(R_limit, '--r', 'LineWidth', 2);
xlabel('R统计量', 'FontSize', 12);
ylabel('概率密度', 'FontSize', 12);
title('训练数据R统计量分布', 'FontSize', 12);
legend('训练数据', '控制限', 'Location', 'best');
grid on;

% 测试数据分布
subplot(2,2,4);
histogram(R_test, 50, 'Normalization', 'pdf', 'FaceAlpha', 0.7);
hold on;
xline(R_limit, '--r', 'LineWidth', 2);
xlabel('R统计量', 'FontSize', 12);
ylabel('概率密度', 'FontSize', 12);
title('测试数据R统计量分布', 'FontSize', 12);
legend('测试数据', '控制限', 'Location', 'best');
grid on;

sgtitle('DMCDL方法 - 催化数据集过程监测', 'FontSize', 16);

% 保存监测图
savefig('dmcdl_cuihua_monitoring.fig');
fprintf('   监测图已保存到: dmcdl_cuihua_monitoring.fig\n');

%% 6. 计算FAR和FDR
fprintf('\n6. 计算检测性能指标...\n');

% 假设每个数据集的测试数据中，前半部分为正常，后半部分为故障
n_datasets = 3;
n_each = length(R_test) / n_datasets;
n_normal_each = floor(n_each / 2);  % 每个数据集前半部分为正常
n_fault_each = n_each - n_normal_each;  % 每个数据集后半部分为故障

FAR_all = zeros(n_datasets, 1);
FDR_all = zeros(n_datasets, 1);

for d = 1:n_datasets
    idx_start = (d-1)*n_each + 1;
    idx_normal = idx_start : idx_start + n_normal_each - 1;
    idx_fault = idx_start + n_normal_each : idx_start + n_each - 1;
    
    % 计算FAR (误报率) 和 FDR (检出率)
    FAR_all(d) = sum(R_test(idx_normal) > R_limit) / n_normal_each;
    FDR_all(d) = sum(R_test(idx_fault) > R_limit) / n_fault_each;
    
    fprintf('   F%d数据集: 正常样本%d个, 故障样本%d个\n', d, n_normal_each, n_fault_each);
    fprintf('   F%d数据集: FAR=%.4f, FDR=%.4f\n', d, FAR_all(d), FDR_all(d));
end

% 总体性能
FAR_overall = mean(FAR_all);
FDR_overall = mean(FDR_all);

%% 7. 与GILDL方法比较
fprintf('\n7. 与GILDL方法比较...\n');

comparison_results = struct();
comparison_results.DMCDL.FAR = FAR_overall;
comparison_results.DMCDL.FDR = FDR_overall;
comparison_results.DMCDL.method_name = 'DMCDL';

% 尝试加载GILDL催化数据集结果
if exist('gildl_cuihua_monitoring_results.mat', 'file')
    load('gildl_cuihua_monitoring_results.mat');
    if exist('FAR_overall', 'var') && exist('FDR_overall', 'var')
        comparison_results.GILDL.FAR = FAR_overall;
        comparison_results.GILDL.FDR = FDR_overall;
        comparison_results.GILDL.method_name = 'GILDL';
        fprintf('   ✓ 加载GILDL结果: FAR=%.4f, FDR=%.4f\n', FAR_overall, FDR_overall);
    end
end

% 生成比较图表
if length(fieldnames(comparison_results)) > 1
    fprintf('   生成方法比较图表...\n');
    
    figure('Position', [150, 150, 1000, 600]);
    
    methods = fieldnames(comparison_results);
    n_methods = length(methods);
    FAR_values = zeros(n_methods, 1);
    FDR_values = zeros(n_methods, 1);
    method_names = cell(n_methods, 1);
    
    for i = 1:n_methods
        method = methods{i};
        FAR_values(i) = comparison_results.(method).FAR;
        FDR_values(i) = comparison_results.(method).FDR;
        method_names{i} = comparison_results.(method).method_name;
    end
    
    % FAR比较
    subplot(1,2,1);
    bar(FAR_values, 'FaceColor', [0.3, 0.6, 0.9]);
    set(gca, 'XTickLabel', method_names);
    ylabel('FAR (误报率)', 'FontSize', 12);
    title('催化数据集FAR比较 (越小越好)', 'FontSize', 14);
    grid on;
    for i = 1:n_methods
        text(i, FAR_values(i), sprintf('%.4f', FAR_values(i)), ...
             'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom');
    end
    
    % FDR比较
    subplot(1,2,2);
    bar(FDR_values, 'FaceColor', [0.9, 0.6, 0.3]);
    set(gca, 'XTickLabel', method_names);
    ylabel('FDR (检出率)', 'FontSize', 12);
    title('催化数据集FDR比较 (越大越好)', 'FontSize', 14);
    grid on;
    for i = 1:n_methods
        text(i, FDR_values(i), sprintf('%.4f', FDR_values(i)), ...
             'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom');
    end
    
    sgtitle('催化数据集 - 字典学习方法监测性能比较', 'FontSize', 16);
    savefig('cuihua_methods_comparison.fig');
    fprintf('   比较图已保存到: cuihua_methods_comparison.fig\n');
end

%% 8. 保存监测结果
fprintf('\n8. 保存监测结果...\n');

dmcdl_cuihua_monitoring_results = struct();
dmcdl_cuihua_monitoring_results.method = 'DMCDL_Cuihua';
dmcdl_cuihua_monitoring_results.dictionary_size = size(D_K);
dmcdl_cuihua_monitoring_results.sparsity = sparsity;
dmcdl_cuihua_monitoring_results.R_limit = R_limit;
dmcdl_cuihua_monitoring_results.R_train = R_train;
dmcdl_cuihua_monitoring_results.R_test = R_test;
dmcdl_cuihua_monitoring_results.FAR_all = FAR_all;
dmcdl_cuihua_monitoring_results.FDR_all = FDR_all;
dmcdl_cuihua_monitoring_results.FAR_overall = FAR_overall;
dmcdl_cuihua_monitoring_results.FDR_overall = FDR_overall;
dmcdl_cuihua_monitoring_results.alpha = alpha;
dmcdl_cuihua_monitoring_results.comparison_results = comparison_results;

save('dmcdl_cuihua_monitoring_results.mat', 'dmcdl_cuihua_monitoring_results');
fprintf('   监测结果已保存到: dmcdl_cuihua_monitoring_results.mat\n');

%% 9. 结果总结
fprintf('\n========== DMCDL催化数据集监测结果总结 ==========\n');
fprintf('📊 基本信息:\n');
fprintf('   方法: DMCDL (双重记忆持续字典学习)\n');
fprintf('   数据集: 催化数据 (F1, F2, F3)\n');
fprintf('   字典大小: %dx%d\n', size(D_K));
fprintf('   稀疏度: %d\n', sparsity);
fprintf('   置信度: %.0f%%\n', (1-alpha)*100);

fprintf('\n📈 各数据集检测性能:\n');
fprintf('   数据集    FAR      FDR\n');
fprintf('   ------   ------   ------\n');
for d = 1:n_datasets
    fprintf('   F%d       %.4f   %.4f\n', d, FAR_all(d), FDR_all(d));
end

fprintf('\n🎯 总体性能:\n');
fprintf('   平均FAR = %.4f (误报率，越小越好)\n', FAR_overall);
fprintf('   平均FDR = %.4f (检出率，越大越好)\n', FDR_overall);

% 性能评估
if FAR_overall < 0.05 && FDR_overall > 0.8
    fprintf('   ✅ 检测性能优秀\n');
elseif FAR_overall < 0.1 && FDR_overall > 0.7
    fprintf('   ✅ 检测性能良好\n');
else
    fprintf('   ⚠️  检测性能需要改进\n');
end

% 方法比较
if length(fieldnames(comparison_results)) > 1
    fprintf('\n🔍 方法比较:\n');
    for i = 1:length(method_names)
        method = method_names{i};
        far_val = FAR_values(i);
        fdr_val = FDR_values(i);
        fprintf('   %s: FAR=%.4f, FDR=%.4f\n', method, far_val, fdr_val);
    end
    
    % 找出最佳方法
    [~, best_far_idx] = min(FAR_values);
    [~, best_fdr_idx] = max(FDR_values);
    
    fprintf('\n🏆 性能排名:\n');
    fprintf('   最低FAR: %s (%.4f)\n', method_names{best_far_idx}, FAR_values(best_far_idx));
    fprintf('   最高FDR: %s (%.4f)\n', method_names{best_fdr_idx}, FDR_values(best_fdr_idx));
end

fprintf('\n🎉 DMCDL催化数据集监测实验完成！\n');
