%% GILDL方法简化性能分析 - 使用learn_D_GILDL函数
% 专门处理单模式字典学习和性能评估

rng(42);

fprintf('========== GILDL方法简化性能分析开始 ==========\n');
fprintf('使用learn_D_GILDL函数进行字典学习\n');
fprintf('使用num_monitoring_exp.m的FAR/FDR计算方法\n\n');

%% ========== 1. 使用GILDL方法训练单个字典 ==========
fprintf('1. 使用GILDL方法训练字典...\n');

% 检查必要函数
if ~exist('learn_D_GILDL.m', 'file')
    error('找不到learn_D_GILDL.m函数');
end

try
    % 调用GILDL函数进行完整的多模式学习
    fprintf('   调用learn_D_GILDL进行多模式学习...\n');
    [Dictionary_history, NMSC, max_angle_rad, final_dict] = learn_D_GILDL();
    
    fprintf('   GILDL学习完成:\n');
    fprintf('     训练了 %d 个模式的字典\n', length(Dictionary_history));
    fprintf('     NMSC = %.4f\n', NMSC);
    fprintf('     最大主空间夹角 = %.4f弧度 (%.2f°)\n', max_angle_rad, max_angle_rad*180/pi);
    
    % 使用最终字典进行性能评估
    D_K = final_dict;
    
catch ME
    fprintf('   警告: learn_D_GILDL调用失败: %s\n', ME.message);
    fprintf('   使用备用方法...\n');
    
    % 备用方法：加载第一个模式数据并训练简单字典
    if exist('mode1_train.mat', 'file')
        load('mode1_train.mat');
    elseif exist('../SVD_DL/mode1_train.mat', 'file')
        load('../SVD_DL/mode1_train.mat');
    else
        error('找不到训练数据文件');
    end
    
    Y = train_data';
    n_atoms = min(20, size(Y,1));
    D_K = randn(size(Y,1), n_atoms);
    for k = 1:n_atoms
        D_K(:,k) = D_K(:,k) / norm(D_K(:,k));
    end
    
    % 设置默认值
    Dictionary_history = {D_K};
    NMSC = 0;
    max_angle_rad = 0;
end

fprintf('   最终字典大小: %dx%d\n', size(D_K,1), size(D_K,2));

%% ========== 2. 性能评估 - 使用num_monitoring_exp.m中的方法 ==========
fprintf('\n2. 性能评估 (使用num_monitoring_exp.m的FAR/FDR计算方法)...\n');

% 检查OMP函数
if ~exist('omp', 'file')
    addpath('../SVD_DL');
    if ~exist('omp', 'file')
        error('未找到OMP函数，请确保omp.m在路径中');
    end
end

% 调用专门的FAR/FDR计算函数
fprintf('   使用compute_far_fdr_gildl函数计算性能指标...\n');
try
    [FAR_overall, FDR_overall, FAR_vec, FDR_vec, R_test, R_limit] = compute_far_fdr_gildl(D_K);
    
    fprintf('   控制限: %.6f\n', R_limit);
    fprintf('   测试统计量范围: [%.6f, %.6f]\n', min(R_test), max(R_test));
    
catch ME
    fprintf('   警告: FAR/FDR计算失败: %s\n', ME.message);
    % 设置默认值
    FAR_overall = NaN; FDR_overall = NaN;
    FAR_vec = NaN(5,1); FDR_vec = NaN(5,1);
    R_test = []; R_limit = NaN;
end

%% ========== 3. 结果分析和显示 ==========
fprintf('\n========== GILDL方法分析结果 ==========\n');

% 显示字典学习结果
if exist('Dictionary_history', 'var') && iscell(Dictionary_history)
    fprintf('📊 字典学习结果:\n');
    fprintf('   训练模式数: %d\n', length(Dictionary_history));
    fprintf('   NMSC (归一化均方变化度): %.4f\n', NMSC);
    fprintf('   主空间最大夹角: %.4f弧度 (%.2f°)\n', max_angle_rad, max_angle_rad*180/pi);
    
    % 计算主空间维度
    subspace_dims = zeros(length(Dictionary_history), 1);
    for i = 1:length(Dictionary_history)
        [U, S, ~] = svd(Dictionary_history{i}, 'econ');
        singular_values = diag(S);
        energy = cumsum(singular_values.^2) / sum(singular_values.^2);
        k_locked = find(energy >= 0.9, 1, 'first');
        if isempty(k_locked)
            k_locked = min(3, size(U,2));
        end
        subspace_dims(i) = k_locked;
    end
    
    fprintf('\n🔍 主空间维度演化:\n');
    for i = 1:length(Dictionary_history)
        fprintf('   Mode %d: %d维\n', i, subspace_dims(i));
    end
else
    fprintf('📊 单字典学习结果:\n');
    fprintf('   字典大小: %dx%d\n', size(D_K,1), size(D_K,2));
    subspace_dims = size(D_K,2);
end

% 显示性能指标
if ~isnan(FAR_overall) && ~isnan(FDR_overall)
    fprintf('\n🎯 性能指标 (FAR/FDR):\n');
    for m = 1:5
        if ~isnan(FAR_vec(m)) && ~isnan(FDR_vec(m))
            fprintf('   Mode %d: FAR=%.4f, FDR=%.4f\n', m, FAR_vec(m), FDR_vec(m));
        end
    end
    fprintf('   总体性能: FAR=%.4f, FDR=%.4f\n', FAR_overall, FDR_overall);
else
    fprintf('\n🎯 性能指标计算失败\n');
end

%% ========== 4. 保存结果 ==========
results_gildl = struct();
results_gildl.method = 'GILDL';
results_gildl.Dictionary_history = Dictionary_history;
% 计算主空间历史
if iscell(Dictionary_history) && ~isempty(Dictionary_history)
    U_locked_history = cell(length(Dictionary_history), 1);
    for i = 1:length(Dictionary_history)
        [U, S, ~] = svd(Dictionary_history{i}, 'econ');
        singular_values = diag(S);
        energy = cumsum(singular_values.^2) / sum(singular_values.^2);
        k_locked = find(energy >= 0.9, 1, 'first');
        if isempty(k_locked)
            k_locked = min([3, size(U,2)]);
        end
        U_locked_history{i} = U(:, 1:k_locked);
    end
    results_gildl.U_locked_history = U_locked_history;
else
    results_gildl.U_locked_history = {};
end
results_gildl.final_dict = D_K;
results_gildl.NMSC_history = NMSC_history;
results_gildl.max_angle_rad = max_angle_rad;

if exist('subspace_dims', 'var')
    results_gildl.subspace_dims = subspace_dims;
end

% 添加缺失的字段以保持兼容性
if exist('max_angle_rad', 'var')
    results_gildl.max_angle_rad = max_angle_rad;
end

% 如果有多模式学习的结果，添加历史记录
if exist('Dictionary_history', 'var') && iscell(Dictionary_history) && length(Dictionary_history) > 1
    % 计算NMSC和子空间夹角历史
    NMSC_history = zeros(length(Dictionary_history)-1, 1);
    Subspace_angle_history = zeros(length(Dictionary_history)-1, 1);

    for i = 2:length(Dictionary_history)
        D_prev = Dictionary_history{i-1};
        D_curr = Dictionary_history{i};

        % 计算NMSC
        epsilon = 1e-8;
        deltaD = D_curr - D_prev;
        NMSC_history(i-1) = mean((deltaD(:).^2) ./ (D_prev(:).^2 + epsilon));

        % 计算子空间夹角
        [U_prev, S_prev, ~] = svd(D_prev, 'econ');
        [U_curr, ~, ~] = svd(D_curr, 'econ');

        energy_prev = cumsum(diag(S_prev).^2) / sum(diag(S_prev).^2);
        r_prev = find(energy_prev >= 0.9, 1, 'first');
        if isempty(r_prev), r_prev = min([3, size(U_prev,2)]); end

        r_curr = min([r_prev, size(U_curr,2)]);
        if r_curr > 0
            try
                Subspace_angle_history(i-1) = subspace(U_prev(:,1:r_curr), U_curr(:,1:r_curr));
            catch
                Subspace_angle_history(i-1) = NaN;
            end
        else
            Subspace_angle_history(i-1) = NaN;
        end
    end

    results_gildl.NMSC_history = NMSC_history;
    results_gildl.Subspace_angle_history = Subspace_angle_history;
else
    % 单模式情况，设置默认值
    results_gildl.NMSC_history = [];
    results_gildl.Subspace_angle_history = [];
end

if ~isnan(FAR_overall)
    results_gildl.FAR_overall = FAR_overall;
    results_gildl.FDR_overall = FDR_overall;
    results_gildl.FAR_vec = FAR_vec;
    results_gildl.FDR_vec = FDR_vec;
    results_gildl.R_test = R_test;
    results_gildl.R_limit = R_limit;
end

save('gildl_simple_analysis.mat', 'results_gildl');

fprintf('\n💾 GILDL简化分析结果已保存到 gildl_simple_analysis.mat\n');

%% ========== 5. 简单可视化 ==========
if ~isnan(FAR_overall) && ~isempty(R_test)
    fprintf('\n📊 生成简单可视化...\n');
    
    figure('Position', [100, 100, 1000, 400]);
    
    % 子图1: 监测统计量
    subplot(1,2,1);
    plot(R_test, 'b', 'LineWidth', 1); hold on;
    yline(R_limit, '--r', 'LineWidth', 2);
    xlabel('样本编号');
    ylabel('R统计量');
    title('GILDL: 监测统计量序列');
    legend('R统计量','控制限', 'Location', 'best');
    grid on;
    
    % 子图2: FAR/FDR性能
    subplot(1,2,2);
    x = 1:5;
    width = 0.35;
    bar(x - width/2, FAR_vec, width, 'DisplayName', 'FAR', 'FaceColor', [0.8, 0.4, 0.4]);
    hold on;
    bar(x + width/2, FDR_vec, width, 'DisplayName', 'FDR', 'FaceColor', [0.4, 0.8, 0.4]);
    set(gca, 'XTick', x, 'XTickLabel', arrayfun(@(i) sprintf('Mode %d', i), 1:5, 'UniformOutput', false));
    ylabel('性能指标');
    title('GILDL: FAR/FDR性能');
    legend('Location', 'best');
    grid on;
    
    sgtitle('GILDL方法简化性能分析', 'FontSize', 14);
end

fprintf('\n🎉 GILDL方法简化性能分析完成！\n');
