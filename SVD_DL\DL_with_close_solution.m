rng(42); % 固定随机种子便于复现
%% ===================== 1. 加载训练数据 =====================
load('mode1_train.mat');  % 得到变量 train_data，大小1000x8
Y = train_data';          % [8 x 1000]

%% ===================== 2. K-SVD 训练字典 ====================
n_atoms = 30;      % 字典原子数
sparsity = 2;      % 稀疏度
n_iter = 50;       % 迭代次数

% 字典初始化为标准正态分布+归一化
D_init = randn(8, n_atoms);
for k = 1:n_atoms
    D_init(:,k) = D_init(:,k) / norm(D_init(:,k));
end

% 训练
[Dictionary, ~] = ksvd_simple(Y, D_init, sparsity, n_iter);

%% ===================== 3. SVD分解与主方向空间锁定 =====================
[U, S, V] = svd(Dictionary, 'econ');
singular_values = diag(S);
energy = cumsum(singular_values.^2) / sum(singular_values.^2);
k_locked = find(energy >= 0.9, 1, 'first');
U_locked = U(:, 1:k_locked);

fprintf('主方向数目k_locked = %d\n', k_locked);
disp('主方向（U_locked）已完成上锁，可用于后续持续学习约束！');

save('mode1_locked_basis.mat', 'U_locked');  % 保存上锁主方向

figure;
plot(singular_values, 'o-'); xlabel('奇异值序号'); ylabel('奇异值大小');
title('KSVD字典的奇异值谱');
grid on;

%% ========= 4. 增量引入mode2到mode5并持续训练字典 =============

all_modes = 5   ;    % 工况数
Dictionary_history = cell(all_modes, 1);
imp_idx_history = cell(all_modes, 1);
U_locked_history = cell(all_modes, 1);

Dictionary_history{1} = Dictionary;
U_locked_history{1} = U_locked;
D_prev = Dictionary;
U_locked_prev = U_locked;
k_locked_prev = k_locked;

lambda = 7.90e+04;    % 主空间正则项系数
%micro_iter = 30; % 微调步数

%lr=1e-7;

for mode = 2:all_modes
    fprintf('==== 进入持续学习：mode%d ====\n', mode);

    % === 1. 新mode独立KSVD训练新字典 ===
    fname = sprintf('mode%d_train.mat', mode);
    load(fname);    % 得到 train_data
    Y_new = train_data';   % [8 x 1000]
    n_atoms = size(D_prev,2);
    sparsity = 2;
    n_iter = 30;

    % 随机初始化
    D_init = randn(8, n_atoms);
    for k = 1:n_atoms
        D_init(:,k) = D_init(:,k) / norm(D_init(:,k));
    end
    [D_new, ~] = ksvd_simple(Y_new, D_init, sparsity, n_iter);

    % === 2. SVD分解旧/新字典，主原子判定 ===
    [Uo, So, Vo] = svd(D_prev, 'econ');
    S_diag = diag(So);
    V_weight = abs(Vo) * S_diag;
    [~, idx] = sort(V_weight, 'descend');
    V_weight_sorted = V_weight(idx);
    energy_cum = cumsum(V_weight_sorted) / sum(V_weight_sorted);
    k_important = find(energy_cum >= 0.9, 1, 'first');
    imp_idx_old = idx(1:k_important);       % 旧主原子
    unimp_idx_old = idx(k_important+1:end); % 旧次原子

    [~, S_new, V_new] = svd(D_new, 'econ');
    V_weight_new = abs(V_new) * diag(S_new);
    [~, idx_new] = sort(V_weight_new, 'descend');
    V_weight_sorted_new = V_weight_new(idx_new);
    energy_cum_new = cumsum(V_weight_sorted_new) / sum(V_weight_sorted_new);
    k_important_new = find(energy_cum_new >= 0.9, 1, 'first');
    imp_idx_new = idx_new(1:k_important_new); % 新主原子
    %unimp_idx_new = idx_new(k_important_new+1:end);

    % === 3. 构建新字典 ===
    D_fused = D_prev;    % 先拷贝旧字典

    % 3.2 新主原子替换旧次原子（按重要度不会顶出的替换）
    replace_cnt = min(numel(imp_idx_new), numel(unimp_idx_old));
    D_fused(:, unimp_idx_old(1:replace_cnt)) = D_new(:, imp_idx_new(1:replace_cnt));

    % 若新主原子比旧次原子还多，剩余部分可选软融合（此处暂不处理）

    
    % === 4. 主方向空间正则化微调（主空间保护） ===
    U_locked = Uo(:, 1:k_locked_prev);
    P_locked = U_locked * U_locked';

    X = zeros(size(D_fused,2), size(Y_new,2));
    for n = 1:size(Y_new,2)
        X(:,n) = omp(D_fused, Y_new(:,n), sparsity);
    end

    A = lambda * (P_locked') * P_locked;
    B = X * X';
    C = lambda * (P_locked') * P_locked * D_prev + Y_new * X';
    D_fused = sylvester(A,B,C);

    for k = 1:size(D_fused,2)
        D_fused(:,k) = D_fused(:,k) / norm(D_fused(:,k));
    end

    % 新主空间
    [U_new, S_new, ~] = svd(D_fused, 'econ');
    singular_values_new = diag(S_new);
    energy_new = cumsum(singular_values_new.^2) / sum(singular_values_new.^2);
    k_locked_new = find(energy_new >= 0.9, 1, 'first');
    U_locked_new = U_new(:, 1:k_locked_new);

    Dictionary_history{mode} = D_fused;
    imp_idx_history{mode} = imp_idx_old;  % 这里可跟踪记录
    U_locked_history{mode} = U_locked_new;
    D_prev = D_fused;
    U_locked_prev = U_locked_new;
    k_locked_prev = k_locked_new;

    fprintf('mode%d主原子数=%d，主空间维度=%d\n', mode, k_important, k_locked_new);

    % === 5. 新字典SVD，用于下轮主空间锁定 ===
    [U_new, S_new, ~] = svd(D_fused, 'econ');
    singular_values_new = diag(S_new);
    energy_new = cumsum(singular_values_new.^2) / sum(singular_values_new.^2);
    k_locked_new = find(energy_new >= 0.9, 1, 'first');
    U_locked_new = U_new(:, 1:k_locked_new);

    Dictionary_history{mode} = D_fused;
    imp_idx_history{mode} = imp_idx_old;
    U_locked_history{mode} = U_locked_new;
    D_prev = D_fused;
    U_locked_prev = U_locked_new;
    k_locked_prev = k_locked_new;

    fprintf('mode%d主原子数=%d，主空间维度=%d\n', mode, k_important, k_locked_new);
end

disp('所有mode持续字典学习与双重保护已完成！');

%% ==================== 可视化字典变化热图与NMSC ====================
D1 = Dictionary_history{1};
Dlast = Dictionary_history{end};

save("Dlast.mat",'Dlast');

deltaD = Dlast - D1;

figure;
imagesc(deltaD);
colorbar;
xlabel('字典原子编号'); ylabel('观测量编号');
title('最终字典与mode1字典差分热图');
set(gca,'FontSize',12);

figure;
imagesc(abs(deltaD));
colorbar;
xlabel('字典原子编号'); ylabel('观测量编号');
title('字典原子绝对变化热图');
set(gca,'FontSize',12);

epsilon = 1e-8;
num = (Dlast - D1).^2;
den = D1.^2 + epsilon;
nmsc = mean(num(:) ./ den(:));
fprintf('归一化均方变化度（NMSC）: %.4f\n', nmsc);

% 主空间变化度（夹角距离）
U1 = U_locked_history{1};
Ulast = U_locked_history{end};
min_dim = min(size(U1,2), size(Ulast,2));
U1 = U1(:,1:min_dim); Ulast = Ulast(:,1:min_dim);
subspace_dist = subspace(U1, Ulast); % Matlab自带subspace函数
fprintf('主空间夹角变化度: %.4f\n', subspace_dist);


