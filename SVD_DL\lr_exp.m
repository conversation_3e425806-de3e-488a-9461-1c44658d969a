lr_list = [1e-7 : 0.1e-7 : 1.5e-7,0];

n_lr = numel(lr_list);
nmsc_arr = zeros(n_lr, 1);
subspace_dist_arr = zeros(n_lr, 1);

lambda = 1e7;
micro_iter = 30;
modes = 5;
n_atoms = 30;
sparsity = 2;
n_iter = 30;

for idx = 1:n_lr
    lr = lr_list(idx);
    [Dict_hist, U_hist, NMSC, sub_dist] = continual_dictionary_learning(...
        lambda, micro_iter, modes, n_atoms, sparsity, n_iter, lr);

    D1 = Dict_hist{1};
    Dlast = Dict_hist{end};
    deltaD_cell{idx} = Dlast - D1;

    nmsc_arr(idx) = NMSC;
    subspace_dist_arr(idx) = sub_dist;
    fprintf('lr=%.1e: NMSC=%.4f, SubspaceDist=%.4f\n', ...
        lr, NMSC, sub_dist);
end

% ========== 绘制所有差分热图 ==========
figure;
n_col = 5;   % 每行5张
n_row = ceil(n_lr / n_col);
for idx = 1:n_lr
    subplot(n_row, n_col, idx);
    imagesc(deltaD_cell{idx});
    colorbar;
    title(['lr=' num2str(lr_list(idx), '%.1e')]);
    xlabel('字典原子编号');
    ylabel('观测量编号');
    set(gca,'FontSize',10);
end
sgtitle('不同学习率下最终字典与mode1字典差分热图');

figure;
subplot(1,2,1);
semilogx(lr_list, nmsc_arr, '-o','LineWidth',2);
xlabel('\lambda'); ylabel('归一化均方变化度NMSC');
title('字典整体变化度');
grid on;
subplot(1,2,2);
semilogx(lr_list, subspace_dist_arr, '-o','LineWidth',2);
xlabel('\lambda'); ylabel('主空间夹角距离 (rad)');
title('主空间变化度');
grid on;

