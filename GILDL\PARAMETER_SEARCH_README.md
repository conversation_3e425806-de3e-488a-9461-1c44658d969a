# GILDL催化数据集参数搜索

## 🎯 问题描述

在`cuihua_learn_D.m`中应用GILDL方法到催化数据集时，学习出的字典矩阵元素出现NaN值，怀疑是参数选取问题。

## 🔍 搜索目标

找到不会产生NaN值的参数组合：
- `n_atoms`: [30, 100] - 字典原子数
- `sparsity`: [1, 7] - OMP稀疏度  
- `lambdaProtect`: [1e-10, 1e10] - 主空间保护参数

## 📁 搜索脚本

### 1. `quick_parameter_screening.m` ⭐**推荐首选**⭐

**特点**: 两阶段搜索，快速高效
- **阶段1**: 粗略筛选，快速找到可能有效的范围
- **阶段2**: 精细搜索，在有效范围内详细搜索

**优势**:
- 搜索时间短（通常几分钟到十几分钟）
- 智能范围缩减
- 提供多个推荐参数

**使用方法**:
```matlab
cd('GILDL')
quick_parameter_screening
```

### 2. `parameter_grid_search_cuihua.m` 

**特点**: 完整网格搜索
- 搜索所有参数组合
- 详细的错误类型分析
- 完整的结果记录

**注意**: 搜索时间较长（可能需要几小时）

**使用方法**:
```matlab
cd('GILDL')
parameter_grid_search_cuihua
```

## 🚀 推荐使用流程

### 步骤1: 快速筛选
```matlab
cd('GILDL')
quick_parameter_screening
```

### 步骤2: 查看结果
脚本会输出类似：
```
========== 推荐参数 ==========
推荐参数 (中位数选择):
  n_atoms = 50
  sparsity = 3
  lambdaProtect = 1.00e-02

保守参数 (最小lambda):
  n_atoms = 40
  sparsity = 2
  lambdaProtect = 1.00e-05
```

### 步骤3: 应用参数
在`cuihua_learn_D.m`中修改：
```matlab
n_atoms = 50;           % 使用推荐值
sparsity = 3;           % 使用推荐值
lambdaProtect = 1e-2;   % 使用推荐值
```

### 步骤4: 验证结果
运行修改后的`cuihua_learn_D.m`，检查是否还有NaN值。

## 📊 搜索范围说明

### 参数范围设计原理

#### n_atoms (字典原子数)
- **范围**: [30, 100]
- **考虑**: 必须 ≤ 数据维度
- **影响**: 过大可能导致过拟合，过小表示能力不足

#### sparsity (稀疏度)
- **范围**: [1, 7] 
- **考虑**: 通常为数据维度的10%-50%
- **影响**: 过大可能导致数值不稳定

#### lambdaProtect (保护参数)
- **范围**: [1e-10, 1e10]
- **考虑**: 对数分布搜索
- **影响**: 过大过度保护，过小保护不足

## 🔧 NaN值产生的可能原因

### 1. 数值不稳定
- **原因**: 矩阵条件数过大
- **解决**: 调整`lambdaProtect`和`eps_norm`

### 2. 维度不匹配
- **原因**: `n_atoms > 数据维度`
- **解决**: 减小`n_atoms`

### 3. 稀疏度过大
- **原因**: `sparsity`接近或超过数据维度
- **解决**: 减小`sparsity`

### 4. 权重矩阵奇异
- **原因**: `Ws`矩阵变为奇异
- **解决**: 增大`eps_norm`或调整`lambdaProtect`

## 📈 结果文件

### `gildl_cuihua_quick_screening_results.mat`
包含：
- `valid_coarse`: 粗略筛选的有效参数
- `valid_fine`: 精细搜索的有效参数  
- `search_ranges`: 搜索范围信息
- `timing`: 搜索耗时统计

### `gildl_cuihua_parameter_search_results.mat`
包含：
- `valid_params`: 所有有效参数组合
- `invalid_params`: 所有无效参数组合
- `search_log`: 详细搜索日志
- 错误类型统计

## 🎯 参数选择建议

### 保守策略
- 选择较小的`lambdaProtect`值
- 选择中等的`n_atoms`和`sparsity`
- 优先保证数值稳定性

### 性能策略  
- 在有效范围内选择较大的`n_atoms`
- 适当增大`sparsity`提高表示能力
- 平衡保护强度和适应性

### 调试策略
如果仍有NaN值：
1. 检查数据预处理
2. 增大`eps_norm`（如改为1e-4）
3. 减少迭代次数观察
4. 添加更多数值检查

## ⚠️ 注意事项

### 数据要求
- 确保催化数据已正确加载
- 检查数据中是否已有NaN或Inf值
- 验证数据维度和样本数

### 计算资源
- 快速筛选：通常几分钟
- 完整搜索：可能需要几小时
- 建议先运行快速筛选

### 结果验证
- 找到有效参数后，建议多次运行验证
- 检查最终字典的数值特性
- 评估算法收敛性

## 🎉 预期效果

成功找到有效参数后，`cuihua_learn_D.m`应该能够：
- 正常完成所有模式的字典学习
- 字典矩阵无NaN或Inf值
- 生成合理的NMSC和主空间夹角指标
- 产生有意义的可视化结果

如果搜索后仍无有效参数，建议检查：
1. 数据质量和预处理
2. 算法实现细节
3. 数值精度设置
4. 硬件计算能力
