# JMSDL方法数据修改总结

## 🎯 修改目标

将JMSDL方法中的训练数据从原始的合成数据改为使用SVD_DL中的真实训练数据，同时保持JMSDL算法的核心逻辑不变。

## 📁 修改的文件

### 1. `Copy_of_JMSDL_new2_num.m` (主要修改)
**修改内容**:
- ✅ 替换数据生成部分，改为加载SVD_DL数据
- ✅ 支持从多个位置加载数据（../SVD_DL/ 或当前目录）
- ✅ 更新样本数量计算（从200改为1000）
- ✅ 保持原有的数据归一化方式
- ✅ 保持JMSDL算法逻辑完全不变

### 2. `copy_svd_dl_data.m` (新增)
**功能**: 将SVD_DL的数据文件复制到JMSDL目录
- 复制mode1-5的训练数据
- 复制mode1-5的正常测试数据
- 复制mode1-5的故障测试数据
- 验证复制的数据完整性

### 3. `run_jmsdl_with_svd_data.m` (新增)
**功能**: 一键运行使用SVD_DL数据的JMSDL方法
- 自动检查和准备数据
- 运行JMSDL字典学习
- 分析输出结果
- 提供使用建议

## 🔄 数据变化对比

### 原始JMSDL数据
```matlab
% 使用generate_data()函数生成合成数据
train_mode1 = generate_data(1, 200)';  % [特征维度 x 200]
train_mode2 = generate_data(2, 200)';  % [特征维度 x 200]
train_mode3 = generate_data(3, 200)';  % [特征维度 x 200]
```

**特点**:
- 合成数据，基于预定义的分布
- 每模式200个样本
- 特征维度由generate_data()决定

### 修改后的JMSDL数据
```matlab
% 加载SVD_DL的真实训练数据（5个模式）
load('mode1_train.mat'); train_mode1 = train_data';  % [8 x 1000]
load('mode2_train.mat'); train_mode2 = train_data';  % [8 x 1000]
load('mode3_train.mat'); train_mode3 = train_data';  % [8 x 1000]
load('mode4_train.mat'); train_mode4 = train_data';  % [8 x 1000]
load('mode5_train.mat'); train_mode5 = train_data';  % [8 x 1000]
```

**特点**:
- 真实数据，来自SVD_DL项目
- 5个模式，每模式1000个样本
- 8维特征（与SVD_DL一致）
- 总样本数: 5000个

## 📊 数据格式说明

### SVD_DL数据文件格式
- **训练数据**: `mode{i}_train.mat` 包含变量 `train_data` [1000 x 8]
- **正常测试数据**: `mode{i}_test_normal.mat` 包含变量 `test_normal_data` [500 x 8]
- **故障测试数据**: `mode{i}_test_fault.mat` 包含变量 `test_fault_data` [500 x 8]

### JMSDL使用的数据格式
- **训练数据**: 转置后为 [8 x 1000]，符合JMSDL的输入要求
- **归一化**: 使用`normalize()`函数按行归一化
- **拼接**: 将3个模式的数据拼接为 [8 x 3000]

## 🔧 算法保持不变

### JMSDL核心算法组件
1. **初始字典训练**: 使用K-SVD在Mode 1数据上训练
2. **增量学习**: 逐步引入Mode 2和Mode 3数据
3. **字典更新**: 使用JMSDL的特有更新规则
4. **稀疏编码**: 使用OMP进行稀疏编码

### 保持不变的参数
- `K = 20`: 字典原子数
- `L = 2`: 稀疏度
- `maxIter = 10`: 每个模式的最大迭代次数
- 归一化方式和更新规则

## 🚀 使用方法

### 方法1: 简化运行（推荐）
```matlab
cd('JMSDL')
run('run_jmsdl_simple.m')
```

### 方法2: 直接运行
```matlab
cd('JMSDL')
Copy_of_JMSDL_new2_num
```

### 方法3: 完整功能运行
```matlab
cd('JMSDL')
run('run_jmsdl_with_svd_data.m')
```

**注意**: 确保训练数据文件 `mode1_train.mat` 到 `mode5_train.mat` 已在JMSDL目录中。

## 📈 预期结果

### 输出文件
- `D_n_num.mat`: 最终训练的字典 [8 x 20]

### 控制台输出示例
```
加载SVD_DL训练数据...
✓ 从 ../SVD_DL/ 成功加载训练数据
  每个模式数据大小: 8x1000
样本数量: Mode1=1000, Mode2=1000, Mode3=1000

=== 模式 1: 初始 K-SVD 训练 ===
模式 1, 迭代  1: 重构误差 = 1.2345e+02
...

=== 模式 2: 增量学习 ===
模式 2, 迭代  1: 重构误差 = 9.8765e+01
...

所有模式（2,3,4）数据逐步更新完成，最终字典 D_n 已得到。
```

## 🆚 与其他方法的比较

### 数据一致性
- ✅ **JMSDL**: 现在使用SVD_DL数据
- ✅ **SVD_DL**: 使用自己的数据
- ✅ **GILDL**: 使用SVD_DL数据

### 算法特点对比
| 方法 | 数据源 | 学习方式 | 主要特点 |
|------|--------|----------|----------|
| SVD_DL | SVD_DL数据 | 主空间锁定 | 保持主方向稳定性 |
| GILDL | SVD_DL数据 | 增量学习 | 平衡稳定性和适应性 |
| JMSDL | SVD_DL数据 | 联合稀疏学习 | 多模式联合优化 |

## ✅ 验证清单

### 数据验证
- ✅ 数据文件正确加载
- ✅ 数据维度匹配 (8维特征)
- ✅ 样本数量正确 (每模式1000个)
- ✅ 数据归一化正确应用

### 算法验证
- ✅ JMSDL核心逻辑保持不变
- ✅ 参数设置保持不变
- ✅ 输出格式保持不变
- ✅ 字典大小正确 [8 x 20]

### 兼容性验证
- ✅ 与SVD_DL数据格式兼容
- ✅ 与现有分析工具兼容
- ✅ 支持后续性能比较

## 🔍 故障排除

### 常见问题
1. **数据文件未找到**: 运行`copy_svd_dl_data.m`复制数据
2. **维度不匹配**: 检查数据转置是否正确
3. **内存不足**: SVD_DL数据较大，确保足够内存
4. **路径问题**: 确保在JMSDL目录中运行

### 调试方法
```matlab
% 检查数据加载
load('../SVD_DL/mode1_train.mat');
fprintf('数据大小: %dx%d\n', size(train_data,1), size(train_data,2));

% 检查转置后的数据
train_mode1 = train_data';
fprintf('转置后大小: %dx%d\n', size(train_mode1,1), size(train_mode1,2));
```

## 🎉 总结

通过这次修改：
1. **保持算法完整性**: JMSDL的核心算法逻辑完全不变
2. **统一数据源**: 所有方法现在都使用相同的SVD_DL数据
3. **提高可比性**: 便于进行公平的性能比较
4. **增强实用性**: 使用真实数据而非合成数据
5. **保持兼容性**: 输出格式与原始JMSDL一致

现在JMSDL方法可以与SVD_DL和GILDL方法在相同的数据基础上进行性能比较！
