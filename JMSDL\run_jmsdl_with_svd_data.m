%% 使用SVD_DL数据运行JMSDL方法
% 这个脚本会自动处理数据加载并运行JMSDL字典学习

fprintf('========== 使用SVD_DL数据运行JMSDL方法 ==========\n');

%% 1. 检查数据文件
fprintf('1. 检查数据文件...\n');

% 检查必要的训练数据文件
required_files = {'mode1_train.mat', 'mode2_train.mat', 'mode3_train.mat', 'mode4_train.mat', 'mode5_train.mat'};
missing_files = {};

for i = 1:length(required_files)
    if ~exist(required_files{i}, 'file')
        missing_files{end+1} = required_files{i};
    end
end

if ~isempty(missing_files)
    fprintf('   ❌ 缺少以下数据文件:\n');
    for i = 1:length(missing_files)
        fprintf('      - %s\n', missing_files{i});
    end
    error('请确保所有必要的数据文件都在当前目录中。');
else
    fprintf('   ✓ 所有必要的数据文件都存在\n');
end

%% 2. 运行JMSDL字典学习
fprintf('\n2. 运行JMSDL字典学习...\n');
try
    tic;
    
    % 运行主要的JMSDL算法
    Copy_of_JMSDL_new2_num;
    
    elapsed_time = toc;
    fprintf('   ✓ JMSDL字典学习完成，耗时: %.2f秒\n', elapsed_time);
    
catch ME
    fprintf('   ❌ JMSDL运行失败: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('   错误位置: %s 第%d行\n', ME.stack(1).name, ME.stack(1).line);
    end
    return;
end

%% 3. 检查输出结果
fprintf('\n3. 检查输出结果...\n');

% 检查是否生成了字典文件
if exist('D_n_num.mat', 'file')
    load('D_n_num.mat', 'D_n');
    fprintf('   ✓ 最终字典已保存: D_n_num.mat\n');
    fprintf('   字典大小: %dx%d\n', size(D_n,1), size(D_n,2));
    
    % 显示字典的基本信息
    fprintf('   字典统计信息:\n');
    fprintf('     - 最小值: %.6f\n', min(D_n(:)));
    fprintf('     - 最大值: %.6f\n', max(D_n(:)));
    fprintf('     - 均值: %.6f\n', mean(D_n(:)));
    fprintf('     - 标准差: %.6f\n', std(D_n(:)));
    
    % 检查字典列的归一化
    col_norms = vecnorm(D_n);
    fprintf('     - 列范数范围: [%.6f, %.6f]\n', min(col_norms), max(col_norms));
    
else
    fprintf('   ❌ 未找到输出字典文件\n');
end

%% 4. 数据对比分析
fprintf('\n4. 数据使用情况分析...\n');

% 显示使用的数据信息
if exist('mode1_train.mat', 'file')
    load('mode1_train.mat');
    fprintf('   使用的训练数据:\n');
    fprintf('     - Mode 1: %dx%d\n', size(train_data,1), size(train_data,2));
    
    if exist('mode2_train.mat', 'file')
        load('mode2_train.mat');
        fprintf('     - Mode 2: %dx%d\n', size(train_data,1), size(train_data,2));
    end
    
    if exist('mode3_train.mat', 'file')
        load('mode3_train.mat');
        fprintf('     - Mode 3: %dx%d\n', size(train_data,1), size(train_data,2));
    end
    
    fprintf('   数据特点:\n');
    fprintf('     - 特征维度: %d (与SVD_DL一致)\n', size(train_data,1));
    fprintf('     - 每模式样本数: %d (SVD_DL标准)\n', size(train_data,2));
end

%% 5. 与原始JMSDL的对比
fprintf('\n5. 与原始JMSDL方法的对比:\n');
fprintf('   原始JMSDL:\n');
fprintf('     - 使用generate_data()生成的合成数据\n');
fprintf('     - 每模式200个样本\n');
fprintf('     - 特征维度可能不同\n');
fprintf('   \n');
fprintf('   修改后的JMSDL:\n');
fprintf('     - 使用SVD_DL的真实训练数据\n');
fprintf('     - 每模式1000个样本\n');
fprintf('     - 8维特征（与SVD_DL一致）\n');
fprintf('     - 保持原有的JMSDL算法逻辑不变\n');

%% 6. 后续使用建议
fprintf('\n6. 后续使用建议:\n');
fprintf('   - 生成的字典文件: D_n_num.mat\n');
fprintf('   - 可用于故障检测和监测任务\n');
fprintf('   - 与SVD_DL和GILDL方法进行性能对比\n');
fprintf('   - 使用相同的测试数据评估性能\n');

fprintf('\n========== JMSDL (使用SVD_DL数据) 运行完成 ==========\n');
