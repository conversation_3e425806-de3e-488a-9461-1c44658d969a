%% GILDL催化数据集参数网格搜索
% 搜索不会产生NaN值的参数组合
% 参数范围: n_atoms=[30~100], sparsity=[1~7], lambdaProtect=[1e-10~1e10]

clc; clear; close all;
rng(42);

fprintf('========== GILDL催化数据集参数网格搜索 ==========\n');
fprintf('目标: 找到不会产生NaN值的参数组合\n');
fprintf('搜索范围:\n');
fprintf('  n_atoms: [30, 100]\n');
fprintf('  sparsity: [1, 7]\n');
fprintf('  lambdaProtect: [1e-10, 1e10]\n\n');

%% 参数搜索范围定义
n_atoms_range = 30:10:100;                    % [30, 40, 50, 60, 70, 80, 90, 100]
sparsity_range = 1:7;                         % [1, 2, 3, 4, 5, 6, 7]
lambdaProtect_range = logspace(-10, 10, 21);  % 1e-10 到 1e10，21个点

fprintf('实际搜索点数:\n');
fprintf('  n_atoms: %d个点 %s\n', length(n_atoms_range), mat2str(n_atoms_range));
fprintf('  sparsity: %d个点 %s\n', length(sparsity_range), mat2str(sparsity_range));
fprintf('  lambdaProtect: %d个点 (对数分布)\n', length(lambdaProtect_range));
fprintf('  总组合数: %d\n\n', length(n_atoms_range) * length(sparsity_range) * length(lambdaProtect_range));

%% 预加载数据以提高效率
fprintf('预加载催化数据...\n');
data_modes = cell(3, 1);
for i = 1:3
    load(['data_selected_F', num2str(i), '.mat']);
    data_modes{i} = cell2mat(data_selected)';
    fprintf('  模式%d数据大小: %dx%d\n', i, size(data_modes{i}, 1), size(data_modes{i}, 2));
end

%% 初始化结果存储
valid_params = [];
invalid_params = [];
search_log = [];

total_combinations = length(n_atoms_range) * length(sparsity_range) * length(lambdaProtect_range);
current_combination = 0;

fprintf('\n开始网格搜索...\n');
tic;

%% 网格搜索主循环
for i = 1:length(n_atoms_range)
    n_atoms = n_atoms_range(i);
    
    for j = 1:length(sparsity_range)
        sparsity = sparsity_range(j);
        
        for k = 1:length(lambdaProtect_range)
            lambdaProtect = lambdaProtect_range(k);
            current_combination = current_combination + 1;
            
            % 显示进度
            if mod(current_combination, 50) == 0 || current_combination == 1
                elapsed_time = toc;
                estimated_total = elapsed_time * total_combinations / current_combination;
                remaining_time = estimated_total - elapsed_time;
                
                fprintf('进度: %d/%d (%.1f%%), 已用时: %.1f分钟, 预计剩余: %.1f分钟\n', ...
                        current_combination, total_combinations, ...
                        100*current_combination/total_combinations, ...
                        elapsed_time/60, remaining_time/60);
            end
            
            % 测试当前参数组合
            [is_valid, error_info] = test_gildl_parameters(data_modes, n_atoms, sparsity, lambdaProtect);
            
            % 记录结果
            param_combo = struct('n_atoms', n_atoms, 'sparsity', sparsity, ...
                               'lambdaProtect', lambdaProtect, 'is_valid', is_valid, ...
                               'error_info', error_info);
            
            search_log = [search_log; param_combo];
            
            if is_valid
                valid_params = [valid_params; n_atoms, sparsity, lambdaProtect];
                if size(valid_params, 1) <= 10  % 只显示前10个有效组合
                    fprintf('  ✓ 有效: n_atoms=%d, sparsity=%d, lambdaProtect=%.2e\n', ...
                            n_atoms, sparsity, lambdaProtect);
                end
            else
                invalid_params = [invalid_params; n_atoms, sparsity, lambdaProtect];
            end
        end
    end
end

total_time = toc;

%% 结果分析和保存
fprintf('\n========== 搜索结果总结 ==========\n');
fprintf('总搜索时间: %.1f分钟\n', total_time/60);
fprintf('总组合数: %d\n', total_combinations);
fprintf('有效组合数: %d (%.1f%%)\n', size(valid_params, 1), 100*size(valid_params, 1)/total_combinations);
fprintf('无效组合数: %d (%.1f%%)\n', size(invalid_params, 1), 100*size(invalid_params, 1)/total_combinations);

if size(valid_params, 1) > 0
    fprintf('\n前20个有效参数组合:\n');
    fprintf('%-8s %-10s %-15s\n', 'n_atoms', 'sparsity', 'lambdaProtect');
    fprintf('%-8s %-10s %-15s\n', '-------', '--------', '-------------');
    
    display_count = min(20, size(valid_params, 1));
    for i = 1:display_count
        fprintf('%-8d %-10d %-15.2e\n', valid_params(i, 1), valid_params(i, 2), valid_params(i, 3));
    end
    
    if size(valid_params, 1) > 20
        fprintf('... 还有 %d 个有效组合\n', size(valid_params, 1) - 20);
    end
    
    % 分析有效参数的分布
    fprintf('\n有效参数分布分析:\n');
    
    % n_atoms分布
    unique_n_atoms = unique(valid_params(:, 1));
    fprintf('  n_atoms有效值: %s\n', mat2str(unique_n_atoms));
    
    % sparsity分布
    unique_sparsity = unique(valid_params(:, 2));
    fprintf('  sparsity有效值: %s\n', mat2str(unique_sparsity));
    
    % lambdaProtect分布
    lambda_min = min(valid_params(:, 3));
    lambda_max = max(valid_params(:, 3));
    fprintf('  lambdaProtect有效范围: [%.2e, %.2e]\n', lambda_min, lambda_max);
    
else
    fprintf('\n❌ 未找到任何有效的参数组合！\n');
    fprintf('建议:\n');
    fprintf('  1. 扩大搜索范围\n');
    fprintf('  2. 检查数据质量\n');
    fprintf('  3. 检查算法实现\n');
end

%% 错误类型分析
if ~isempty(search_log)
    fprintf('\n错误类型分析:\n');
    error_types = {};
    error_counts = [];
    
    for i = 1:length(search_log)
        if ~search_log(i).is_valid
            error_type = search_log(i).error_info;
            idx = find(strcmp(error_types, error_type));
            if isempty(idx)
                error_types{end+1} = error_type;
                error_counts(end+1) = 1;
            else
                error_counts(idx) = error_counts(idx) + 1;
            end
        end
    end
    
    [error_counts, sort_idx] = sort(error_counts, 'descend');
    error_types = error_types(sort_idx);
    
    for i = 1:length(error_types)
        fprintf('  %s: %d次 (%.1f%%)\n', error_types{i}, error_counts(i), ...
                100*error_counts(i)/size(invalid_params, 1));
    end
end

%% 保存结果
results = struct();
results.search_params = struct('n_atoms_range', n_atoms_range, ...
                              'sparsity_range', sparsity_range, ...
                              'lambdaProtect_range', lambdaProtect_range);
results.valid_params = valid_params;
results.invalid_params = invalid_params;
results.search_log = search_log;
results.total_time = total_time;
results.total_combinations = total_combinations;

save('gildl_cuihua_parameter_search_results.mat', 'results');
fprintf('\n搜索结果已保存到: gildl_cuihua_parameter_search_results.mat\n');

%% 推荐参数
if size(valid_params, 1) > 0
    fprintf('\n========== 推荐参数 ==========\n');
    
    % 选择中等大小的参数作为推荐
    mid_idx = ceil(size(valid_params, 1) / 2);
    recommended = valid_params(mid_idx, :);
    
    fprintf('推荐参数组合 (中位数选择):\n');
    fprintf('  n_atoms = %d\n', recommended(1));
    fprintf('  sparsity = %d\n', recommended(2));
    fprintf('  lambdaProtect = %.2e\n', recommended(3));
    
    fprintf('\n使用方法:\n');
    fprintf('在 cuihua_learn_D.m 中设置:\n');
    fprintf('  n_atoms = %d;\n', recommended(1));
    fprintf('  sparsity = %d;\n', recommended(2));
    fprintf('  lambdaProtect = %.2e;\n', recommended(3));
end

fprintf('\n🎉 参数网格搜索完成！\n');

%% 辅助函数：测试GILDL参数
function [is_valid, error_info] = test_gildl_parameters(data_modes, n_atoms, sparsity, lambdaProtect)
    try
        % 固定参数
        all_modes = 3;
        iter_KSVD = 10;      % 减少迭代次数以加快搜索
        iter_gildl = 5;      % 减少迭代次数以加快搜索
        eps_norm = 1e-6;
        
        % 初始化
        Dictionary_history = cell(all_modes, 1);
        Ws = zeros(n_atoms);
        
        % Mode 1: K-SVD
        Y1 = data_modes{1};
        [m, ~] = size(Y1);
        
        % 检查维度兼容性
        if n_atoms > m
            is_valid = false;
            error_info = 'n_atoms > data_dimension';
            return;
        end
        
        % 随机初始化字典
        D0 = randn(m, n_atoms);
        D0 = D0 ./ vecnorm(D0);
        
        % 检查初始化
        if any(isnan(D0(:))) || any(isinf(D0(:)))
            is_valid = false;
            error_info = 'initial_dictionary_invalid';
            return;
        end
        
        % K-SVD训练
        [Dict1, ~] = ksvd_simple(Y1, D0, sparsity, iter_KSVD);
        
        % 检查K-SVD结果
        if any(isnan(Dict1(:))) || any(isinf(Dict1(:)))
            is_valid = false;
            error_info = 'ksvd_result_nan';
            return;
        end
        
        Dictionary_history{1} = Dict1;
        D_prev = Dict1;
        
        % Mode 2-3: GILDL
        for s = 2:all_modes
            Y_new = data_modes{s};
            [m, N] = size(Y_new);
            
            D = D_prev;
            omega_s = zeros(n_atoms, 1);
            
            for it = 1:iter_gildl
                % OMP编码
                X = zeros(n_atoms, N);
                for j = 1:N
                    x_j = omp(D, Y_new(:, j), sparsity);
                    if any(isnan(x_j)) || any(isinf(x_j))
                        is_valid = false;
                        error_info = 'omp_result_nan';
                        return;
                    end
                    X(:, j) = x_j;
                end
                
                % 闭式更新
                D_old = D;
                numerator = D_prev * Ws + Y_new * X';
                denominator = X * X' + Ws + eps_norm * eye(n_atoms);
                
                % 检查分母是否奇异
                if rcond(denominator) < 1e-12
                    is_valid = false;
                    error_info = 'singular_denominator';
                    return;
                end
                
                D = numerator / denominator;
                
                % 检查更新结果
                if any(isnan(D(:))) || any(isinf(D(:)))
                    is_valid = false;
                    error_info = 'dictionary_update_nan';
                    return;
                end
                
                % 归一化
                D = D ./ vecnorm(D);
                
                % 再次检查归一化结果
                if any(isnan(D(:))) || any(isinf(D(:)))
                    is_valid = false;
                    error_info = 'normalization_nan';
                    return;
                end
                
                % 计算损失差
                L_before = norm(Y_new - D_old * X, 'fro')^2;
                L_after = norm(Y_new - D * X, 'fro')^2;
                deltaL = L_before - L_after;
                omega_s = omega_s + deltaL;
            end
            
            % 计算w_tilde
            norm_diff = vecnorm(D - D_prev).^2 + eps_norm;
            w_tilde = omega_s ./ norm_diff;
            
            % 检查w_tilde
            if any(isnan(w_tilde)) || any(isinf(w_tilde))
                is_valid = false;
                error_info = 'w_tilde_nan';
                return;
            end
            
            % 权重更新
            Ws = Ws + lambdaProtect * diag(w_tilde);
            
            % 检查权重矩阵
            if any(isnan(Ws(:))) || any(isinf(Ws(:)))
                is_valid = false;
                error_info = 'weight_matrix_nan';
                return;
            end
            
            Dictionary_history{s} = D;
            D_prev = D;
        end
        
        % 如果所有检查都通过
        is_valid = true;
        error_info = 'success';
        
    catch ME
        is_valid = false;
        error_info = ['exception_' ME.identifier];
    end
end
