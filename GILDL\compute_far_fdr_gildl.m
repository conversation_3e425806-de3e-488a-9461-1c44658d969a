function [FAR_overall, FDR_overall, FAR_vec, FDR_vec, R_test, R_limit] = compute_far_fdr_gildl(D_K)
%% 使用num_monitoring_exp.m中的方法计算FAR和FDR
% 输入: D_K - 字典矩阵
% 输出: FAR_overall, FDR_overall - 总体FAR和FDR
%       FAR_vec, FDR_vec - 各模式的FAR和FDR
%       R_test, R_limit - 测试统计量和控制限

sparsity = 2;  % 与num_monitoring_exp.m中相同

%% ========== 1. 拼接1~5 mode训练/测试数据 (按照num_monitoring_exp.m) ==========
Y_train = [];
Y_test = [];

% 加载训练数据
for mm = 1:5
    train_file = sprintf('mode%d_train.mat', mm);
    if ~exist(train_file, 'file')
        train_file = sprintf('../SVD_DL/mode%d_train.mat', mm);
    end
    load(train_file);
    Y_train = [Y_train; train_data];
end

% 加载测试数据 (按照num_monitoring_exp.m的顺序)
for mm = 1:5
    normal_file = sprintf('mode%d_test_normal.mat', mm);
    fault_file = sprintf('mode%d_test_fault.mat', mm);
    
    if ~exist(normal_file, 'file')
        normal_file = sprintf('../SVD_DL/mode%d_test_normal.mat', mm);
        fault_file = sprintf('../SVD_DL/mode%d_test_fault.mat', mm);
    end
    
    load(normal_file);
    load(fault_file);
    Y_test = [Y_test; test_normal_data; test_fault_data];
end

Y_train = Y_train';  % [特征, 总样本数]
Y_test = Y_test';

%% ========== 2. 训练数据：OMP编码+R统计量 (完全按照num_monitoring_exp.m) ==========
R_train = zeros(1, size(Y_train,2));
for i = 1:size(Y_train,2)
    y = Y_train(:,i);
    x = omp(D_K, y, sparsity);
    R_train(i) = norm(y - D_K*x, 2)^2;
end

% 使用KDE估计统计量的分布并计算控制限
[f_R, xi_R] = ksdensity(R_train, 'Function', 'cdf');
% 找到 1-alpha 分位数
idx_R = find(f_R >= 1 - 0.01, 1, 'first');
R_limit = xi_R(idx_R);

%% ========== 3. 测试集：OMP编码+R统计量 (完全按照num_monitoring_exp.m) ==========
R_test = zeros(1, size(Y_test,2));
for i = 1:size(Y_test,2)
    y = Y_test(:,i);
    x = omp(D_K, y, sparsity);
    R_test(i) = norm(y - D_K*x, 2)^2;
end

%% ========== 4. 计算FAR/FDR (完全按照num_monitoring_exp.m) ==========
n_mode = 5;
n_each = 1000;     % 每个mode 1000个测试样本
n_normal = 500;    % 每mode前500正常
n_fault = 500;     % 每mode后500故障

FAR_vec = zeros(n_mode, 1);
FDR_vec = zeros(n_mode, 1);

for m = 1:n_mode
    idx_start = (m-1)*n_each + 1;
    idx_normal = idx_start : idx_start + n_normal - 1;
    idx_fault  = idx_start + n_normal : idx_start + n_each - 1;
    
    FAR_vec(m) = sum(R_test(idx_normal) > R_limit) / n_normal;
    FDR_vec(m) = sum(R_test(idx_fault)  > R_limit) / n_fault;
end

FAR_overall = mean(FAR_vec);
FDR_overall = mean(FDR_vec);

end
