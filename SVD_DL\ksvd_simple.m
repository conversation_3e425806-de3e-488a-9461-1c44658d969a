function [D, X] = ksvd_simple(Y, D_init, sparsity, n_iter)
% Y: [m x N]，观测数据
% D_init: [m x K]，初始字典
% sparsity: 稀疏度
% n_iter: 迭代次数
[m, N] = size(Y);
K = size(D_init,2);
D = D_init;

for iter = 1:n_iter
    % === 1. 稀疏编码（OMP） ===
    X = zeros(K,N);
    for n = 1:N
        X(:,n) = omp(D, Y(:,n), sparsity);
    end

    % === 2. 字典更新 ===
    for k = 1:K
        omega = find(X(k,:) ~= 0);
        if isempty(omega), continue; end
        R = Y(:,omega) - D*X(:,omega) + D(:,k)*X(k,omega);
        [U, S, V] = svd(R, 'econ');
        D(:,k) = U(:,1);
        X(k,omega) = S(1,1) * V(:,1)';
    end
    % 单位范数归一化
    for k = 1:K
        D(:,k) = D(:,k) / norm(D(:,k));
    end

    if mod(iter,5)==0
        fprintf('K-SVD迭代 %d/%d 完成\n', iter, n_iter);
    end
end
end

