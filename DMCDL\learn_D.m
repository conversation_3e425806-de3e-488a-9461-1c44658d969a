%=========================================================
%  DM‑CDL  ·  Offline Modeling  ·  Modes 1‑5
%  训练完成后保存:
%    D_final    字典
%    W_final    参数记忆矩阵
%    Yh_final   样本记忆池
%    Rtr_final  重构误差控制限
%=========================================================
clear; clc; rng(42);

%% ---------- 超参数 ----------
n_atoms   = 20;        % 字典原子数
sparsity  = 2;         % OMP 稀疏度
n_iter    = 50;        % K‑SVD 迭代
lambda_x  = 1e-3;      % 稀疏惩罚
lambda_1  = 1e-6;      % 初始正则系数
zeta      = 1e-12;     % 数值稳定
M         = 10;        % 重放样本容量 (代表+判别)

%% ---------- 读取 Mode‑1 数据 ----------
load mode1_train.mat   % 需包含变量 train_data
Y = train_data';       % [m × N]

%% ---------- 1) 初始建模 ----------
[D, X, w, W] = dmcdl_initial(Y, n_atoms, sparsity, n_iter, lambda_1, zeta);

% ---------- 阈值 & 样本记忆池 ----------
[Rtr, Yh] = build_threshold_and_memory(D, X, Y, M);

%% ---------- 2) 增量处理 Mode‑2…5 ----------
for k = 2:5
    fprintf('\n=== Incremental Mode %d ===\n', k);
    % 读取新模式
    fname = sprintf('mode%d_train.mat', k);
    load(fname);                      % 得到 train_data
    Ynew = train_data';
    
    % 双重记忆拼接
    Y_concat = [Ynew, Yh];
    
    % 交替优化 (式 17‑23)
    [D, X, W, w] = dmcdl_incremental(D, W, Y_concat, ...
                     n_atoms, sparsity, n_iter, lambda_x, zeta, w);
    
    % 更新阈值 & 样本记忆池
    [Rtr, Yh] = build_threshold_and_memory(D, X, Y_concat, M);
end

%% ---------- 保存模型 ----------
D_final  = D;
W_final  = W;
Yh_final = Yh;
Rtr_final= Rtr;
save dmcdl_model.mat D_final W_final Yh_final Rtr_final
fprintf('\n模型保存至  dmcdl_model.mat\n');
