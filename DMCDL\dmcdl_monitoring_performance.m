%% DMCDL监测性能分析
% 仿照SVD_DL的num_monitoring_exp.m分析DMCDL的过程监测性能

clc; clear; close all;
rng(42);

fprintf('========== DMCDL监测性能分析 ==========\n');
fprintf('使用DMCDL最终字典进行过程监测性能评估\n\n');

%% 1. 加载DMCDL最终字典
fprintf('1. 加载DMCDL最终字典...\n');

% 检查是否存在DMCDL模型
if exist('dmcdl_model.mat', 'file')
    load('dmcdl_model.mat', 'D_final');
    D_K = D_final;
    fprintf('   ✓ 成功加载DMCDL最终字典\n');
    fprintf('   字典大小: %dx%d\n', size(D_K, 1), size(D_K, 2));
elseif exist('dmcdl_evolution_results.mat', 'file')
    load('dmcdl_evolution_results.mat', 'Dictionary_history');
    D_K = Dictionary_history{end};
    fprintf('   ✓ 从演化结果加载最终字典\n');
    fprintf('   字典大小: %dx%d\n', size(D_K, 1), size(D_K, 2));
else
    fprintf('   ❌ 未找到DMCDL字典，开始训练...\n');
    % 运行DMCDL训练
    learn_D;
    load('dmcdl_model.mat', 'D_final');
    D_K = D_final;
    fprintf('   ✓ DMCDL训练完成，字典大小: %dx%d\n', size(D_K, 1), size(D_K, 2));
end

sparsity = 2;  % 稀疏度设置

%% 2. 拼接1~5模式训练/测试数据
fprintf('\n2. 加载训练和测试数据...\n');

Y_train = [];
Y_test = [];

% 加载训练数据
for mode = 1:5
    load(sprintf('mode%d_train.mat', mode));
    Y_train = [Y_train; train_data];
    fprintf('   Mode %d训练数据: %d样本\n', mode, size(train_data, 1));
end

% 加载测试数据（正常+故障）
for mode = 1:5
    load(sprintf('mode%d_test_normal.mat', mode));
    load(sprintf('mode%d_test_fault.mat', mode));
    Y_test = [Y_test; test_normal_data; test_fault_data];
    fprintf('   Mode %d测试数据: %d正常 + %d故障\n', mode, ...
            size(test_normal_data, 1), size(test_fault_data, 1));
end

Y_train = Y_train';  % [特征维度, 总样本数]
Y_test = Y_test';

fprintf('   总训练数据: %dx%d\n', size(Y_train, 1), size(Y_train, 2));
fprintf('   总测试数据: %dx%d\n', size(Y_test, 1), size(Y_test, 2));

%% 3. 训练数据：OMP编码+R统计量
fprintf('\n3. 计算训练数据R统计量...\n');

R_train = zeros(1, size(Y_train, 2));
for i = 1:size(Y_train, 2)
    y = Y_train(:, i);
    x = omp(D_K, y, sparsity);
    R_train(i) = norm(y - D_K*x, 2)^2;
end

% 使用KDE估计统计量的分布并计算控制限
alpha = 0.01;  % 99%置信度
[f_R, xi_R] = ksdensity(R_train, 'Function', 'cdf');
idx_R = find(f_R >= 1 - alpha, 1, 'first');
R_limit = xi_R(idx_R);

fprintf('   训练样本数: %d\n', length(R_train));
fprintf('   R统计量范围: [%.6f, %.6f]\n', min(R_train), max(R_train));
fprintf('   控制限 (99%%置信度): %.6f\n', R_limit);

%% 4. 测试集：OMP编码+R统计量
fprintf('\n4. 计算测试数据R统计量...\n');

R_test = zeros(1, size(Y_test, 2));
for i = 1:size(Y_test, 2)
    y = Y_test(:, i);
    x = omp(D_K, y, sparsity);
    R_test(i) = norm(y - D_K*x, 2)^2;
end

fprintf('   测试样本数: %d\n', length(R_test));
fprintf('   R统计量范围: [%.6f, %.6f]\n', min(R_test), max(R_test));

%% 5. 可视化统计量与控制限
fprintf('\n5. 生成监测图表...\n');

figure('Position', [100, 100, 1200, 800]);

% 主监测图
subplot(2,2,[1,2]);
plot(R_test, 'b-', 'LineWidth', 1);
hold on;
yline(R_limit, '--r', 'LineWidth', 2);

% 标记不同模式的分界线
n_each = 1000;  % 每个模式1000个测试样本
for mode = 1:4
    xline(mode * n_each, '--g', sprintf('Mode%d|Mode%d', mode, mode+1), 'LineWidth', 1.5);
end

xlabel('样本编号', 'FontSize', 12);
ylabel('R统计量', 'FontSize', 12);
title('DMCDL过程监测 - R统计量与控制限', 'FontSize', 14);
legend('测试样本', '控制限 (99%)', 'Location', 'best');
grid on;

% 训练数据分布
subplot(2,2,3);
histogram(R_train, 50, 'Normalization', 'pdf', 'FaceAlpha', 0.7);
hold on;
xline(R_limit, '--r', 'LineWidth', 2);
xlabel('R统计量', 'FontSize', 12);
ylabel('概率密度', 'FontSize', 12);
title('训练数据R统计量分布', 'FontSize', 12);
legend('训练数据', '控制限', 'Location', 'best');
grid on;

% 测试数据分布
subplot(2,2,4);
histogram(R_test, 50, 'Normalization', 'pdf', 'FaceAlpha', 0.7);
hold on;
xline(R_limit, '--r', 'LineWidth', 2);
xlabel('R统计量', 'FontSize', 12);
ylabel('概率密度', 'FontSize', 12);
title('测试数据R统计量分布', 'FontSize', 12);
legend('测试数据', '控制限', 'Location', 'best');
grid on;

sgtitle('DMCDL方法 - 过程监测性能分析', 'FontSize', 16);

% 保存监测图
savefig('dmcdl_monitoring_performance.fig');
fprintf('   监测图已保存到: dmcdl_monitoring_performance.fig\n');

%% 6. 计算FAR和FDR
fprintf('\n6. 计算检测性能指标...\n');

n_mode = 5;
n_each = 1000;     % 每个模式1000个测试样本
n_normal = 500;    % 每模式前500正常
n_fault = 500;     % 每模式后500故障

FAR_all = zeros(n_mode, 1);
FDR_all = zeros(n_mode, 1);

for m = 1:n_mode
    idx_start = (m-1)*n_each + 1;
    idx_normal = idx_start : idx_start + n_normal - 1;
    idx_fault = idx_start + n_normal : idx_start + n_each - 1;
    
    FAR_all(m) = sum(R_test(idx_normal) > R_limit) / n_normal;
    FDR_all(m) = sum(R_test(idx_fault) > R_limit) / n_fault;
    
    fprintf('   Mode %d: FAR=%.4f, FDR=%.4f\n', m, FAR_all(m), FDR_all(m));
end

FAR_overall = mean(FAR_all);
FDR_overall = mean(FDR_all);

%% 7. 与其他方法的比较分析
fprintf('\n7. 与其他方法的比较分析...\n');

% 尝试加载其他方法的结果进行比较
comparison_results = struct();
comparison_results.DMCDL.FAR = FAR_overall;
comparison_results.DMCDL.FDR = FDR_overall;
comparison_results.DMCDL.method_name = 'DMCDL';

% 检查SVD_DL结果
if exist('../SVD_DL/monitoring_results.mat', 'file')
    load('../SVD_DL/monitoring_results.mat');
    if exist('FAR', 'var') && exist('FDR', 'var')
        comparison_results.SVD_DL.FAR = FAR;
        comparison_results.SVD_DL.FDR = FDR;
        comparison_results.SVD_DL.method_name = 'SVD_DL';
        fprintf('   ✓ 加载SVD_DL结果: FAR=%.4f, FDR=%.4f\n', FAR, FDR);
    end
end

% 检查GILDL结果
if exist('../GILDL/monitoring_results.mat', 'file')
    load('../GILDL/monitoring_results.mat');
    if exist('FAR', 'var') && exist('FDR', 'var')
        comparison_results.GILDL.FAR = FAR;
        comparison_results.GILDL.FDR = FDR;
        comparison_results.GILDL.method_name = 'GILDL';
        fprintf('   ✓ 加载GILDL结果: FAR=%.4f, FDR=%.4f\n', FAR, FDR);
    end
end

%% 8. 生成比较图表
if length(fieldnames(comparison_results)) > 1
    fprintf('\n8. 生成方法比较图表...\n');
    
    figure('Position', [150, 150, 1000, 600]);
    
    methods = fieldnames(comparison_results);
    n_methods = length(methods);
    FAR_values = zeros(n_methods, 1);
    FDR_values = zeros(n_methods, 1);
    method_names = cell(n_methods, 1);
    
    for i = 1:n_methods
        method = methods{i};
        FAR_values(i) = comparison_results.(method).FAR;
        FDR_values(i) = comparison_results.(method).FDR;
        method_names{i} = comparison_results.(method).method_name;
    end
    
    % FAR比较
    subplot(1,2,1);
    bar(FAR_values, 'FaceColor', [0.3, 0.6, 0.9]);
    set(gca, 'XTickLabel', method_names);
    ylabel('FAR (误报率)', 'FontSize', 12);
    title('各方法FAR比较 (越小越好)', 'FontSize', 14);
    grid on;
    for i = 1:n_methods
        text(i, FAR_values(i), sprintf('%.4f', FAR_values(i)), ...
             'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom');
    end
    
    % FDR比较
    subplot(1,2,2);
    bar(FDR_values, 'FaceColor', [0.9, 0.6, 0.3]);
    set(gca, 'XTickLabel', method_names);
    ylabel('FDR (检出率)', 'FontSize', 12);
    title('各方法FDR比较 (越大越好)', 'FontSize', 14);
    grid on;
    for i = 1:n_methods
        text(i, FDR_values(i), sprintf('%.4f', FDR_values(i)), ...
             'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom');
    end
    
    sgtitle('字典学习方法监测性能比较', 'FontSize', 16);
    savefig('methods_monitoring_comparison.fig');
    fprintf('   比较图已保存到: methods_monitoring_comparison.fig\n');
end

%% 9. 保存监测结果
fprintf('\n9. 保存监测结果...\n');

dmcdl_monitoring_results = struct();
dmcdl_monitoring_results.method = 'DMCDL';
dmcdl_monitoring_results.dictionary_size = size(D_K);
dmcdl_monitoring_results.sparsity = sparsity;
dmcdl_monitoring_results.R_limit = R_limit;
dmcdl_monitoring_results.R_train = R_train;
dmcdl_monitoring_results.R_test = R_test;
dmcdl_monitoring_results.FAR_all = FAR_all;
dmcdl_monitoring_results.FDR_all = FDR_all;
dmcdl_monitoring_results.FAR_overall = FAR_overall;
dmcdl_monitoring_results.FDR_overall = FDR_overall;
dmcdl_monitoring_results.alpha = alpha;
dmcdl_monitoring_results.comparison_results = comparison_results;

save('dmcdl_monitoring_results.mat', 'dmcdl_monitoring_results');
fprintf('   监测结果已保存到: dmcdl_monitoring_results.mat\n');

%% 10. 结果总结
fprintf('\n========== DMCDL监测性能总结 ==========\n');
fprintf('📊 基本信息:\n');
fprintf('   方法: DMCDL (双重记忆持续字典学习)\n');
fprintf('   字典大小: %dx%d\n', size(D_K));
fprintf('   稀疏度: %d\n', sparsity);
fprintf('   置信度: %.0f%%\n', (1-alpha)*100);

fprintf('\n📈 各模式检测性能:\n');
fprintf('   模式    FAR      FDR\n');
fprintf('   ----   ------   ------\n');
for m = 1:n_mode
    fprintf('   %2d     %.4f   %.4f\n', m, FAR_all(m), FDR_all(m));
end

fprintf('\n🎯 总体性能:\n');
fprintf('   平均FAR = %.4f (误报率，越小越好)\n', FAR_overall);
fprintf('   平均FDR = %.4f (检出率，越大越好)\n', FDR_overall);

% 性能评估
if FAR_overall < 0.05 && FDR_overall > 0.8
    fprintf('   ✅ 检测性能优秀\n');
elseif FAR_overall < 0.1 && FDR_overall > 0.7
    fprintf('   ✅ 检测性能良好\n');
else
    fprintf('   ⚠️  检测性能需要改进\n');
end

% 方法比较
if length(fieldnames(comparison_results)) > 1
    fprintf('\n🔍 方法比较:\n');
    for i = 1:length(method_names)
        method = method_names{i};
        far_val = FAR_values(i);
        fdr_val = FDR_values(i);
        fprintf('   %s: FAR=%.4f, FDR=%.4f\n', method, far_val, fdr_val);
    end
    
    % 找出最佳方法
    [~, best_far_idx] = min(FAR_values);
    [~, best_fdr_idx] = max(FDR_values);
    
    fprintf('\n🏆 性能排名:\n');
    fprintf('   最低FAR: %s (%.4f)\n', method_names{best_far_idx}, FAR_values(best_far_idx));
    fprintf('   最高FDR: %s (%.4f)\n', method_names{best_fdr_idx}, FDR_values(best_fdr_idx));
end

fprintf('\n🎉 DMCDL监测性能分析完成！\n');
