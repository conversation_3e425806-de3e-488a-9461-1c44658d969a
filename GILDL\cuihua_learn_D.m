%======================================================================
%  GI‑LDL 主脚本  (论文最严谨实现 + 稳定数值处理)
%----------------------------------------------------------------------
%  Mode‑1 : K‑SVD(列级 SVD) + 计算 W1
%  Mode‑2...S : OMP + 闭式整体更新 (式24) ‑ 无列 SVD
%  递归权重  Ws = Ws + λ*diag(w_tilde)
%  指标 : NMSC, 主空间最大夹角
%======================================================================

clc; clear; close all;
rng(42);                                % 复现实验

%% ---------------- 参数设置 ----------------
all_modes      = 3;                     % F1,F2,F3
n_atoms        = 54;                    % 字典原子数
sparsity       = 2;                     % OMP 稀疏度
iter_KSVD      = 50;                    % Mode‑1 K‑SVD 迭代
iter_gildl     = 30;                    % 持续学习交替迭代
lambdaProtect  = 1e4;                  % λ_s  (可调)
eps_norm       = 1e-6;                  % 防奇异
zeta           = 1e-2;                  % 论文 ζ
%-------------------------------------------

Dictionary_history = cell(all_modes,1); % 保存每个 mode 字典
Ws   = zeros(n_atoms);                  % W1 先置零

%% ===== Mode‑1 : K‑SVD 训练字典 & 计算 W1 ===========================
load('data_selected_F1.mat');           
Y1 = cell2mat(data_selected)';          % [m × N]
[m,~] = size(Y1);

% 随机初始化并归一化
D0 = randn(m,n_atoms);  D0 = D0 ./ vecnorm(D0);

% K‑SVD (列级 SVD)
[Dict1, ~] = ksvd_simple(Y1,D0,sparsity,iter_KSVD);
Dictionary_history{1} = Dict1;
D_prev = Dict1;

% -------- 计算 W1 ---------------------------------------------------
% 真实实现应在 K‑SVD 内累加 ω_i；这里用“单位贡献”近似
omega_1  = ones(n_atoms,1);               % 简洁做法：假设均等贡献
w_tilde1 = omega_1 ./ (vecnorm(Dict1-D0).^2 + zeta);
Ws = lambdaProtect * diag(w_tilde1);      % W1
fprintf('W1 已计算完成 (diag 最大值 %.2e)\n',max(diag(Ws)));

%% ===== Mode‑2 ... Mode‑S : Lifelong Dictionary Learning ===========
for s = 2:all_modes
    fprintf('\n=== Mode‑%d Lifelong 更新 ===\n',s);
    
    % 读新模式数据
    load(['data_selected_F',num2str(s),'.mat']);
    Y_new = cell2mat(data_selected)';     
    [~,N] = size(Y_new);
    
    D = D_prev;                           % 当前字典
    omega_s = zeros(n_atoms,1);           % ΔL 累加
    
    for it = 1:iter_gildl
        % ---- 1) OMP 编码 ------------------------------------------
        X = zeros(n_atoms,N);
        for j = 1:N,  X(:,j) = omp(D,Y_new(:,j),sparsity);  end
        
        % ---- 2) 闭式整体更新 (式24) -------------------------------
        D_old = D;
        M = X*X' + Ws + eps_norm*eye(n_atoms);  % 防奇异
        D   = (D_prev*Ws + Y_new*X') / M;
        nrm = vecnorm(D);  nrm(nrm<1e-12)=1;    % 防0归一化
        D   = D ./ nrm;
        
        % ---- 3) 整体 ΔL 累加 --------------------------------------
        L_before = norm(Y_new - D_old*X,'fro')^2;
        L_after  = norm(Y_new - D    *X,'fro')^2;
        deltaL   = max(L_before - L_after,0);   % 防微负
        omega_s  = omega_s + deltaL;            % 均匀累加
    end
    
    % ---- 4) w_tilde & 递归 Ws --------------------------------------
    w_tilde = omega_s ./ (vecnorm(D-D_prev).^2 + zeta);
    Ws = Ws + lambdaProtect * diag(w_tilde);    % 论文递归式
    
    % ---- 5) 保存 & 准备下轮 ----------------------------------------
    Dictionary_history{s} = D;
    D_prev = D;
end

%% ============ 可视化字典变化 =====================================
D_first = Dictionary_history{1};
D_last  = Dictionary_history{end};
deltaD  = D_last - D_first;

figure; imagesc(deltaD); colorbar;
xlabel('字典原子'); ylabel('观测维');
title('最终字典 − Mode1 字典 (差分)'); set(gca,'FontSize',12);

figure; imagesc(abs(deltaD)); colorbar;
xlabel('字典原子'); ylabel('观测维');
title('字典原子绝对变化热图'); set(gca,'FontSize',12);

%% ============ NMSC ===============================================
nmsc = mean(deltaD(:).^2 ./ (D_first(:).^2 + eps_norm));
fprintf('\n归一化均方变化度 NMSC = %.4f\n', nmsc);

%% ============ 主空间最大夹角 =====================================
[U1,S1] = svd(D_first,'econ');
[U2,~ ] = svd(D_last ,'econ');

energy_ratio = cumsum(diag(S1).^2)/sum(diag(S1).^2);
r = find(energy_ratio >= 0.9,1,'first');

s = svd(U1(:,1:r)'*U2(:,1:r));
max_angle_rad = max( acos(min(max(s,-1),1)) );
fprintf('主空间维数 r = %d\n', r);
fprintf('主空间最大夹角 = %.4f 弧度 (%.2f°)\n', ...
        max_angle_rad, rad2deg(max_angle_rad));
        
save("Dlast_cuihua_GILDL.mat",'D_last');