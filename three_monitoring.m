% ==== 加载数据 ====
load("DMCDL\DMCDL_R_sta.mat");    DMCDL_R_sta = R_statistics;
load("DMCDL\DMCDL_R_th.mat");     DMCDL_R_th  = Rtr_final;

load("GILDL\GILDL_R_sta.mat");    GILDL_R_sta = R_test;
load("GILDL\GILDL_R_th.mat");     GILDL_R_th  = Rtr;

load("SVD_DL\SVD-DL_R_sta.mat");  SVD_DL_R_sta = R_test_opt;
load("SVD_DL\SVD_DL_R_th.mat");   SVD_DL_R_th  = R_limit_opt;

% ==== 性能指标（百分数，保留两位小数） ====
FAR_DMCDL  = 0.0100;  FDR_DMCDL  = 0.9680;
FAR_GILDL  = 0.0116;  FDR_GILDL  = 0.9428;
FAR_SVD_DL = 0.0084;  FDR_SVD_DL = 1.0000;

% ---- 图 1：DMCDL 统计量与控制限 ----
figure('Name','DMCDL','NumberTitle','off','Position',[100 100 600 400]);
x1 = 1:length(DMCDL_R_sta);
plot(x1, DMCDL_R_sta, '-', 'LineWidth',2); hold on;
yline(DMCDL_R_th, '--', 'LineWidth',2,'Color','r');
hold off;
grid off;
set(gca,'FontName','Times New Roman','FontSize',16,'FontWeight','bold');
xlabel('Samples','FontSize',16,'FontName','Times New Roman','FontWeight','bold');
ylabel('R','FontSize',16,'FontName','Times New Roman','FontWeight','bold');
legend({'Statistics','Control Limit'},'Location','Northeast','FontSize',12);
title(sprintf('DMCDL (FAR=%.2f%%, FDR=%.2f%%)', FAR_DMCDL*100, FDR_DMCDL*100), ...
      'FontSize',18,'FontName','Times New Roman','FontWeight','bold');

% ---- 图 2：GILDL 统计量与控制限 ----
figure('Name','GILDL','NumberTitle','off','Position',[550 100 600 400]);
x2 = 1:length(GILDL_R_sta);
plot(x2, GILDL_R_sta, '-', 'LineWidth',2); hold on;
yline(GILDL_R_th, '--', 'LineWidth',2,'Color','r');
hold off;
grid off;
set(gca,'FontName','Times New Roman','FontSize',16,'FontWeight','bold');
xlabel('Samples','FontSize',16,'FontName','Times New Roman','FontWeight','bold');
ylabel('R','FontSize',16,'FontName','Times New Roman','FontWeight','bold');
legend({'Statistics','Control Limit'},'Location','Northeast','FontSize',12);
title(sprintf('GILDL (FAR=%.2f%%, FDR=%.2f%%)', FAR_GILDL*100, FDR_GILDL*100), ...
      'FontSize',18,'FontName','Times New Roman','FontWeight','bold');

% ---- 图 3：SVD‑DL 统计量与控制限 ----
figure('Name','DGCDL','NumberTitle','off','Position',[1000 100 600 400]);
x3 = 1:length(SVD_DL_R_sta);
plot(x3, SVD_DL_R_sta, '-', 'LineWidth',2); hold on;
yline(SVD_DL_R_th, '--', 'LineWidth',2,'Color','r');
hold off;
grid off;
set(gca,'FontName','Times New Roman','FontSize',16,'FontWeight','bold');
xlabel('Samples','FontSize',16,'FontName','Times New Roman','FontWeight','bold');
ylabel('R','FontSize',16,'FontName','Times New Roman','FontWeight','bold');
legend({'Statistics','Control Limit'},'Location','Northeast','FontSize',12);
title(sprintf('DGCDL (FAR=%.2f%%, FDR=%.2f%%)', FAR_SVD_DL*100, FDR_SVD_DL*100), ...
      'FontSize',18,'FontName','Times New Roman','FontWeight','bold');

%保存三个图为pdf
% 获取当前所有图形句柄
all_figs = findall(0, 'Type', 'figure');

% 遍历并保存每个图形为PDF
for i = 1:length(all_figs)
    fig = all_figs(i);
    fig_name = get(fig, 'Name');
    print(fig, [fig_name, '_monitoring'], '-dpdf', '-bestfit');
end

fprintf('✓ 监测图表已保存为PDF格式\n');
