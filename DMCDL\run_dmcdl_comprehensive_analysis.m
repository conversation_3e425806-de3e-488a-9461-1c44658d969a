%% DMCDL综合分析主脚本
% 仿照SVD_DL方法，对DMCDL进行完整的字典演化和监测性能分析

clc; clear; close all;
rng(42);

fprintf('========== DMCDL综合分析 ==========\n');
fprintf('方法: DMCDL (双重记忆持续字典学习)\n');
fprintf('分析内容: 字典演化 + 监测性能 + 方法比较\n\n');

%% 1. 检查必要文件
fprintf('🔍 检查必要文件...\n');

required_files = {};
for mode = 1:5
    required_files{end+1} = sprintf('mode%d_train.mat', mode);
    required_files{end+1} = sprintf('mode%d_test_normal.mat', mode);
    required_files{end+1} = sprintf('mode%d_test_fault.mat', mode);
end

missing_files = {};
for i = 1:length(required_files)
    if exist(required_files{i}, 'file')
        fprintf('   ✓ %s\n', required_files{i});
    else
        fprintf('   ❌ %s (缺失)\n', required_files{i});
        missing_files{end+1} = required_files{i};
    end
end

if ~isempty(missing_files)
    fprintf('   请确保所有必要的数据文件都在DMCDL目录中。\n');
    return;
end

fprintf('✅ 文件检查完成\n\n');

%% 2. 字典演化分析
fprintf('📊 开始字典演化分析...\n');

% 初始化时间变量
evolution_time = 0;
monitoring_time = 0;

try
    tic;
    analyze_dmcdl_evolution;
    evolution_time = toc;
    fprintf('   ✅ 字典演化分析完成，耗时: %.1f秒\n', evolution_time);
catch ME
    fprintf('   ❌ 字典演化分析失败: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('   错误位置: %s 第%d行\n', ME.stack(1).name, ME.stack(1).line);
    end
    evolution_time = 0;  % 确保变量存在
    return;
end

%% 3. 监测性能分析
fprintf('\n🎯 开始监测性能分析...\n');

try
    tic;
    dmcdl_monitoring_performance;
    monitoring_time = toc;
    fprintf('   ✅ 监测性能分析完成，耗时: %.1f秒\n', monitoring_time);
catch ME
    fprintf('   ❌ 监测性能分析失败: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('   错误位置: %s 第%d行\n', ME.stack(1).name, ME.stack(1).line);
    end
    monitoring_time = 0;  % 确保变量存在
    return;
end

%% 4. 加载并整合分析结果
fprintf('\n📋 整合分析结果...\n');

try
    % 加载字典演化结果
    load('dmcdl_analysis_results.mat', 'dmcdl_analysis_results');
    evolution_results = dmcdl_analysis_results;
    
    % 加载监测性能结果
    load('dmcdl_monitoring_results.mat', 'dmcdl_monitoring_results');
    monitoring_results = dmcdl_monitoring_results;
    
    fprintf('   ✓ 成功加载所有分析结果\n');
    
catch ME
    fprintf('   ❌ 结果加载失败: %s\n', ME.message);
    return;
end

%% 5. 生成综合报告
fprintf('\n📄 生成综合分析报告...\n');

fprintf('\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
fprintf('📊 DMCDL方法综合分析报告\n');
fprintf('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');

%% 5.1 基本信息
fprintf('\n🔧 基本配置:\n');
fprintf('   方法名称: %s\n', evolution_results.method);
fprintf('   字典大小: %dx%d\n', size(evolution_results.Dictionary_history{1}));
fprintf('   模式数量: %d\n', length(evolution_results.Dictionary_history));
fprintf('   稀疏度: %d\n', monitoring_results.sparsity);

%% 5.2 字典演化性能
fprintf('\n📈 字典演化性能:\n');

% NMSC分析
NMSC_history = evolution_results.NMSC_history;
fprintf('   NMSC (归一化均方变化度):\n');
for i = 1:length(NMSC_history)
    fprintf('     Mode %d→%d: %.6f\n', i, i+1, NMSC_history(i));
end
fprintf('     平均值: %.6f\n', mean(NMSC_history));

% 子空间夹角分析
Subspace_angle_history = evolution_results.Subspace_angle_history;
valid_angles = Subspace_angle_history(~isnan(Subspace_angle_history));
if ~isempty(valid_angles)
    fprintf('   主方向子空间夹角:\n');
    for i = 1:length(Subspace_angle_history)
        if ~isnan(Subspace_angle_history(i))
            fprintf('     Mode %d→%d: %.4f弧度 (%.2f°)\n', i, i+1, ...
                    Subspace_angle_history(i), Subspace_angle_history(i)*180/pi);
        end
    end
    fprintf('     平均值: %.4f弧度 (%.2f°)\n', mean(valid_angles), mean(valid_angles)*180/pi);
end

% 主空间维度
subspace_dims = evolution_results.subspace_dims;
fprintf('   主空间维度演化:\n');
for i = 1:length(subspace_dims)
    fprintf('     Mode %d: %d维\n', i, subspace_dims(i));
end

%% 5.3 记忆机制分析
fprintf('\n🧠 记忆机制性能:\n');

Weight_norms = evolution_results.Weight_norms;
Memory_sizes = evolution_results.Memory_sizes;
Memory_diversities = evolution_results.Memory_diversities;

fprintf('   权重矩阵演化:\n');
for i = 1:length(Weight_norms)
    fprintf('     Mode %d: 范数=%.4f\n', i, Weight_norms(i));
end

fprintf('   记忆池演化:\n');
for i = 1:length(Memory_sizes)
    fprintf('     Mode %d: 大小=%d, 多样性=%.4f\n', i, Memory_sizes(i), Memory_diversities(i));
end

%% 5.4 监测性能
fprintf('\n🎯 过程监测性能:\n');

FAR_all = monitoring_results.FAR_all;
FDR_all = monitoring_results.FDR_all;
FAR_overall = monitoring_results.FAR_overall;
FDR_overall = monitoring_results.FDR_overall;

fprintf('   各模式检测性能:\n');
fprintf('     模式    FAR      FDR\n');
fprintf('     ----   ------   ------\n');
for m = 1:length(FAR_all)
    fprintf('     %2d     %.4f   %.4f\n', m, FAR_all(m), FDR_all(m));
end

fprintf('   总体性能:\n');
fprintf('     平均FAR: %.4f (误报率，越小越好)\n', FAR_overall);
fprintf('     平均FDR: %.4f (检出率，越大越好)\n', FDR_overall);

%% 5.5 性能评估
fprintf('\n⭐ 性能评估:\n');

% 字典稳定性评估
avg_nmsc = mean(NMSC_history);
if avg_nmsc < 0.1
    fprintf('   字典稳定性: 优秀 (NMSC=%.6f)\n', avg_nmsc);
elseif avg_nmsc < 0.5
    fprintf('   字典稳定性: 良好 (NMSC=%.6f)\n', avg_nmsc);
else
    fprintf('   字典稳定性: 一般 (NMSC=%.6f)\n', avg_nmsc);
end

% 监测性能评估
if FAR_overall < 0.05 && FDR_overall > 0.8
    fprintf('   监测性能: 优秀 (FAR=%.4f, FDR=%.4f)\n', FAR_overall, FDR_overall);
elseif FAR_overall < 0.1 && FDR_overall > 0.7
    fprintf('   监测性能: 良好 (FAR=%.4f, FDR=%.4f)\n', FAR_overall, FDR_overall);
else
    fprintf('   监测性能: 需改进 (FAR=%.4f, FDR=%.4f)\n', FAR_overall, FDR_overall);
end

% 记忆机制评估
final_memory_size = Memory_sizes(end);
avg_diversity = mean(Memory_diversities);
if final_memory_size >= 10 && avg_diversity > 0.1
    fprintf('   记忆机制: 有效 (大小=%d, 多样性=%.4f)\n', final_memory_size, avg_diversity);
else
    fprintf('   记忆机制: 需优化 (大小=%d, 多样性=%.4f)\n', final_memory_size, avg_diversity);
end

%% 5.6 方法比较
if isfield(monitoring_results, 'comparison_results') && ...
   length(fieldnames(monitoring_results.comparison_results)) > 1
    
    fprintf('\n🔍 方法比较:\n');
    comparison = monitoring_results.comparison_results;
    methods = fieldnames(comparison);
    
    fprintf('     方法      FAR      FDR\n');
    fprintf('     ------   ------   ------\n');
    for i = 1:length(methods)
        method = methods{i};
        far_val = comparison.(method).FAR;
        fdr_val = comparison.(method).FDR;
        fprintf('     %-8s %.4f   %.4f\n', method, far_val, fdr_val);
    end
    
    % 排名分析
    FAR_values = arrayfun(@(i) comparison.(methods{i}).FAR, 1:length(methods));
    FDR_values = arrayfun(@(i) comparison.(methods{i}).FDR, 1:length(methods));
    
    [~, best_far_idx] = min(FAR_values);
    [~, best_fdr_idx] = max(FDR_values);
    
    fprintf('\n   🏆 性能排名:\n');
    fprintf('     最低FAR: %s (%.4f)\n', methods{best_far_idx}, FAR_values(best_far_idx));
    fprintf('     最高FDR: %s (%.4f)\n', methods{best_fdr_idx}, FDR_values(best_fdr_idx));
end

%% 6. 保存综合结果
fprintf('\n💾 保存综合分析结果...\n');

% 确保时间变量存在
if ~exist('evolution_time', 'var')
    evolution_time = 0;
    fprintf('   警告: evolution_time未定义，设为0\n');
end

if ~exist('monitoring_time', 'var')
    monitoring_time = 0;
    fprintf('   警告: monitoring_time未定义，设为0\n');
end

comprehensive_results = struct();
comprehensive_results.method = 'DMCDL_Comprehensive';

% 安全地添加结果
if exist('evolution_results', 'var')
    comprehensive_results.evolution_results = evolution_results;
else
    fprintf('   警告: evolution_results未找到\n');
    comprehensive_results.evolution_results = struct();
end

if exist('monitoring_results', 'var')
    comprehensive_results.monitoring_results = monitoring_results;
else
    fprintf('   警告: monitoring_results未找到\n');
    comprehensive_results.monitoring_results = struct();
end

comprehensive_results.analysis_time = struct('evolution_time', evolution_time, ...
                                            'monitoring_time', monitoring_time, ...
                                            'total_time', evolution_time + monitoring_time);

save('dmcdl_comprehensive_results.mat', 'comprehensive_results');
fprintf('   综合结果已保存到: dmcdl_comprehensive_results.mat\n');

%% 7. 输出文件总结
fprintf('\n📁 生成的文件总结:\n');

output_files = {
    'dmcdl_evolution_results.mat', '字典演化数据';
    'dmcdl_analysis_results.mat', '演化分析结果';
    'dmcdl_evolution_analysis.fig', '演化分析图表';
    'dmcdl_monitoring_results.mat', '监测性能数据';
    'dmcdl_monitoring_performance.fig', '监测性能图表';
    'dmcdl_comprehensive_results.mat', '综合分析结果';
    'methods_monitoring_comparison.fig', '方法比较图表'
};

fprintf('   生成的文件:\n');
for i = 1:size(output_files, 1)
    filename = output_files{i, 1};
    description = output_files{i, 2};
    
    if exist(filename, 'file')
        file_info = dir(filename);
        fprintf('     ✓ %s (%.1f KB) - %s\n', filename, file_info.bytes/1024, description);
    else
        fprintf('     ❌ %s - %s (未生成)\n', filename, description);
    end
end

%% 8. 使用建议
fprintf('\n💡 使用建议:\n');
fprintf('   1. 查看演化分析: open(''dmcdl_evolution_analysis.fig'')\n');
fprintf('   2. 查看监测性能: open(''dmcdl_monitoring_performance.fig'')\n');
fprintf('   3. 查看方法比较: open(''methods_monitoring_comparison.fig'')\n');
fprintf('   4. 加载详细数据: load(''dmcdl_comprehensive_results.mat'')\n');
fprintf('   5. 参数调优: 修改learn_D.m中的超参数\n');

fprintf('\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
fprintf('🎉 DMCDL综合分析完成！\n');

% 安全地计算总耗时
total_analysis_time = 0;
if exist('evolution_time', 'var') && exist('monitoring_time', 'var')
    total_analysis_time = evolution_time + monitoring_time;
end

if total_analysis_time > 0
    fprintf('总耗时: %.1f分钟\n', total_analysis_time/60);
else
    fprintf('总耗时: 未记录\n');
end

fprintf('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
