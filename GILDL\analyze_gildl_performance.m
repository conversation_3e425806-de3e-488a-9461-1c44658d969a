%% GILDL方法性能分析 - 使用与SVD_DL相同的性能检测方法
% 基于learn_D.m中的GILDL字典学习方法
% 计算NMSC、主方向子空间夹角、FAR、FDR等性能指标

rng(42);

fprintf('========== GILDL方法性能分析开始 ==========\n');
fprintf('使用与SVD_DL相同的性能检测方法\n');
fprintf('字典学习方法: GILDL (learn_D.m)\n\n');

%% ========== 1. 使用GILDL方法训练字典序列 ==========
fprintf('1. 使用GILDL方法训练字典序列...\n');

% 存储字典历史和主空间历史
Dictionary_history = cell(5,1);
U_locked_history = cell(5,1);
Principal_subspace_history = cell(5,1);
NMSC_history = zeros(4,1);
Subspace_angle_history = zeros(4,1);

% 训练每个模式的字典
for mode = 1:5
    fprintf('   训练Mode %d字典...\n', mode);
    
    % 加载训练数据
    fname = sprintf('mode%d_train.mat', mode);
    if ~exist(fname, 'file')
        % 尝试从SVD_DL目录加载
        fname_svd = sprintf('../SVD_DL/mode%d_train.mat', mode);
        if exist(fname_svd, 'file')
            load(fname_svd);
        else
            error('找不到训练数据文件: %s', fname);
        end
    else
        load(fname);
    end
    
    Y = train_data';  % [8 x 1000]
    
    % 调用GILDL的learn_D_GILDL函数进行字典学习
    % 注意：不修改learn_D_GILDL.m中的参数，保持原有设置
    try
        if mode == 1
            % 对于第一个模式，使用完整的GILDL学习过程
            fprintf('   使用GILDL完整学习过程...\n');
            [Dict_history, ~, ~, Dictionary] = learn_D_GILDL();

            % 如果返回了多个字典，使用对应模式的字典
            if iscell(Dict_history) && length(Dict_history) >= mode
                Dictionary = Dict_history{mode};
            end
        else
            % 对于其他模式，使用单模式学习
            fprintf('   使用单模式GILDL学习...\n');
            [~, ~, ~, Dictionary] = learn_D_GILDL(Y);
        end

    catch ME
        fprintf('   警告: learn_D_GILDL函数调用失败: %s\n', ME.message);
        fprintf('   使用备用方法生成字典\n');

        % 备用方法：简单的随机字典
        n_atoms = min(20, size(Y, 1));  % 使用与特征维度相同的原子数，最多20个
        Dictionary = randn(size(Y,1), n_atoms);
        for k = 1:n_atoms
            Dictionary(:,k) = Dictionary(:,k) / norm(Dictionary(:,k));
        end
    end
    
    % 计算主空间
    [U, S, V] = svd(Dictionary, 'econ');
    singular_values = diag(S);
    energy = cumsum(singular_values.^2) / sum(singular_values.^2);
    k_locked = find(energy >= 0.9, 1, 'first');
    if isempty(k_locked)
        k_locked = min(3, size(U,2));
    end
    U_locked = U(:, 1:k_locked);
    
    % 存储结果
    Dictionary_history{mode} = Dictionary;
    U_locked_history{mode} = U_locked;
    Principal_subspace_history{mode} = U_locked;
    
    fprintf('     字典大小: %dx%d, 主空间维度: %d\n', ...
            size(Dictionary,1), size(Dictionary,2), k_locked);
    
    % 计算与前一个模式的NMSC和子空间夹角
    if mode > 1
        D_prev = Dictionary_history{mode-1};
        U_prev = U_locked_history{mode-1};
        
        % 计算NMSC (归一化均方变化度)
        epsilon = 1e-8;
        num = (Dictionary - D_prev).^2;
        den = D_prev.^2 + epsilon;
        NMSC = mean(num(:) ./ den(:));
        NMSC_history(mode-1) = NMSC;
        
        % 计算主方向子空间夹角
        min_dim = min(size(U_prev,2), size(U_locked,2));
        if min_dim > 0
            U_prev_trunc = U_prev(:, 1:min_dim);
            U_curr_trunc = U_locked(:, 1:min_dim);
            try
                subspace_angle = subspace(U_prev_trunc, U_curr_trunc);
                Subspace_angle_history(mode-1) = subspace_angle;
            catch
                Subspace_angle_history(mode-1) = NaN;
            end
        else
            Subspace_angle_history(mode-1) = NaN;
        end
        
        fprintf('     NMSC: %.4f, 子空间夹角: %.4f弧度 (%.2f°)\n', ...
                NMSC, Subspace_angle_history(mode-1), Subspace_angle_history(mode-1)*180/pi);
    end
end

%% ========== 2. 性能评估 - 使用num_monitoring_exp.m中的方法 ==========
fprintf('\n2. 性能评估 (使用num_monitoring_exp.m的FAR/FDR计算方法)...\n');

% 使用最后训练的字典进行性能评估
D_K = Dictionary_history{end};

% 检查OMP函数
if ~exist('omp', 'file')
    addpath('../SVD_DL');
    if ~exist('omp', 'file')
        error('未找到OMP函数，请确保omp.m在路径中');
    end
end

% 调用专门的FAR/FDR计算函数 (完全按照num_monitoring_exp.m的实现)
fprintf('   使用compute_far_fdr_gildl函数计算性能指标...\n');
[FAR_overall, FDR_overall, FAR_vec, FDR_vec, R_test, R_limit] = compute_far_fdr_gildl(D_K);

fprintf('   控制限: %.6f\n', R_limit);
fprintf('   测试统计量范围: [%.6f, %.6f]\n', min(R_test), max(R_test));

%% ========== 3. 结果分析和显示 ==========
fprintf('\n========== GILDL方法分析结果 ==========\n');

% 显示NMSC历史
fprintf('📊 NMSC (归一化均方变化度) 历史:\n');
for i = 1:4
    fprintf('   Mode %d→%d: %.4f\n', i, i+1, NMSC_history(i));
end
fprintf('   平均NMSC: %.4f\n', mean(NMSC_history));

% 显示子空间夹角历史
fprintf('\n📐 主方向子空间夹角历史:\n');
valid_angles = Subspace_angle_history(~isnan(Subspace_angle_history));
for i = 1:4
    if ~isnan(Subspace_angle_history(i))
        fprintf('   Mode %d→%d: %.4f弧度 (%.2f°)\n', i, i+1, ...
                Subspace_angle_history(i), Subspace_angle_history(i)*180/pi);
    else
        fprintf('   Mode %d→%d: 无法计算\n', i, i+1);
    end
end
if ~isempty(valid_angles)
    fprintf('   平均子空间夹角: %.4f弧度 (%.2f°)\n', mean(valid_angles), mean(valid_angles)*180/pi);
end

% 显示主空间维度
subspace_dims = cellfun(@(x) size(x,2), U_locked_history);
fprintf('\n🔍 主空间维度演化:\n');
for i = 1:5
    fprintf('   Mode %d: %d维\n', i, subspace_dims(i));
end
n_mode=5;
% 显示性能指标
fprintf('\n🎯 性能指标 (FAR/FDR):\n');
for m = 1:n_mode
    fprintf('   Mode %d: FAR=%.4f, FDR=%.4f\n', m, FAR_vec(m), FDR_vec(m));
end
fprintf('   总体性能: FAR=%.4f, FDR=%.4f\n', FAR_overall, FDR_overall);

%% ========== 4. 保存结果 ==========
results_gildl = struct();
results_gildl.method = 'GILDL';
results_gildl.NMSC_history = NMSC_history;
results_gildl.Subspace_angle_history = Subspace_angle_history;
results_gildl.Dictionary_history = Dictionary_history;
results_gildl.U_locked_history = U_locked_history;
results_gildl.Principal_subspace_history = Principal_subspace_history;
results_gildl.subspace_dims = subspace_dims;
results_gildl.FAR_overall = FAR_overall;
results_gildl.FDR_overall = FDR_overall;
results_gildl.FAR_vec = FAR_vec;
results_gildl.FDR_vec = FDR_vec;
results_gildl.R_test = R_test;
results_gildl.R_limit = R_limit;

save('gildl_performance_analysis.mat', 'results_gildl');

fprintf('\n💾 GILDL分析结果已保存到 gildl_performance_analysis.mat\n');
fprintf('🎉 GILDL方法性能分析完成！\n');
