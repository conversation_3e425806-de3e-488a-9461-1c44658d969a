%% 四种字典学习方法雷达图
% 单独创建雷达图以避免subplot限制

clc; clear; close all;

fprintf('========== 生成方法对比雷达图 ==========\n');

%% 数据准备
methods = {'GILDL', 'DMCDL', 'SVD-DL', 'JMSDL'};
colors = [0.2, 0.6, 0.8;    % GILDL - 蓝色
          0.8, 0.4, 0.2;    % DMCDL - 橙色  
          0.4, 0.8, 0.3;    % SVD-DL - 绿色
          0.9, 0.6, 0.9];   % JMSDL - 紫色

% 原始性能数据
nmsc_values = [0.150, 0.120, 0.250, 10.000];
far_values = [0.025, 0.018, 0.035, 0.040];
fdr_values = [0.920, 0.950, 0.880, 0.850];
time_values = [45, 65, 30, 50];

%% 创建雷达图
figure('Position', [100, 100, 800, 800]);

% 归一化数据用于雷达图 (0-1范围)
nmsc_norm = 1 - (nmsc_values - min(nmsc_values)) / (max(nmsc_values) - min(nmsc_values));
far_norm = 1 - far_values / max(far_values);  % FAR越小越好，所以取反
fdr_norm = fdr_values / max(fdr_values);      % FDR越大越好
time_norm = 1 - (time_values - min(time_values)) / (max(time_values) - min(time_values)); % 时间越短越好

% 雷达图数据矩阵
radar_data = [nmsc_norm; far_norm; fdr_norm; time_norm]';
radar_labels = {'字典稳定性', '误报率性能', '检出率性能', '计算效率'};

% 角度设置
n_metrics = length(radar_labels);
angles = linspace(0, 2*pi, n_metrics+1);

% 绘制雷达图
hold on;

% 绘制网格线
for r = 0.2:0.2:1.0
    polarplot(angles, ones(size(angles))*r, '--', 'Color', [0.7, 0.7, 0.7], 'LineWidth', 0.5);
end

% 绘制角度线
for i = 1:n_metrics
    polarplot([angles(i), angles(i)], [0, 1], '--', 'Color', [0.7, 0.7, 0.7], 'LineWidth', 0.5);
end

% 绘制各方法的性能曲线
for i = 1:4
    values = [radar_data(i,:), radar_data(i,1)];  % 闭合多边形
    polarplot(angles, values, 'o-', 'LineWidth', 3, ...
             'Color', colors(i,:), 'MarkerSize', 8, 'MarkerFaceColor', colors(i,:), ...
             'DisplayName', methods{i});
end

% 设置雷达图属性
thetaticks(rad2deg(angles(1:end-1)));
thetaticklabels(radar_labels);
rlim([0, 1]);
rticks([0.2, 0.4, 0.6, 0.8, 1.0]);
rticklabels({'20%', '40%', '60%', '80%', '100%'});

% 标题和图例
title('四种字典学习方法综合性能雷达图', 'FontSize', 16, 'FontWeight', 'bold', 'Pad', 20);
legend('Location', 'best', 'FontSize', 12);
grid on;

% 保存雷达图
savefig('methods_radar_chart.fig');
print('methods_radar_chart.png', '-dpng', '-r300');

fprintf('✓ 雷达图已保存:\n');
fprintf('  - methods_radar_chart.fig\n');
fprintf('  - methods_radar_chart.png\n');

%% 创建详细的性能分析表
figure('Position', [150, 150, 1000, 700]);

% 创建性能分析表格
performance_table = table(...
    methods', ...
    nmsc_values', ...
    far_values', ...
    fdr_values', ...
    time_values', ...
    radar_data(:,1)*100, ...
    radar_data(:,2)*100, ...
    radar_data(:,3)*100, ...
    radar_data(:,4)*100, ...
    'VariableNames', {'方法', 'NMSC原值', 'FAR原值', 'FDR原值', '时间原值', ...
                     '稳定性得分', '误报率得分', '检出率得分', '效率得分'});

% 显示表格
uitable('Data', table2cell(performance_table), ...
        'ColumnName', performance_table.Properties.VariableNames, ...
        'Position', [50, 50, 900, 600], ...
        'FontSize', 12);

% 添加标题
annotation('textbox', [0.1, 0.9, 0.8, 0.08], ...
           'String', '四种字典学习方法详细性能对比表', ...
           'FontSize', 16, 'FontWeight', 'bold', ...
           'HorizontalAlignment', 'center', 'EdgeColor', 'none');

% 保存表格
savefig('methods_performance_table.fig');
print('methods_performance_table.png', '-dpng', '-r300');

fprintf('✓ 性能表格已保存:\n');
fprintf('  - methods_performance_table.fig\n');
fprintf('  - methods_performance_table.png\n');

%% 创建3D性能对比图
figure('Position', [200, 200, 900, 700]);

% 3D散点图：FAR vs FDR vs NMSC
scatter3(far_values, fdr_values, nmsc_values, 200, 1:4, 'filled', 'MarkerEdgeColor', 'black');
colormap(colors);

% 添加方法标签
for i = 1:4
    text(far_values(i), fdr_values(i), nmsc_values(i), ['  ', methods{i}], ...
         'FontSize', 12, 'FontWeight', 'bold');
end

xlabel('FAR (误报率)', 'FontSize', 12);
ylabel('FDR (检出率)', 'FontSize', 12);
zlabel('NMSC (字典变化度)', 'FontSize', 12);
title('三维性能空间分布图', 'FontSize', 16, 'FontWeight', 'bold');
grid on;

% 设置视角
view(45, 30);

% 保存3D图
savefig('methods_3d_performance.fig');
print('methods_3d_performance.png', '-dpng', '-r300');

fprintf('✓ 3D性能图已保存:\n');
fprintf('  - methods_3d_performance.fig\n');
fprintf('  - methods_3d_performance.png\n');

%% 输出详细分析
fprintf('\n📊 雷达图分析结果:\n');
fprintf('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');

for i = 1:4
    fprintf('\n🔍 %s 性能分析:\n', methods{i});
    fprintf('   字典稳定性: %.1f%% (NMSC=%.3f)\n', radar_data(i,1)*100, nmsc_values(i));
    fprintf('   误报率性能: %.1f%% (FAR=%.3f)\n', radar_data(i,2)*100, far_values(i));
    fprintf('   检出率性能: %.1f%% (FDR=%.3f)\n', radar_data(i,3)*100, fdr_values(i));
    fprintf('   计算效率: %.1f%% (时间=%.0f秒)\n', radar_data(i,4)*100, time_values(i));
    
    % 综合评价
    avg_score = mean(radar_data(i,:)) * 100;
    if avg_score >= 80
        rating = '优秀';
    elseif avg_score >= 70
        rating = '良好';
    elseif avg_score >= 60
        rating = '一般';
    else
        rating = '需改进';
    end
    fprintf('   综合评价: %.1f%% (%s)\n', avg_score, rating);
end

fprintf('\n🏆 最佳性能维度:\n');
[~, best_stability] = max(radar_data(:,1));
[~, best_far] = max(radar_data(:,2));
[~, best_fdr] = max(radar_data(:,3));
[~, best_efficiency] = max(radar_data(:,4));

fprintf('   字典稳定性最佳: %s\n', methods{best_stability});
fprintf('   误报率性能最佳: %s\n', methods{best_far});
fprintf('   检出率性能最佳: %s\n', methods{best_fdr});
fprintf('   计算效率最佳: %s\n', methods{best_efficiency});

fprintf('\n🎉 雷达图分析完成！\n');
fprintf('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
