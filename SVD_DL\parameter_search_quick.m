%% 快速参数搜索实验 - 寻找最佳FDR对应的lambda、n_atoms、sparsity参数组合
% 使用较少的参数组合进行快速测试

rng(42);

%% ========== 参数网格搜索设置 (快速版本) ==========
% 定义参数范围 - 减少参数数量以加快测试
lambda_list = [1e-4, 1e-3, 1e-2];  % 3个lambda值
n_atoms_list = [40, 50, 60];  % 3个原子数
sparsity_list = [2, 3];  % 2个稀疏度

% 计算总的参数组合数
n_lambda = length(lambda_list);
n_atoms_count = length(n_atoms_list);
n_sparsity = length(sparsity_list);
total_combinations = n_lambda * n_atoms_count * n_sparsity;

fprintf('快速测试版本 - 总共需要测试 %d 个参数组合\n', total_combinations);
fprintf('参数范围:\n');
fprintf('  lambda: [%.1e, %.1e, %.1e]\n', lambda_list);
fprintf('  n_atoms: [%d, %d, %d]\n', n_atoms_list);
fprintf('  sparsity: [%d, %d]\n', sparsity_list);

% 存储所有结果
results = struct();
results.lambda_vals = [];
results.n_atoms_vals = [];
results.sparsity_vals = [];
results.FAR_vals = [];
results.FDR_vals = [];
results.R_test_all = {};
results.R_limit_vals = [];

combination_idx = 0;

% 三重嵌套循环进行网格搜索
for ll = 1:n_lambda
    lambda = lambda_list(ll);
    
    for aa = 1:n_atoms_count
        n_atoms = n_atoms_list(aa);
        
        for ss = 1:n_sparsity
            sparsity = sparsity_list(ss);
            combination_idx = combination_idx + 1;
            
            fprintf('\n==== 组合 %d/%d: lambda=%.2e, n_atoms=%d, sparsity=%d ====\n', ...
                    combination_idx, total_combinations, lambda, n_atoms, sparsity);
            
            try
                %% ========== 1. 加载训练数据 ==========
                load('mode1_train.mat');  % 变量 train_data，1000x8
                Y = train_data';          % [8 x 1000]
                
                %% ========== 2. K-SVD训练 ==========
                n_iter = 30;  % 减少迭代次数以加快速度
                D_init = randn(8, n_atoms);
                for k = 1:n_atoms
                    D_init(:,k) = D_init(:,k) / norm(D_init(:,k));
                end
                [Dictionary, ~] = ksvd_simple(Y, D_init, sparsity, n_iter);

                [U, S, V] = svd(Dictionary, 'econ');
                singular_values = diag(S);
                energy = cumsum(singular_values.^2) / sum(singular_values.^2);
                k_locked = find(energy >= 0.9, 1, 'first');
                U_locked = U(:, 1:k_locked);

                Dictionary_history = cell(5,1);
                U_locked_history = cell(5,1);
                Dictionary_history{1} = Dictionary;
                U_locked_history{1} = U_locked;
                D_prev = Dictionary;
                U_locked_prev = U_locked;
                k_locked_prev = k_locked;
                
                for mode = 2:5
                    % === 新mode训练 ===
                    fname = sprintf('mode%d_train.mat', mode);
                    load(fname); Y_new = train_data';
                    n_atoms_current = size(D_prev,2);
                    D_init = randn(8, n_atoms_current);
                    for k = 1:n_atoms_current
                        D_init(:,k) = D_init(:,k) / norm(D_init(:,k));
                    end
                    [D_new, ~] = ksvd_simple(Y_new, D_init, sparsity, 20);  % 减少迭代

                    % === SVD判定主原子 ===
                    [Uo, So, Vo] = svd(D_prev, 'econ');
                    S_diag = diag(So);
                    V_weight = abs(Vo) * S_diag;
                    [~, idx] = sort(V_weight, 'descend');
                    V_weight_sorted = V_weight(idx);
                    energy_cum = cumsum(V_weight_sorted) / sum(V_weight_sorted);
                    k_important = find(energy_cum >= 0.9, 1, 'first');
                    imp_idx_old = idx(1:k_important);
                    unimp_idx_old = idx(k_important+1:end);

                    [~, S_new, V_new] = svd(D_new, 'econ');
                    V_weight_new = abs(V_new) * diag(S_new);
                    [~, idx_new] = sort(V_weight_new, 'descend');
                    V_weight_sorted_new = V_weight_new(idx_new);
                    energy_cum_new = cumsum(V_weight_sorted_new) / sum(V_weight_sorted_new);
                    k_important_new = find(energy_cum_new >= 0.9, 1, 'first');
                    imp_idx_new = idx_new(1:k_important_new);

                    D_fused = D_prev;
                    replace_cnt = min(numel(imp_idx_new), numel(unimp_idx_old));
                    D_fused(:, unimp_idx_old(1:replace_cnt)) = D_new(:, imp_idx_new(1:replace_cnt));

                    % === 主空间保护微调 ===
                    U_locked = Uo(:, 1:k_locked_prev);
                    P_locked = U_locked * U_locked';
                    X = zeros(size(D_fused,2), size(Y_new,2));
                    for n = 1:size(Y_new,2)
                        X(:,n) = omp(D_fused, Y_new(:,n), sparsity);
                    end
                    A = lambda * (P_locked') * P_locked;
                    B = X * X';
                    C = lambda * (P_locked') * P_locked * D_prev + Y_new * X';
                    D_fused = sylvester(A,B,C);

                    for k = 1:size(D_fused,2)
                        D_fused(:,k) = D_fused(:,k) / norm(D_fused(:,k));
                    end

                    [U_new, S_new, ~] = svd(D_fused, 'econ');
                    singular_values_new = diag(S_new);
                    energy_new = cumsum(singular_values_new.^2) / sum(singular_values_new.^2);
                    k_locked_new = find(energy_new >= 0.9, 1, 'first');
                    U_locked_new = U_new(:, 1:k_locked_new);

                    Dictionary_history{mode} = D_fused;
                    U_locked_history{mode} = U_locked_new;
                    D_prev = D_fused;
                    U_locked_prev = U_locked_new;
                    k_locked_prev = k_locked_new;
                end
                
                %% ========== 字典训练后性能评估 ==========
                D_K = Dictionary_history{end};

                % --- 训练及测试数据准备 ---
                Y_train = []; Y_test = [];
                for mm = 1:5
                    load(sprintf("mode%d_train.mat",mm)); Y_train=[Y_train;train_data];
                    load(sprintf("mode%d_test_normal.mat",mm)); Y_test=[Y_test;test_normal_data];
                    load(sprintf("mode%d_test_fault.mat",mm));  Y_test=[Y_test;test_fault_data];
                end
                Y_train = Y_train'; Y_test = Y_test';

                % === 训练集编码+统计量
                R_train = zeros(1, size(Y_train,2));
                for i = 1:size(Y_train,2)
                    y = Y_train(:,i);
                    x = omp(D_K, y, sparsity);
                    R_train(i) = norm(y - D_K*x, 2)^2;
                end
                [f_R, xi_R] = ksdensity(R_train,  'Function', 'cdf');
                idx_R = find(f_R >= 1 - 0.01, 1, 'first');
                R_limit= xi_R(idx_R);

                % === 测试集编码+统计量
                R_test = zeros(1, size(Y_test,2));
                for i = 1:size(Y_test,2)
                    y = Y_test(:,i);
                    x = omp(D_K, y, sparsity);
                    R_test(i) = norm(y - D_K*x, 2)^2;
                end

                % === 计算FAR/FDR ===
                n_mode = 5; n_each = 1000; n_normal = 500; n_fault = 500;
                FAR_vec = zeros(n_mode,1); FDR_vec = zeros(n_mode,1);
                for m = 1:n_mode
                    idx_start = (m-1)*n_each + 1;
                    idx_normal = idx_start : idx_start + n_normal - 1;
                    idx_fault  = idx_start + n_normal : idx_start + n_each - 1;
                    FAR_vec(m) = sum(R_test(idx_normal) > R_limit) / n_normal;
                    FDR_vec(m) = sum(R_test(idx_fault)  > R_limit) / n_fault;
                end
                FAR_current = mean(FAR_vec);
                FDR_current = mean(FDR_vec);

                % 存储当前参数组合的结果
                results.lambda_vals(end+1) = lambda;
                results.n_atoms_vals(end+1) = n_atoms;
                results.sparsity_vals(end+1) = sparsity;
                results.FAR_vals(end+1) = FAR_current;
                results.FDR_vals(end+1) = FDR_current;
                results.R_test_all{end+1} = R_test;
                results.R_limit_vals(end+1) = R_limit;

                fprintf('成功: FAR=%.4f, FDR=%.4f\n', FAR_current, FDR_current);
                
            catch ME
                fprintf('错误: %s\n', ME.message);
                % 存储失败的结果
                results.lambda_vals(end+1) = lambda;
                results.n_atoms_vals(end+1) = n_atoms;
                results.sparsity_vals(end+1) = sparsity;
                results.FAR_vals(end+1) = NaN;
                results.FDR_vals(end+1) = NaN;
                results.R_test_all{end+1} = [];
                results.R_limit_vals(end+1) = NaN;
            end
            
        end  % sparsity loop
    end  % n_atoms loop
end  % lambda loop

%% ========== 分析最佳参数组合 ==========
fprintf('\n========== 快速参数搜索完成 ==========\n');
fprintf('总共测试了 %d 个参数组合\n', length(results.FDR_vals));

% 移除失败的结果
valid_idx = ~isnan(results.FDR_vals);
if sum(valid_idx) == 0
    fprintf('所有参数组合都失败了！\n');
    return;
end

fprintf('成功的参数组合: %d 个\n', sum(valid_idx));

% 找到最佳FDR对应的参数组合
valid_FDR = results.FDR_vals(valid_idx);
valid_lambda = results.lambda_vals(valid_idx);
valid_n_atoms = results.n_atoms_vals(valid_idx);
valid_sparsity = results.sparsity_vals(valid_idx);
valid_FAR = results.FAR_vals(valid_idx);

[maxFDR, max_idx] = max(valid_FDR);
best_lambda = valid_lambda(max_idx);
best_n_atoms = valid_n_atoms(max_idx);
best_sparsity = valid_sparsity(max_idx);
best_FAR = valid_FAR(max_idx);

fprintf('\n最佳FDR = %.4f\n', maxFDR);
fprintf('对应参数组合:\n');
fprintf('  lambda = %.2e\n', best_lambda);
fprintf('  n_atoms = %d\n', best_n_atoms);
fprintf('  sparsity = %d\n', best_sparsity);
fprintf('  对应FAR = %.4f\n', best_FAR);

% 找到最小FAR对应的参数组合
[minFAR, min_idx] = min(valid_FAR);
minFAR_lambda = valid_lambda(min_idx);
minFAR_n_atoms = valid_n_atoms(min_idx);
minFAR_sparsity = valid_sparsity(min_idx);
minFAR_FDR = valid_FDR(min_idx);

fprintf('\n最小FAR = %.4f\n', minFAR);
fprintf('对应参数组合:\n');
fprintf('  lambda = %.2e\n', minFAR_lambda);
fprintf('  n_atoms = %d\n', minFAR_n_atoms);
fprintf('  sparsity = %d\n', minFAR_sparsity);
fprintf('  对应FDR = %.4f\n', minFAR_FDR);

%% ========== 可视化结果 ==========
if sum(valid_idx) > 1
    % 参数对FDR的影响
    figure('Position', [100, 100, 1200, 400]);

    subplot(1,3,1);
    scatter(valid_lambda, valid_FDR, 50, 'filled');
    xlabel('Lambda'); ylabel('FDR');
    title('Lambda对FDR的影响');
    set(gca, 'XScale', 'log');
    grid on;

    subplot(1,3,2);
    scatter(valid_n_atoms, valid_FDR, 50, 'filled');
    xlabel('N\_atoms'); ylabel('FDR');
    title('N\_atoms对FDR的影响');
    grid on;

    subplot(1,3,3);
    scatter(valid_sparsity, valid_FDR, 50, 'filled');
    xlabel('Sparsity'); ylabel('FDR');
    title('Sparsity对FDR的影响');
    grid on;

    % FAR vs FDR散点图
    figure('Position', [100, 100, 600, 500]);
    scatter(valid_FAR, valid_FDR, 50, 'filled'); hold on;
    scatter(best_FAR, maxFDR, 100, 'r', 'filled', '^');
    scatter(minFAR, minFAR_FDR, 100, 'b', 'filled', 'v');
    xlabel('FAR'); ylabel('FDR');
    title('FAR vs FDR 散点图 (快速测试)');
    legend('所有参数组合', '最大FDR', '最小FAR', 'Location', 'best');
    grid on;
    set(gca,'FontSize',12);
end

%% ========== 保存结果 ==========
save('parameter_search_quick_results.mat', 'results', 'best_lambda', 'best_n_atoms', ...
     'best_sparsity', 'maxFDR', 'best_FAR', 'minFAR');

fprintf('\n快速测试结果已保存到 parameter_search_quick_results.mat\n');
