%% 测试JMSDL字典历史保存和加载

fprintf('========== 测试JMSDL字典历史功能 ==========\n');

%% 1. 检查训练数据
fprintf('1. 检查训练数据文件...\n');
required_files = {'mode1_train.mat', 'mode2_train.mat', 'mode3_train.mat', 'mode4_train.mat', 'mode5_train.mat'};
all_exist = true;

for i = 1:length(required_files)
    if exist(required_files{i}, 'file')
        fprintf('   ✓ %s\n', required_files{i});
    else
        fprintf('   ❌ %s (缺失)\n', required_files{i});
        all_exist = false;
    end
end

if ~all_exist
    error('缺少必要的训练数据文件');
end

%% 2. 运行JMSDL训练（如果需要）
fprintf('\n2. 检查JMSDL字典文件...\n');

dict_exists = exist('D_n_num_JMSDL.mat', 'file');
history_exists = exist('JMSDL_Dictionary_history.mat', 'file');

if dict_exists && history_exists
    fprintf('   ✓ 字典文件和历史文件都存在\n');
else
    fprintf('   需要运行JMSDL训练...\n');
    try
        Copy_of_JMSDL_new2_num;
        fprintf('   ✓ JMSDL训练完成\n');
    catch ME
        fprintf('   ❌ JMSDL训练失败: %s\n', ME.message);
        return;
    end
end

%% 3. 验证字典历史
fprintf('\n3. 验证字典历史文件...\n');

if exist('JMSDL_Dictionary_history.mat', 'file')
    load('JMSDL_Dictionary_history.mat', 'Dictionary_history');
    
    fprintf('   ✓ 成功加载字典历史\n');
    fprintf('   字典数量: %d\n', length(Dictionary_history));
    
    % 检查每个字典
    for i = 1:length(Dictionary_history)
        if ~isempty(Dictionary_history{i})
            dict_size = size(Dictionary_history{i});
            fprintf('   Mode %d 字典: %dx%d\n', i, dict_size(1), dict_size(2));
        else
            fprintf('   ❌ Mode %d 字典为空\n', i);
        end
    end
    
    % 验证字典变化
    if length(Dictionary_history) >= 2
        fprintf('\n   字典变化分析:\n');
        for i = 2:length(Dictionary_history)
            D_prev = Dictionary_history{i-1};
            D_curr = Dictionary_history{i};
            
            if ~isempty(D_prev) && ~isempty(D_curr) && isequal(size(D_prev), size(D_curr))
                diff_norm = norm(D_curr - D_prev, 'fro');
                fprintf('   Mode %d→%d 变化范数: %.6f\n', i-1, i, diff_norm);
            else
                fprintf('   ❌ Mode %d→%d 字典大小不匹配或为空\n', i-1, i);
            end
        end
    end
    
else
    fprintf('   ❌ 字典历史文件不存在\n');
    return;
end

%% 4. 测试性能分析
fprintf('\n4. 测试性能分析...\n');

try
    % 运行性能分析
    analyze_jmsdl_performance;
    
    % 检查结果
    if exist('jmsdl_performance_analysis.mat', 'file')
        load('jmsdl_performance_analysis.mat', 'results_jmsdl');
        fprintf('   ✓ 性能分析完成\n');
        
        % 显示关键结果
        if isfield(results_jmsdl, 'NMSC_history')
            fprintf('   NMSC历史: [%s]\n', num2str(results_jmsdl.NMSC_history', '%.4f '));
        end
        
        if isfield(results_jmsdl, 'FAR_overall') && isfield(results_jmsdl, 'FDR_overall')
            fprintf('   总体性能: FAR=%.4f, FDR=%.4f\n', ...
                    results_jmsdl.FAR_overall, results_jmsdl.FDR_overall);
        end
        
    else
        fprintf('   ❌ 性能分析结果文件未生成\n');
    end
    
catch ME
    fprintf('   ❌ 性能分析失败: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('   错误位置: %s 第%d行\n', ME.stack(1).name, ME.stack(1).line);
    end
end

%% 5. 总结
fprintf('\n========== 测试总结 ==========\n');

% 检查所有输出文件
output_files = {'D_n_num_JMSDL.mat', 'JMSDL_Dictionary_history.mat', 'jmsdl_performance_analysis.mat'};
fprintf('输出文件状态:\n');

for i = 1:length(output_files)
    if exist(output_files{i}, 'file')
        file_info = dir(output_files{i});
        fprintf('   ✓ %s (%.1f KB)\n', output_files{i}, file_info.bytes/1024);
    else
        fprintf('   ❌ %s (缺失)\n', output_files{i});
    end
end

fprintf('\n如果所有文件都存在，可以运行完整分析:\n');
fprintf('   run(''run_jmsdl_analysis.m'')\n');

fprintf('\n🎉 测试完成！\n');
