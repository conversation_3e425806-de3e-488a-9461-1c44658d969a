%% JMSDL方法性能分析 - 仿照SVD_DL方法
% 使用与SVD_DL相同的性能检测方法分析JMSDL
% 计算FAR、FDR、NMSC等指标

rng(42);

fprintf('========== JMSDL方法性能分析开始 ==========\n');
fprintf('分析目标: 使用与SVD_DL相同的性能检测方法\n');
fprintf('字典学习方法: JMSDL\n');
fprintf('性能指标: NMSC、主方向子空间夹角、FAR、FDR\n\n');

%% ========== 1. 字典学习和演化分析 ==========
fprintf('1. JMSDL字典学习和演化分析...\n');

% 检查是否已有训练好的字典
if exist('D_n_num_JMSDL.mat', 'file')
    fprintf('   发现已训练的字典文件，加载中...\n');
    load('D_n_num_JMSDL.mat', 'D_n');
    final_dict = D_n;
    fprintf('   最终字典大小: %dx%d\n', size(final_dict,1), size(final_dict,2));
else
    fprintf('   未找到训练好的字典，开始训练...\n');
    % 运行JMSDL训练
    Copy_of_JMSDL_new2_num;
    load('D_n_num_JMSDL.mat', 'D_n');
    final_dict = D_n;
end

% 加载JMSDL训练过程中保存的字典历史
fprintf('   加载JMSDL字典演化历史...\n');
if exist('JMSDL_Dictionary_history.mat', 'file')
    load('JMSDL_Dictionary_history.mat', 'Dictionary_history');
    fprintf('   ✓ 成功加载字典历史，包含 %d 个模式的字典\n', length(Dictionary_history));
else
    fprintf('   ❌ 未找到JMSDL_Dictionary_history.mat文件\n');
    fprintf('   请先运行 Copy_of_JMSDL_new2_num.m 进行字典训练\n');
    error('缺少字典历史文件');
end

% 预分配数组
NMSC_history = zeros(4, 1);  % 4个模式转换
Subspace_angle_history = zeros(4, 1);

% 计算NMSC和子空间夹角
fprintf('   计算NMSC和子空间夹角...\n');
epsilon = 1e-8;

for i = 2:5
    D_prev = Dictionary_history{i-1};
    D_curr = Dictionary_history{i};
    
    % 计算NMSC
    deltaD = D_curr - D_prev;
    NMSC_history(i-1) = mean((deltaD(:).^2) ./ (D_prev(:).^2 + epsilon));
    
    % 计算主方向子空间夹角
    [U_prev, S_prev, ~] = svd(D_prev, 'econ');
    [U_curr, ~, ~] = svd(D_curr, 'econ');
    
    % 确定主空间维度
    energy_prev = cumsum(diag(S_prev).^2) / sum(diag(S_prev).^2);
    r_prev = find(energy_prev >= 0.9, 1, 'first');
    if isempty(r_prev), r_prev = min([3, size(U_prev,2)]); end
    
    r_curr = min([r_prev, size(U_curr,2)]);
    if r_curr > 0
        try
            Subspace_angle_history(i-1) = subspace(U_prev(:,1:r_curr), U_curr(:,1:r_curr));
        catch
            Subspace_angle_history(i-1) = NaN;
        end
    else
        Subspace_angle_history(i-1) = NaN;
    end
end

% 计算主空间维度
subspace_dims = zeros(5, 1);
for i = 1:5
    [U, S, ~] = svd(Dictionary_history{i}, 'econ');
    singular_values = diag(S);
    energy = cumsum(singular_values.^2) / sum(singular_values.^2);
    k_locked = find(energy >= 0.9, 1, 'first');
    if isempty(k_locked)
        k_locked = min([3, size(U,2)]);
    end
    subspace_dims(i) = k_locked;
end

%% ========== 2. 性能评估 - 使用与SVD_DL相同的方法 ==========
fprintf('\n2. 性能评估 (FAR/FDR计算)...\n');

% 使用最终字典进行性能评估
D_K = final_dict;

% 检查OMP函数
if ~exist('omp', 'file')
    addpath('../SVD_DL');
    if ~exist('omp', 'file')
        error('未找到OMP函数，请确保omp.m在路径中');
    end
end

% 调用专门的FAR/FDR计算函数 (仿照GILDL的方法)
fprintf('   使用compute_far_fdr_jmsdl函数计算性能指标...\n');
try
    [FAR_overall, FDR_overall, FAR_vec, FDR_vec, R_test, R_limit] = compute_far_fdr_jmsdl(D_K);
    
    fprintf('   控制限: %.6f\n', R_limit);
    fprintf('   测试统计量范围: [%.6f, %.6f]\n', min(R_test), max(R_test));
    
catch ME
    fprintf('   警告: FAR/FDR计算失败: %s\n', ME.message);
    % 设置默认值
    FAR_overall = NaN; FDR_overall = NaN;
    FAR_vec = NaN(5,1); FDR_vec = NaN(5,1);
    R_test = []; R_limit = NaN;
end

%% ========== 3. 保存结果 ==========
results_jmsdl = struct();
results_jmsdl.method = 'JMSDL';
results_jmsdl.Dictionary_history = Dictionary_history;
results_jmsdl.final_dict = D_K;
results_jmsdl.NMSC_history = NMSC_history;
results_jmsdl.Subspace_angle_history = Subspace_angle_history;
results_jmsdl.subspace_dims = subspace_dims;

if ~isnan(FAR_overall)
    results_jmsdl.FAR_overall = FAR_overall;
    results_jmsdl.FDR_overall = FDR_overall;
    results_jmsdl.FAR_vec = FAR_vec;
    results_jmsdl.FDR_vec = FDR_vec;
    results_jmsdl.R_test = R_test;
    results_jmsdl.R_limit = R_limit;
end

save('jmsdl_performance_analysis.mat', 'results_jmsdl');

fprintf('\n💾 JMSDL性能分析结果已保存到 jmsdl_performance_analysis.mat\n');
fprintf('🎉 JMSDL方法性能分析完成！\n');

%% OMP函数定义
function w = omp(D, x, L)
% OMP  简易正交匹配追踪
%   w = omp(D, x, L)
%   D: D×K 字典, x: D×1 样本, L: 稀疏度
    K = size(D,2);
    r = x;                   % 初始残差
    idx = false(1,K);        % 已选原子索引
    w = zeros(K,1);
    for j = 1:L
        proj = D' * r;
        [~, kbest] = max(abs(proj));
        idx(kbest) = true;
        % 在已选原子子空间上做最小二乘
        Dsub = D(:,idx);
        a = Dsub \ x;
        r = x - Dsub * a;
        if norm(r) < 1e-6
            break;
        end
    end
    w(idx) = a;
end
