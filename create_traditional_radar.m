%% 四种字典学习方法传统雷达图
% 使用传统plot方法创建雷达图

clc; clear; close all;

fprintf('========== 生成传统雷达图 ==========\n');

%% 数据准备
methods = {'GILDL', 'DMCDL', 'SVD-DL', 'JMSDL'};
colors = [0.2, 0.6, 0.8;    % GILDL - 蓝色
          0.8, 0.4, 0.2;    % DMCDL - 橙色  
          0.4, 0.8, 0.3;    % SVD-DL - 绿色
          0.9, 0.6, 0.9];   % JMSDL - 紫色

% 原始性能数据
nmsc_values = [0.150, 0.120, 0.250, 10.000];
far_values = [0.025, 0.018, 0.035, 0.040];
fdr_values = [0.920, 0.950, 0.880, 0.850];
time_values = [45, 65, 30, 50];

%% 创建传统雷达图
figure('Position', [100, 100, 800, 800]);

% 归一化数据 (0-1范围)
nmsc_norm = 1 - (nmsc_values - min(nmsc_values)) / (max(nmsc_values) - min(nmsc_values));
far_norm = 1 - far_values / max(far_values);  % FAR越小越好
fdr_norm = fdr_values / max(fdr_values);      % FDR越大越好
time_norm = 1 - (time_values - min(time_values)) / (max(time_values) - min(time_values));

% 雷达图数据
radar_data = [nmsc_norm; far_norm; fdr_norm; time_norm]';
radar_labels = {'字典稳定性', '误报率性能', '检出率性能', '计算效率'};

% 角度设置
n_metrics = length(radar_labels);
angles = linspace(0, 2*pi, n_metrics+1);

% 转换为笛卡尔坐标
hold on;

% 绘制网格圆圈
for r = 0.2:0.2:1.0
    theta_circle = linspace(0, 2*pi, 100);
    x_circle = r * cos(theta_circle);
    y_circle = r * sin(theta_circle);
    plot(x_circle, y_circle, '--', 'Color', [0.7, 0.7, 0.7], 'LineWidth', 0.5);
end

% 绘制角度线
for i = 1:n_metrics
    x_line = [0, cos(angles(i))];
    y_line = [0, sin(angles(i))];
    plot(x_line, y_line, '--', 'Color', [0.7, 0.7, 0.7], 'LineWidth', 0.5);
end

% 绘制各方法的性能多边形
for i = 1:4
    values = [radar_data(i,:), radar_data(i,1)];  % 闭合
    x_coords = values .* cos(angles);
    y_coords = values .* sin(angles);
    
    % 绘制填充多边形
    fill(x_coords, y_coords, colors(i,:), 'FaceAlpha', 0.3, 'EdgeColor', colors(i,:), ...
         'LineWidth', 2.5, 'DisplayName', methods{i});
    
    % 绘制数据点
    plot(x_coords(1:end-1), y_coords(1:end-1), 'o', 'Color', colors(i,:), ...
         'MarkerSize', 8, 'MarkerFaceColor', colors(i,:), 'LineWidth', 2);
end

% 添加标签
for i = 1:n_metrics
    x_label = 1.15 * cos(angles(i));
    y_label = 1.15 * sin(angles(i));
    text(x_label, y_label, radar_labels{i}, 'HorizontalAlignment', 'center', ...
         'VerticalAlignment', 'middle', 'FontSize', 12, 'FontWeight', 'bold');
end

% 添加刻度标签
for r = 0.2:0.2:1.0
    text(r, 0, sprintf('%.0f%%', r*100), 'HorizontalAlignment', 'center', ...
         'VerticalAlignment', 'bottom', 'FontSize', 10);
end

% 设置图形属性
axis equal;
axis off;
xlim([-1.3, 1.3]);
ylim([-1.3, 1.3]);

% 标题和图例
title('四种字典学习方法综合性能雷达图', 'FontSize', 16, 'FontWeight', 'bold');
legend('Location', 'best', 'FontSize', 12);

% 保存雷达图
savefig('traditional_radar_chart.fig');
print('traditional_radar_chart.png', '-dpng', '-r300');

fprintf('✓ 传统雷达图已保存:\n');
fprintf('  - traditional_radar_chart.fig\n');
fprintf('  - traditional_radar_chart.png\n');

%% 创建性能对比柱状图
figure('Position', [150, 150, 1200, 800]);

% 子图1: 原始数据对比
subplot(2,2,1);
bar_data = [nmsc_values; far_values*10; fdr_values; time_values/10]';  % 缩放以便显示
b1 = bar(bar_data);
set(gca, 'XTickLabel', methods);
ylabel('标准化值');
title('原始性能数据对比', 'FontSize', 14, 'FontWeight', 'bold');
legend({'NMSC', 'FAR×10', 'FDR', '时间÷10'}, 'Location', 'best');
grid on;

% 子图2: 归一化得分对比
subplot(2,2,2);
b2 = bar(radar_data * 100);
set(gca, 'XTickLabel', methods);
ylabel('性能得分 (%)');
title('归一化性能得分对比', 'FontSize', 14, 'FontWeight', 'bold');
legend(radar_labels, 'Location', 'best');
grid on;

% 子图3: 综合得分排名
subplot(2,2,3);
overall_scores = mean(radar_data, 2) * 100;
[sorted_scores, rank_idx] = sort(overall_scores, 'descend');
b3 = bar(sorted_scores);
b3.FaceColor = 'flat';
b3.CData = colors(rank_idx, :);
set(gca, 'XTickLabel', methods(rank_idx));
ylabel('综合得分 (%)');
title('综合性能排名', 'FontSize', 14, 'FontWeight', 'bold');
grid on;

% 添加得分标签
for i = 1:4
    text(i, sorted_scores(i), sprintf('%.1f', sorted_scores(i)), ...
         'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', 'FontSize', 10);
end

% 子图4: 优势分析
subplot(2,2,4);
[~, best_indices] = max(radar_data, [], 1);
advantage_matrix = zeros(4, 4);
for i = 1:4
    advantage_matrix(best_indices(i), i) = 1;
end

imagesc(advantage_matrix);
colormap([1,1,1; 0.2,0.8,0.2]);  % 白色和绿色
set(gca, 'XTick', 1:4);
set(gca, 'XTickLabel', radar_labels);
set(gca, 'YTick', 1:4);
set(gca, 'YTickLabel', methods);
title('各维度最佳方法', 'FontSize', 14, 'FontWeight', 'bold');

% 添加标记
for i = 1:4
    for j = 1:4
        if advantage_matrix(i,j) == 1
            text(j, i, '★', 'HorizontalAlignment', 'center', ...
                 'VerticalAlignment', 'middle', 'FontSize', 20, 'Color', 'red');
        end
    end
end

sgtitle('四种字典学习方法详细性能分析', 'FontSize', 18, 'FontWeight', 'bold');

% 保存对比图
savefig('detailed_performance_comparison.fig');
print('detailed_performance_comparison.png', '-dpng', '-r300');

fprintf('✓ 详细对比图已保存:\n');
fprintf('  - detailed_performance_comparison.fig\n');
fprintf('  - detailed_performance_comparison.png\n');

%% 输出详细分析报告
fprintf('\n📊 详细性能分析报告:\n');
fprintf('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');

fprintf('\n🔍 各方法性能详情:\n');
for i = 1:4
    fprintf('\n%s:\n', methods{i});
    fprintf('   字典稳定性: %.1f%% (NMSC=%.3f)\n', radar_data(i,1)*100, nmsc_values(i));
    fprintf('   误报率性能: %.1f%% (FAR=%.3f)\n', radar_data(i,2)*100, far_values(i));
    fprintf('   检出率性能: %.1f%% (FDR=%.3f)\n', radar_data(i,3)*100, fdr_values(i));
    fprintf('   计算效率: %.1f%% (时间=%.0f秒)\n', radar_data(i,4)*100, time_values(i));
    fprintf('   综合得分: %.1f%%\n', overall_scores(i));
end

fprintf('\n🏆 各维度最佳方法:\n');
dimension_names = {'字典稳定性', '误报率性能', '检出率性能', '计算效率'};
for i = 1:4
    fprintf('   %s: %s (%.1f%%)\n', dimension_names{i}, methods{best_indices(i)}, ...
            radar_data(best_indices(i), i)*100);
end

fprintf('\n📈 综合排名:\n');
for i = 1:4
    rank_method = methods{rank_idx(i)};
    score = sorted_scores(i);
    if score >= 80
        rating = '优秀';
    elseif score >= 70
        rating = '良好';
    elseif score >= 60
        rating = '一般';
    else
        rating = '需改进';
    end
    fprintf('   第%d名: %s (%.1f%%, %s)\n', i, rank_method, score, rating);
end

fprintf('\n💡 应用建议:\n');
best_method = methods{rank_idx(1)};
fprintf('   综合推荐: %s\n', best_method);
fprintf('   - 在大多数应用场景下表现最佳\n');
fprintf('   - 各项性能指标相对均衡\n');

fprintf('\n🎯 特定场景推荐:\n');
fprintf('   高精度监测: %s (监测性能最佳)\n', methods{best_indices(2)});
fprintf('   实时应用: %s (计算效率最高)\n', methods{best_indices(4)});
fprintf('   稳定性要求: %s (字典最稳定)\n', methods{best_indices(1)});

fprintf('\n🎉 传统雷达图分析完成！\n');
fprintf('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
