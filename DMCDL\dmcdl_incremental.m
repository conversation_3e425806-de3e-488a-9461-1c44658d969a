function [D_new,X_new,W_new,w_new] = dmcdl_incremental(D_prev,W_prev,Y,...
    n_atoms,sparsity,n_iter,lambda_x,zeta,w_prev)
%=========================================================
%  增量学习 (Mode k+1)
%  输入:
%    D_prev, W_prev : 上轮字典 & 权重矩阵
%    Y              : [Y_{k+1}, Y_h]  拼接数据
%  输出:
%    D_new, X_new   : 更新后字典与系数
%    W_new, w_new   : 新权重矩阵与向量
%=========================================================
[m,N] = size(Y);
D = D_prev;
W = W_prev;
omega = zeros(n_atoms,1);      % 损失下降累计

for it = 1:n_iter
%% ----- 1) Fix D , 求 X  (式 21) -----
X = omp(D,Y,sparsity);

%% ----- 2) Fix X , 求 D  (闭式 23) -----
% 构建正则项
G = (X*X') + W;
F = (Y*X') + D_prev*W;
D = F / G;

% 列归一化 (可选，但确保与 D_prev 度量一致)
D = D ./ sqrt(sum(D.^2, 1));

%% ----- 3) 记录损失下降 (公式 19) -----
R_before = Y - D_prev*X;
R_after  = Y - D*X;
deltaL   = norm(R_before,'fro')^2 - norm(R_after,'fro')^2;
% 将总损失差按列分摊 (简单近似)
omega = omega - (deltaL/n_atoms);

D_prev = D;  % 为下次迭代准备
end

%% ----- 4) 计算新权重 w_i (公式 12) -----
delta_d = sum((D - D_prev).^2, 1) + zeta;
w_new   = omega ./ delta_d';
W_new   = diag(w_new);

D_new = D; X_new = X;
end
