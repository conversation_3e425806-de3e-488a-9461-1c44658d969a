function visualize_gildl_evolution(results_gildl)
%% 可视化GILDL字典演化过程中的NMSC和子空间夹角
% 输入: results_gildl - 包含GILDL分析结果的结构体

%% 提取数据
NMSC_history = results_gildl.NMSC_history;
Subspace_angle_history = results_gildl.Subspace_angle_history;
Dictionary_history = results_gildl.Dictionary_history;
U_locked_history = results_gildl.U_locked_history;
FAR_vec = results_gildl.FAR_vec;
FDR_vec = results_gildl.FDR_vec;
FAR_overall = results_gildl.FAR_overall;
FDR_overall = results_gildl.FDR_overall;

%% 图1: GILDL方法综合演化分析
figure('Position', [100, 100, 1400, 1000]);

% 子图1: NMSC变化趋势
subplot(2,3,1);
plot(2:5, NMSC_history, 'bo-', 'LineWidth', 3, 'MarkerSize', 10, 'MarkerFaceColor', 'b');
xlabel('目标模式', 'FontSize', 12);
ylabel('NMSC', 'FontSize', 12);
title('GILDL: 归一化均方变化度', 'FontSize', 14);
grid on;
ylim([0, max(NMSC_history)*1.1]);
set(gca, 'FontSize', 11);
% 添加数值标签
for i = 1:length(NMSC_history)
    text(i+1, NMSC_history(i)+max(NMSC_history)*0.02, sprintf('%.3f', NMSC_history(i)), ...
         'HorizontalAlignment', 'center', 'FontSize', 10);
end

% 子图2: 子空间夹角变化趋势
subplot(2,3,2);
valid_idx = ~isnan(Subspace_angle_history);
valid_modes = find(valid_idx) + 1;
valid_angles = Subspace_angle_history(valid_idx);
plot(valid_modes, valid_angles, 'ro-', 'LineWidth', 3, 'MarkerSize', 10, 'MarkerFaceColor', 'r');
xlabel('目标模式', 'FontSize', 12);
ylabel('子空间夹角 (弧度)', 'FontSize', 12);
title('GILDL: 主方向子空间夹角', 'FontSize', 14);
grid on;
ylim([0, pi/2]);
set(gca, 'FontSize', 11);
% 添加数值标签
for i = 1:length(valid_angles)
    text(valid_modes(i), valid_angles(i)+0.05, sprintf('%.3f', valid_angles(i)), ...
         'HorizontalAlignment', 'center', 'FontSize', 10);
end

% 子图3: 主空间维度变化
subplot(2,3,3);
subspace_dims = cellfun(@(x) size(x,2), U_locked_history);
plot(1:5, subspace_dims, 'go-', 'LineWidth', 3, 'MarkerSize', 10, 'MarkerFaceColor', 'g');
xlabel('模式', 'FontSize', 12);
ylabel('主空间维度', 'FontSize', 12);
title('GILDL: 主空间维度演化', 'FontSize', 14);
grid on;
set(gca, 'FontSize', 11);
% 添加数值标签
for i = 1:length(subspace_dims)
    text(i, subspace_dims(i)+0.1, sprintf('%d', subspace_dims(i)), ...
         'HorizontalAlignment', 'center', 'FontSize', 10);
end

% 子图4: FAR性能
subplot(2,3,4);
bar(1:5, FAR_vec, 'FaceColor', [0.8, 0.4, 0.4], 'EdgeColor', 'k');
xlabel('模式', 'FontSize', 12);
ylabel('FAR', 'FontSize', 12);
title('GILDL: 各模式FAR', 'FontSize', 14);
grid on;
set(gca, 'FontSize', 11);
% 添加数值标签
for i = 1:length(FAR_vec)
    text(i, FAR_vec(i)+0.005, sprintf('%.3f', FAR_vec(i)), ...
         'HorizontalAlignment', 'center', 'FontSize', 10);
end

% 子图5: FDR性能
subplot(2,3,5);
bar(1:5, FDR_vec, 'FaceColor', [0.4, 0.8, 0.4], 'EdgeColor', 'k');
xlabel('模式', 'FontSize', 12);
ylabel('FDR', 'FontSize', 12);
title('GILDL: 各模式FDR', 'FontSize', 14);
grid on;
set(gca, 'FontSize', 11);
% 添加数值标签
for i = 1:length(FDR_vec)
    text(i, FDR_vec(i)+0.02, sprintf('%.3f', FDR_vec(i)), ...
         'HorizontalAlignment', 'center', 'FontSize', 10);
end

% 子图6: 方法总结和关键指标
subplot(2,3,6);
axis off;
text(0.1, 0.9, 'GILDL方法总结:', 'FontSize', 14, 'FontWeight', 'bold');
text(0.1, 0.8, sprintf('总体FAR = %.4f', FAR_overall), 'FontSize', 12);
text(0.1, 0.7, sprintf('总体FDR = %.4f', FDR_overall), 'FontSize', 12);
text(0.1, 0.6, sprintf('平均NMSC = %.4f', mean(NMSC_history)), 'FontSize', 12);
if ~isempty(valid_angles)
    text(0.1, 0.5, sprintf('平均夹角 = %.3f弧度', mean(valid_angles)), 'FontSize', 12);
    text(0.1, 0.4, sprintf('         = %.2f°', mean(valid_angles)*180/pi), 'FontSize', 12);
end
text(0.1, 0.3, sprintf('主空间维度: %d-%d', min(subspace_dims), max(subspace_dims)), 'FontSize', 12);
text(0.1, 0.2, '字典学习方法: GILDL', 'FontSize', 12);

sgtitle('GILDL方法字典演化综合分析', 'FontSize', 16);

%% 图2: GILDL字典变化热图分析
figure('Position', [150, 150, 1200, 800]);

% 计算第一个字典和最后一个字典的变化
D1 = Dictionary_history{1};
Dlast = Dictionary_history{end};
deltaD = Dlast - D1;

% 子图1: 字典差分热图
subplot(2,3,1);
imagesc(deltaD);
colorbar;
xlabel('字典原子编号', 'FontSize', 12); 
ylabel('观测量编号', 'FontSize', 12);
title('GILDL: 最终字典与mode1差分热图', 'FontSize', 14);
set(gca,'FontSize',11);

% 子图2: 字典绝对变化热图
subplot(2,3,2);
imagesc(abs(deltaD));
colorbar;
xlabel('字典原子编号', 'FontSize', 12); 
ylabel('观测量编号', 'FontSize', 12);
title('GILDL: 字典绝对变化热图', 'FontSize', 14);
set(gca,'FontSize',11);

% 子图3: 相对变化热图
subplot(2,3,3);
epsilon = 1e-8;
relative_change = (deltaD).^2 ./ (D1.^2 + epsilon);
imagesc(relative_change);
colorbar;
xlabel('字典原子编号', 'FontSize', 12); 
ylabel('观测量编号', 'FontSize', 12);
title('GILDL: 字典相对变化热图', 'FontSize', 14);
set(gca,'FontSize',11);

% 子图4-6: 各模式转换的变化热图
for mode_transition = 1:3
    subplot(2,3,mode_transition+3);
    
    D_old = Dictionary_history{mode_transition};
    D_new = Dictionary_history{mode_transition + 1};
    delta_mode = D_new - D_old;
    
    imagesc(abs(delta_mode));
    colorbar;
    xlabel('字典原子编号', 'FontSize', 12);
    ylabel('观测量编号', 'FontSize', 12);
    title(sprintf('GILDL: Mode %d→%d 变化热图', mode_transition, mode_transition+1), 'FontSize', 14);
    set(gca, 'FontSize', 11);
    
    % 添加NMSC值
    text(size(delta_mode,2)*0.02, size(delta_mode,1)*0.95, ...
         sprintf('NMSC=%.3f', NMSC_history(mode_transition)), ...
         'Color', 'white', 'FontSize', 12, 'FontWeight', 'bold');
end

sgtitle('GILDL方法字典变化热图分析', 'FontSize', 16);

%% 图3: GILDL监测统计量分析 (按照num_monitoring_exp.m的风格)
figure('Position', [200, 200, 1200, 800]);

R_test = results_gildl.R_test;
R_limit = results_gildl.R_limit;

% 子图1: 监测统计量序列 (与num_monitoring_exp.m相同的风格)
subplot(2,2,1);
plot(R_test, 'b', 'LineWidth', 1.2); hold on;
yline(R_limit, '--k', 'LineWidth', 2, 'Color', 'r');
xlabel('样本编号', 'FontSize', 12);
ylabel('R统计量', 'FontSize', 12);
title('GILDL: 在线监测统计量与控制限', 'FontSize', 14);
legend('测试样本','控制限', 'Location', 'best');
grid on;
set(gca,'FontSize',12);

% 子图2: 统计量直方图
subplot(2,2,2);
histogram(R_test, 50, 'FaceAlpha', 0.7, 'EdgeColor', 'k');
xline(R_limit, '--r', 'LineWidth', 2);
xlabel('R统计量', 'FontSize', 12);
ylabel('频次', 'FontSize', 12);
title('GILDL: 监测统计量分布', 'FontSize', 14);
legend('统计量分布','控制限', 'Location', 'best');
grid on;
set(gca,'FontSize',12);

% 子图3: 各模式的FAR/FDR详细分析
subplot(2,2,3);
FAR_vec = results_gildl.FAR_vec;
FDR_vec = results_gildl.FDR_vec;

x = 1:5;
width = 0.35;
bar(x - width/2, FAR_vec, width, 'DisplayName', 'FAR', 'FaceColor', [0.8, 0.4, 0.4]);
hold on;
bar(x + width/2, FDR_vec, width, 'DisplayName', 'FDR', 'FaceColor', [0.4, 0.8, 0.4]);
set(gca, 'XTick', x, 'XTickLabel', arrayfun(@(i) sprintf('Mode %d', i), 1:5, 'UniformOutput', false));
ylabel('性能指标', 'FontSize', 12);
title('GILDL: 各模式FAR/FDR详细分析', 'FontSize', 14);
legend('Location', 'best');
grid on;
set(gca, 'FontSize', 12);

% 添加数值标签
for i = 1:5
    text(i - width/2, FAR_vec(i) + 0.01, sprintf('%.3f', FAR_vec(i)), ...
         'HorizontalAlignment', 'center', 'FontSize', 10);
    text(i + width/2, FDR_vec(i) + 0.01, sprintf('%.3f', FDR_vec(i)), ...
         'HorizontalAlignment', 'center', 'FontSize', 10);
end

% 子图4: 监测区域分析
subplot(2,2,4);
n_mode = 5; n_each = 1000; n_normal = 500; n_fault = 500;
colors = ['r', 'g', 'b', 'm', 'c'];

for m = 1:n_mode
    idx_start = (m-1)*n_each + 1;
    idx_normal = idx_start : idx_start + n_normal - 1;
    idx_fault  = idx_start + n_normal : idx_start + n_each - 1;

    % 绘制正常区域
    plot(idx_normal, R_test(idx_normal), '.', 'Color', colors(m), 'MarkerSize', 4, ...
         'DisplayName', sprintf('Mode %d Normal', m));
    hold on;

    % 绘制故障区域
    plot(idx_fault, R_test(idx_fault), 'x', 'Color', colors(m), 'MarkerSize', 4, ...
         'DisplayName', sprintf('Mode %d Fault', m));
end

yline(R_limit, '--k', 'LineWidth', 2, 'Color', 'r', 'DisplayName', '控制限');
xlabel('样本编号', 'FontSize', 12);
ylabel('R统计量', 'FontSize', 12);
title('GILDL: 各模式监测区域分析', 'FontSize', 14);
legend('Location', 'best');
grid on;
set(gca,'FontSize',12);

sgtitle('GILDL方法监测统计量分析 (按照num\_monitoring\_exp.m)', 'FontSize', 16);

%% 输出数值总结
fprintf('\n========== GILDL方法详细数值分析 ==========\n');
fprintf('📊 NMSC详细分析 (归一化均方变化度):\n');
for i = 1:4
    fprintf('   Mode %d→%d: NMSC = %.4f\n', i, i+1, NMSC_history(i));
end
fprintf('   NMSC统计: 均值=%.4f, 标准差=%.4f, 范围=[%.4f, %.4f]\n', ...
        mean(NMSC_history), std(NMSC_history), min(NMSC_history), max(NMSC_history));

fprintf('\n📐 子空间夹角详细分析 (弧度):\n');
for i = 1:4
    if ~isnan(Subspace_angle_history(i))
        fprintf('   Mode %d→%d: 夹角 = %.4f弧度 (%.2f°)\n', i, i+1, ...
                Subspace_angle_history(i), Subspace_angle_history(i)*180/pi);
    else
        fprintf('   Mode %d→%d: 无法计算夹角\n', i, i+1);
    end
end
if ~isempty(valid_angles)
    fprintf('   夹角统计: 均值=%.4f弧度(%.2f°), 标准差=%.4f弧度, 范围=[%.4f, %.4f]弧度\n', ...
            mean(valid_angles), mean(valid_angles)*180/pi, std(valid_angles), min(valid_angles), max(valid_angles));
end

fprintf('\n🔍 主空间维度分析:\n');
for i = 1:5
    fprintf('   Mode %d: 维度 = %d\n', i, subspace_dims(i));
end
fprintf('   维度统计: 均值=%.1f, 范围=[%d, %d]\n', ...
        mean(subspace_dims), min(subspace_dims), max(subspace_dims));

fprintf('\n🎯 性能指标分析:\n');
for i = 1:5
    fprintf('   Mode %d: FAR=%.4f, FDR=%.4f\n', i, FAR_vec(i), FDR_vec(i));
end
fprintf('   总体性能: FAR=%.4f, FDR=%.4f\n', FAR_overall, FDR_overall);

end
