% ==== 准备数据 ====
methods = categorical({'GILDL','DGCDL','DMCDL'});
    methods = reordercats(methods, {'GILDL','DGCDL','DMCDL'});
    y = double(methods);  % [1,2,3]
    
    % NMSC 与 SD 平均值
    avgNMSC = [mean(NMSC_GILDL(2:end)), mean(NMSC_DGCDL(2:end)), mean(NMSC_DMCDL(2:end))];
    avgSD   = [mean(ang_GILDL(2:end)),    mean(ang_DGCDL(2:end)),    mean(ang_DMCDL(2:end))];
    
    % 分组参数
    barWidth = 0.4;                    % 每根柱子的厚度
    offsets  = [-barWidth/2, barWidth/2];  % 两组柱子在 y 方向的偏移
    
    % 颜色
    c1 = [0.6, 0.8, 0.4];  % NMSC
    c2 = [0.3, 0.7, 0.6];  % SD
    
    % ==== 在同一 axes 中画分组横向柱状图 ====
    figure('Position',[300 300 700 400]);
    hold on;
    
    for j = 1:2
        % 选择这一组的数据与格式
        if j == 1
            data      = avgNMSC;
            faceColor = c1;
            fmt       = '%.3f';
            txtOffset = 0.01 * max(avgNMSC);
        else
            data      = avgSD;
            faceColor = c2;
            fmt       = '%.2f°';
            txtOffset = 0.01 * max(avgSD);
        end
    
        % 计算这一组柱子的垂直位置
        yPos = y + offsets(j);
    
        % 画柱
        barh(yPos, data, barWidth, 'FaceColor', faceColor, 'EdgeColor','none');
    
        % 在柱尾添加数值标签
        for i = 1:numel(data)
            text( ...
                data(i) + txtOffset, ...      % x 位置：柱尾 + 偏移
                yPos(i), ...                  % y 位置
                sprintf(fmt, data(i)), ...    % 文本内容
                'FontSize',12, ...
                'FontName','Times New Roman', ...
                'FontWeight','bold', ...
                'HorizontalAlignment','left', ...
                'VerticalAlignment','middle' ...
            );
        end
    end
    
    hold off;
    
    % 设置坐标轴与图例
    set(gca, ...
        'YTick', y, ...
        'YTickLabel', cellstr(methods), ...
        'FontName','Times New Roman', ...
        'FontSize',14, ...
        'FontWeight','bold');
    xlabel('Value','FontSize',16,'FontWeight','bold');
    legend({'Average NMSC','Average SD (°)'}, ...
           'Location','northeast','Box','off','FontSize',12);
    grid off;
    box on;
xlim([0,30]);
    