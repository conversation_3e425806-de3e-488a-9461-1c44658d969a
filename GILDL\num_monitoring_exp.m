%% ========== 1. 加载最终融合字典 ==========
%load('最终融合字典.mat', 'Dictionary_history'); % 假定你有这个文件
load("Dlast_num.mat");
D_K = D_last;                 % [8, n_atoms]
sparsity = 2;

%% ========== 2. 拼接1~5 mode训练/测试数据 ==========
Y_train = [];
Y_test = [];

load("mode1_train.mat");Y_train=[Y_train;train_data];
load("mode2_train.mat");Y_train=[Y_train;train_data];
load("mode3_train.mat");Y_train=[Y_train;train_data];
load("mode4_train.mat");Y_train=[Y_train;train_data];
load("mode5_train.mat");Y_train=[Y_train;train_data];

load("mode1_test_normal.mat");load("mode1_test_fault.mat");Y_test=[Y_test;test_normal_data;test_fault_data];
load("mode2_test_normal.mat");load("mode2_test_fault.mat");Y_test=[Y_test;test_normal_data;test_fault_data];
load("mode3_test_normal.mat");load("mode3_test_fault.mat");Y_test=[Y_test;test_normal_data;test_fault_data];
load("mode4_test_normal.mat");load("mode4_test_fault.mat");Y_test=[Y_test;test_normal_data;test_fault_data];
load("mode5_test_normal.mat");load("mode5_test_fault.mat");Y_test=[Y_test;test_normal_data;test_fault_data];

Y_train = Y_train';          % [特征, 总样本数]
Y_test = Y_test';
%% ========== 3. 训练数据：OMP编码+R统计量 ==========
R_train = zeros(1, size(Y_train,2));
for i = 1:size(Y_train,2)
    y = Y_train(:,i);
    x = omp(D_K, y, sparsity);
    R_train(i) = norm(y - D_K*x, 2)^2;
end

    % 使用KDE估计统计量的分布并计算控制限
    [f_R, xi_R] = ksdensity(R_train,  'Function', 'cdf');
    % 找到 1-alpha 分位数
    idx_R = find(f_R >= 1 - 0.01, 1, 'first');
    R_limit= xi_R(idx_R);
%% ========== 4. 测试集：OMP编码+R统计量 =========

R_test = zeros(1, size(Y_test,2));
for i = 1:size(Y_test,2)
    y = Y_test(:,i);
    x = omp(D_K, y, sparsity);
    R_test(i) = norm(y - D_K*x, 2)^2;
end

%% ========== 5. 画统计量与控制限 ==========
figure;
plot(R_test, 'b');
yline(R_limit, '--k','LineWidth',2,'Color','r');
legend('测试样本','控制限');
xlabel('样本编号'); ylabel('R统计量');
title('在线监测统计量与控制限');

n_mode = 5;
n_each = 1000;     % 每个mode 1000个测试样本
n_normal = 500;    % 每mode前500正常
n_fault  = 500;    % 每mode后500故障

FAR_all = zeros(n_mode, 1);
FDR_all = zeros(n_mode, 1);

for m = 1:n_mode
    idx_start = (m-1)*n_each + 1;
    idx_normal = idx_start : idx_start + n_normal - 1;
    idx_fault  = idx_start + n_normal : idx_start + n_each - 1;
    
    FAR_all(m) = sum(R_test(idx_normal) > R_limit) / n_normal;
    FDR_all(m) = sum(R_test(idx_fault)  > R_limit) / n_fault;
end

FAR = mean(FAR_all);
FDR = mean(FDR_all);

fprintf('各mode FAR: %s\n', num2str(FAR_all','%.4f '));
fprintf('各mode FDR: %s\n', num2str(FDR_all','%.4f '));
fprintf('平均FAR=%.4f, 平均FDR=%.4f\n', FAR, FDR);

