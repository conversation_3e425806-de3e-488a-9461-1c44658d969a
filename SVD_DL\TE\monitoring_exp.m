%% ========== 1. 加载最终融合字典 ==========
%load('最终融合字典.mat', 'Dictionary_history'); % 假定你有这个文件
load("Dlast.mat");
D_K = Dlast;                 % [8, n_atoms]
sparsity = 2;

%% ========== 2. 拼接1~6 mode训练/测试数据 ==========
Y_train = [];
Y_test = [];

load('m1d00');load('m2d00');load('m3d00');load('m4d00');load('m5d00.mat');load('m6d00.mat')

train_mode1_norm=m1d00(1:400,1:54)';
%删除第46,50,54列
train_mode1_norm([46,50,54],:)=[];
train_mode2_norm=m2d00(1:400,1:54)';
train_mode2_norm([46,50,54],:)=[];
train_mode3_norm=m3d00(1:400,1:54)';
train_mode3_norm([46,50,54],:)=[];
train_mode4_norm=m4d00(1:400,1:54)';
train_mode4_norm([46,50,54],:)=[];
train_mode5_norm=m5d00(1:400,1:54)';
train_mode5_norm([46,50,54],:)=[];
train_mode6_norm=m6d00(1:400,1:54)';
train_mode6_norm([46,50,54],:)=[];

train_set={train_mode1_norm,train_mode2_norm,train_mode3_norm,train_mode4_norm,train_mode5_norm,train_mode6_norm};
Y_train=[train_mode1_norm,train_mode2_norm,train_mode3_norm,train_mode4_norm,train_mode5_norm,train_mode6_norm];


% 删除第46,50,54列
train_mode1_norm = m1d00(1:100,1:54)';
train_mode1_norm([46,50,54],:) = [];
train_mode2_norm = m2d00(1:100,1:54)';
train_mode2_norm([46,50,54],:) = [];
train_mode3_norm = m3d00(1:100,1:54)';
train_mode3_norm([46,50,54],:) = [];
train_mode4_norm = m4d00(1:100,1:54)';
train_mode4_norm([46,50,54],:) = [];
train_mode5_norm = m5d00(1:100,1:54)';
train_mode5_norm([46,50,54],:) = [];
train_mode6_norm = m6d00(1:100,1:54)';
train_mode6_norm([46,50,54],:) = [];


load('m1d02.mat'); load('m2d02.mat'); 
load('m3d02.mat');load('m4d02.mat');
load('m5d02.mat');load('m6d02.mat')
test_mode1 = m1d02(1:300,1:54)';
test_mode1([46,50,54],:) = [];
test_mode2 = m2d02(1:300,1:54)';
test_mode2([46,50,54],:) = [];
test_mode3 = m3d02(1:300,1:54)';
test_mode3([46,50,54],:) = [];
test_mode4 = m4d02(1:300,1:54)';
test_mode4([46,50,54],:) = [];
test_mode5 = m5d02(1:300,1:54)';
test_mode5([46,50,54],:) = [];
test_mode6 = m6d02(1:300,1:54)';
test_mode6([46,50,54],:) = [];



test_data = [train_mode1_norm, test_mode1, train_mode2_norm, test_mode2, ...
                train_mode3_norm, test_mode3,train_mode4_norm, test_mode4,...
                train_mode5_norm, test_mode5,train_mode6_norm, test_mode6,];

Y_test=test_data;



Y_train = Y_train;          % [特征, 总样本数]
Y_test = Y_test;
%% ========== 3. 训练数据：OMP编码+R统计量 ==========
R_train = zeros(1, size(Y_train,2));
for i = 1:size(Y_train,2)
    y = Y_train(:,i);
    x = omp(D_K, y, sparsity);
    R_train(i) = norm(y - D_K*x, 2)^2;
end

    % 使用KDE估计统计量的分布并计算控制限
    [f_R, xi_R] = ksdensity(R_train,  'Function', 'cdf');
    % 找到 1-alpha 分位数
    idx_R = find(f_R >= 1 - 0.01, 1, 'first');
    R_limit= xi_R(idx_R);
%% ========== 4. 测试集：OMP编码+R统计量 =========

R_test = zeros(1, size(Y_test,2));   
for i = 1:size(Y_test,2)
    y = Y_test(:,i);
    x = omp(D_K, y, sparsity);
    R_test(i) = norm(y - D_K*x, 2)^2;
end

%% ========== 5. 画统计量与控制限 ==========
figure;
plot(R_test, 'b');
yline(R_limit, '--k','LineWidth',2,'Color','r');
legend('测试样本','控制限');
xlabel('样本编号'); ylabel('R统计量');
title('在线监测统计量与控制限');

%% ========== 6. 计算FAR/FDR ==========
% n_test = size(Y_test,2);
% n_mode = 5;
% n_normal_each = size(test_normal_data,1); % 假设每个test_normal_data样本数一致
% n_fault_each = size(test_fault_data,1);   % 假设每个test_fault_data样本数一致
% n_normal = n_mode * n_normal_each;
% n_fault  = n_mode * n_fault_each;
% 
% R_test_normal = R_test(1:n_normal);
% R_test_fault  = R_test(n_normal+1 : n_normal+n_fault);
% 
% FAR = sum(R_test_normal > R_limit) / n_normal;
% FDR = sum(R_test_fault  > R_limit) / n_fault;
% fprintf('FAR=%.3f, FDR=%.3f\n', FAR, FDR);

