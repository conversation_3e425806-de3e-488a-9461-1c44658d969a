%% ===============================================================
%  DM‑CDL ‑ 初始建模阶段（算法 1 第 1~4 步）
%  输出:  D1  — 训练好的字典
%         X1  — 稀疏系数
%         w   — 优化敏感度权重向量 (l×1)
%         W1  — 原子记忆矩阵 λ1·diag(w)
%  依赖:  omp()  ‑ 稀疏编码
%% ===============================================================
function [D1,X1,w,W1] = dmcdl_initial(Y,n_atoms,sparsity,n_iter,lambda1,zeta)

    [m,~] = size(Y);
    
    % ---------- 1) 字典随机初始化 d_i^0 ----------
    D0 = randn(m,n_atoms);
    D0 = D0 ./ vecnorm(D0);      % 列归一化
    
    % 初始化变量
    D = D0;                       % 当前字典 (会迭代更新到 D1)
    omega = zeros(n_atoms,1);     % 累计总损失下降 Ω_i
    X = zeros(n_atoms,size(Y,2)); % 稀疏系数占位
    
    %% ---------- 2) K‑SVD 主循环 ----------
    for it = 1:n_iter
        % 2.1 稀疏编码 (OMP)
        X = omp(D,Y,sparsity);           % 用户已有或第三方实现
        
        % 2.2 列级 SVD 更新并统计 ΔL_i^η
        for i = 1:n_atoms
            idx = find(X(i,:) ~= 0);     % 当前列参与的样本索引
            if isempty(idx), continue; end
            
            % 残差矩阵 (排除第 i 列贡献)
            R = Y(:,idx) - D*X(:,idx) + D(:,i)*X(i,idx);
            
            % ----------- 旧损失 L_before -----------
            L_before = norm(R,'fro')^2;
            
            % ----------- SVD 更新第 i 个原子-----------
            [u,s,v] = svd(R,'econ');
            di_new  = u(:,1);
            xi_new  = s(1,1)*v(:,1)';
            
            % 更新字典和稀疏系数
            D(:,i)      = di_new;
            X(i,idx)    = xi_new;
            
            % ----------- 新损失 L_after -----------
            R_after = R - di_new*xi_new;
            L_after = norm(R_after,'fro')^2;
            
            % ----------- 累计总损失下降 Ω_i ----------
            % ΔL_i^η = L_after − L_before  (负值；下降取负)
            omega(i) = omega(i) - (L_after - L_before);  % 与论文 ω_i = −ΣΔL_i^η 一致
        end
    end
    
    D1 = D;    % 迭代收敛后的字典
    X1 = X;
    
    %% ---------- 3) 计算优化敏感度权重 w_i (公式 12) ----------
    delta_d = vecnorm(D0 - D1).^2 + zeta;  % ||d_i^0 - d_i^1||_2^2 + ζ
    w       = omega ./ delta_d';           % 行向量转列向量
    
    %% ---------- 4) 生成权重矩阵 W1 ----------
    W1 = lambda1 * diag(w);
    
    end
    