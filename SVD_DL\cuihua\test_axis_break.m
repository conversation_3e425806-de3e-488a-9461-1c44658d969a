%% 测试纵轴截断效果
clear; clc; close all;

% 模拟数据，包含你指定的范围
n_samples = 3000;
R_test = zeros(1, n_samples);

% 生成模拟数据：大部分在低值区域，少部分在高值区域
R_test(1:2000) = 1000 + 50000*rand(1, 2000);  % 低值区域
R_test(2001:2800) = 5e5 + 1e6*rand(1, 800);   % 中值区域  
R_test(2801:3000) = 1.5e7 + 2e6*rand(1, 200); % 高值区域

R_limit = 2.3207e+04;  % 控制限

%% 纵轴截断参数设置
y_break_start = 4.4111e+05;  % 截断开始值
y_break_end = 1.7644e+07;    % 截断结束值

% 计算截断调整参数
y_interval = 2e6;  % 纵坐标刻度间隔
adjust_value = 0.4 * y_interval;
update_num = y_break_end - y_break_start - y_interval;

fprintf('截断范围: %.4e - %.4e\n', y_break_start, y_break_end);
fprintf('平移量: %.4e\n', update_num);

% 处理数据：超过截断结束位置的数据向下平移
R_test_adjusted = R_test;
R_test_adjusted(R_test > y_break_end) = R_test(R_test > y_break_end) - update_num;

% 处理控制限
R_limit_adjusted = R_limit;
if R_limit > y_break_end
    R_limit_adjusted = R_limit - update_num;
end

%% 绘图
figure('Position', [100, 100, 1200, 600]);

% 原始数据图
subplot(1,2,1);
plot(R_test, 'b', 'LineWidth', 1.5);
hold on;
yline(R_limit, '--r', 'LineWidth', 2);
xlabel('样本编号');
ylabel('R统计量');
title('原始数据 (无截断)');
legend('R统计量', '控制限');
grid on;

% 截断后的图
subplot(1,2,2);
plot(R_test_adjusted, 'b', 'LineWidth', 1.5);
hold on;
yline(R_limit_adjusted, '--r', 'LineWidth', 2);

% 添加截断标记
ylimit = get(gca, 'ylim');
location_Y = (y_break_start + adjust_value - ylimit(1)) / diff(ylimit);

t1 = text(0, location_Y, '//', 'Units', 'normalized', 'BackgroundColor', 'w', ...
          'margin', eps, 'fontsize', 13, 'HorizontalAlignment', 'left');
set(t1, 'rotation', 90);

t2 = text(1, location_Y, '//', 'Units', 'normalized', 'BackgroundColor', 'w', ...
          'margin', eps, 'fontsize', 13, 'HorizontalAlignment', 'right');
set(t2, 'rotation', 90);

% 重新定义纵坐标刻度
lower_ticks = 0:y_interval:y_break_start;
upper_start = ceil(y_break_end/y_interval) * y_interval;
upper_end = ceil(max(R_test)/y_interval) * y_interval;
upper_ticks = upper_start:y_interval:upper_end;

y_tick_values = [lower_ticks, upper_ticks];
y_tick_positions = y_tick_values;
y_tick_positions(y_tick_values > y_break_start + eps) = ...
    y_tick_positions(y_tick_values > y_break_start + eps) - update_num;

% 设置刻度标签
y_tick_labels = cell(1, length(y_tick_values));
for i = 1:length(y_tick_values)
    y_tick_labels{i} = sprintf('%.1e', y_tick_values(i));
end

set(gca, 'ytick', y_tick_positions);
set(gca, 'yTickLabel', y_tick_labels, 'FontSize', 10);

xlabel('样本编号');
ylabel('R统计量');
title('截断后数据');
legend('R统计量', '控制限');
grid on;

sgtitle('纵轴截断效果对比', 'FontSize', 14, 'FontWeight', 'bold');

% 保存图片
saveas(gcf, 'Axis_Break_Test.png', 'png');
fprintf('测试图片已保存: Axis_Break_Test.png\n');
