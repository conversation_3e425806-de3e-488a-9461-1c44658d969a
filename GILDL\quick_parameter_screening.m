%% GILDL催化数据集快速参数筛选
% 快速筛选可能有效的参数范围，然后进行精细搜索

clc; clear; close all;
rng(42);

fprintf('========== GILDL催化数据集快速参数筛选 ==========\n');
fprintf('目标: 快速找到可能有效的参数范围\n\n');

%% 预加载数据
fprintf('预加载催化数据...\n');
data_modes = cell(3, 1);
for i = 1:3
    load(['data_selected_F', num2str(i), '.mat']);
    data_modes{i} = cell2mat(data_selected)';
    fprintf('  模式%d数据大小: %dx%d\n', i, size(data_modes{i}, 1), size(data_modes{i}, 2));
end

data_dim = size(data_modes{1}, 1);
fprintf('  数据维度: %d\n', data_dim);

%% 第一阶段：粗略筛选
fprintf('\n第一阶段：粗略筛选...\n');

% 粗略参数范围
n_atoms_coarse = [30, 40, 50, 60, 70, 80];  % 6个点
sparsity_coarse = [1, 2, 3, 4, 5];          % 5个点  
lambdaProtect_coarse = logspace(-5, 5, 11); % 11个点，1e-5到1e5

valid_coarse = [];
total_coarse = length(n_atoms_coarse) * length(sparsity_coarse) * length(lambdaProtect_coarse);

fprintf('粗略搜索组合数: %d\n', total_coarse);

tic;
count = 0;
for i = 1:length(n_atoms_coarse)
    for j = 1:length(sparsity_coarse)
        for k = 1:length(lambdaProtect_coarse)
            count = count + 1;
            
            n_atoms = n_atoms_coarse(i);
            sparsity = sparsity_coarse(j);
            lambdaProtect = lambdaProtect_coarse(k);
            
            if mod(count, 50) == 0
                fprintf('  进度: %d/%d (%.1f%%)\n', count, total_coarse, 100*count/total_coarse);
            end
            
            % 快速测试（只测试前两个模式，减少迭代）
            [is_valid, ~] = quick_test_gildl(data_modes, n_atoms, sparsity, lambdaProtect);
            
            if is_valid
                valid_coarse = [valid_coarse; n_atoms, sparsity, lambdaProtect];
                fprintf('    ✓ 粗略有效: n_atoms=%d, sparsity=%d, lambda=%.1e\n', ...
                        n_atoms, sparsity, lambdaProtect);
            end
        end
    end
end

coarse_time = toc;
fprintf('粗略筛选完成，耗时: %.1f秒\n', coarse_time);
fprintf('粗略有效组合数: %d/%d (%.1f%%)\n', size(valid_coarse, 1), total_coarse, ...
        100*size(valid_coarse, 1)/total_coarse);

if size(valid_coarse, 1) == 0
    fprintf('❌ 粗略筛选未找到有效组合，建议检查算法或数据\n');
    return;
end

%% 第二阶段：基于粗略结果的精细搜索
fprintf('\n第二阶段：精细搜索...\n');

% 分析粗略结果，确定精细搜索范围
n_atoms_valid = unique(valid_coarse(:, 1));
sparsity_valid = unique(valid_coarse(:, 2));
lambda_min = min(valid_coarse(:, 3));
lambda_max = max(valid_coarse(:, 3));

fprintf('基于粗略结果确定精细搜索范围:\n');
fprintf('  n_atoms: %s\n', mat2str(n_atoms_valid));
fprintf('  sparsity: %s\n', mat2str(sparsity_valid));
fprintf('  lambdaProtect: [%.2e, %.2e]\n', lambda_min, lambda_max);

% 扩展精细搜索范围
n_atoms_fine = [];
for val = n_atoms_valid'
    n_atoms_fine = [n_atoms_fine, max(30, val-5):5:min(100, val+5)];
end
n_atoms_fine = unique(n_atoms_fine);

sparsity_fine = [];
for val = sparsity_valid'
    sparsity_fine = [sparsity_fine, max(1, val-1):min(7, val+1)];
end
sparsity_fine = unique(sparsity_fine);

% lambda范围扩展一个数量级
lambda_exp_min = log10(lambda_min) - 1;
lambda_exp_max = log10(lambda_max) + 1;
lambdaProtect_fine = logspace(lambda_exp_min, lambda_exp_max, 15);

fprintf('扩展后的精细搜索范围:\n');
fprintf('  n_atoms: %d个点 %s\n', length(n_atoms_fine), mat2str(n_atoms_fine));
fprintf('  sparsity: %d个点 %s\n', length(sparsity_fine), mat2str(sparsity_fine));
fprintf('  lambdaProtect: %d个点 [%.2e, %.2e]\n', length(lambdaProtect_fine), ...
        min(lambdaProtect_fine), max(lambdaProtect_fine));

total_fine = length(n_atoms_fine) * length(sparsity_fine) * length(lambdaProtect_fine);
fprintf('精细搜索组合数: %d\n', total_fine);

valid_fine = [];
tic;
count = 0;

for i = 1:length(n_atoms_fine)
    for j = 1:length(sparsity_fine)
        for k = 1:length(lambdaProtect_fine)
            count = count + 1;
            
            n_atoms = n_atoms_fine(i);
            sparsity = sparsity_fine(j);
            lambdaProtect = lambdaProtect_fine(k);
            
            if mod(count, 100) == 0
                fprintf('  进度: %d/%d (%.1f%%)\n', count, total_fine, 100*count/total_fine);
            end
            
            % 完整测试（所有模式）
            [is_valid, ~] = test_gildl_parameters_full(data_modes, n_atoms, sparsity, lambdaProtect);
            
            if is_valid
                valid_fine = [valid_fine; n_atoms, sparsity, lambdaProtect];
                if size(valid_fine, 1) <= 20
                    fprintf('    ✓ 精细有效: n_atoms=%d, sparsity=%d, lambda=%.2e\n', ...
                            n_atoms, sparsity, lambdaProtect);
                end
            end
        end
    end
end

fine_time = toc;
total_time = coarse_time + fine_time;

%% 结果总结
fprintf('\n========== 筛选结果总结 ==========\n');
fprintf('总筛选时间: %.1f分钟\n', total_time/60);
fprintf('粗略筛选: %d/%d有效 (%.1f%%), 耗时%.1f秒\n', ...
        size(valid_coarse, 1), total_coarse, 100*size(valid_coarse, 1)/total_coarse, coarse_time);
fprintf('精细筛选: %d/%d有效 (%.1f%%), 耗时%.1f秒\n', ...
        size(valid_fine, 1), total_fine, 100*size(valid_fine, 1)/total_fine, fine_time);

if size(valid_fine, 1) > 0
    fprintf('\n✅ 找到 %d 个有效参数组合:\n', size(valid_fine, 1));
    fprintf('%-8s %-10s %-15s\n', 'n_atoms', 'sparsity', 'lambdaProtect');
    fprintf('%-8s %-10s %-15s\n', '-------', '--------', '-------------');
    
    display_count = min(15, size(valid_fine, 1));
    for i = 1:display_count
        fprintf('%-8d %-10d %-15.2e\n', valid_fine(i, 1), valid_fine(i, 2), valid_fine(i, 3));
    end
    
    if size(valid_fine, 1) > 15
        fprintf('... 还有 %d 个有效组合\n', size(valid_fine, 1) - 15);
    end
    
    % 推荐参数
    fprintf('\n========== 推荐参数 ==========\n');
    mid_idx = ceil(size(valid_fine, 1) / 2);
    recommended = valid_fine(mid_idx, :);
    
    fprintf('推荐参数 (中位数选择):\n');
    fprintf('  n_atoms = %d\n', recommended(1));
    fprintf('  sparsity = %d\n', recommended(2));
    fprintf('  lambdaProtect = %.2e\n', recommended(3));
    
    % 保守参数（较小的lambda）
    [~, min_lambda_idx] = min(valid_fine(:, 3));
    conservative = valid_fine(min_lambda_idx, :);
    
    fprintf('\n保守参数 (最小lambda):\n');
    fprintf('  n_atoms = %d\n', conservative(1));
    fprintf('  sparsity = %d\n', conservative(2));
    fprintf('  lambdaProtect = %.2e\n', conservative(3));
    
else
    fprintf('\n❌ 精细筛选未找到有效组合\n');
    fprintf('建议:\n');
    fprintf('  1. 进一步扩大搜索范围\n');
    fprintf('  2. 检查数据预处理\n');
    fprintf('  3. 调整算法参数（如eps_norm）\n');
end

%% 保存结果
results = struct();
results.valid_coarse = valid_coarse;
results.valid_fine = valid_fine;
results.search_ranges = struct('n_atoms_fine', n_atoms_fine, ...
                              'sparsity_fine', sparsity_fine, ...
                              'lambdaProtect_fine', lambdaProtect_fine);
results.timing = struct('coarse_time', coarse_time, 'fine_time', fine_time, 'total_time', total_time);

save('gildl_cuihua_quick_screening_results.mat', 'results');
fprintf('\n筛选结果已保存到: gildl_cuihua_quick_screening_results.mat\n');

fprintf('\n🎉 快速参数筛选完成！\n');

%% 辅助函数：快速测试（只测试前两个模式）
function [is_valid, error_info] = quick_test_gildl(data_modes, n_atoms, sparsity, lambdaProtect)
    try
        eps_norm = 1e-6;
        
        % Mode 1
        Y1 = data_modes{1};
        [m, ~] = size(Y1);
        
        if n_atoms > m
            is_valid = false;
            error_info = 'n_atoms_too_large';
            return;
        end
        
        D0 = randn(m, n_atoms);
        D0 = D0 ./ vecnorm(D0);
        
        [Dict1, ~] = ksvd_simple(Y1, D0, sparsity, 5);  % 只迭代5次
        
        if any(isnan(Dict1(:)))
            is_valid = false;
            error_info = 'ksvd_nan';
            return;
        end
        
        % Mode 2 (简化版)
        Y2 = data_modes{2};
        [~, N] = size(Y2);
        
        D = Dict1;
        Ws = zeros(n_atoms);
        
        % 只做一次迭代
        X = zeros(n_atoms, N);
        for j = 1:N
            X(:, j) = omp(D, Y2(:, j), sparsity);
        end
        
        if any(isnan(X(:)))
            is_valid = false;
            error_info = 'omp_nan';
            return;
        end
        
        % 简化的字典更新
        denominator = X * X' + Ws + eps_norm * eye(n_atoms);
        if rcond(denominator) < 1e-12
            is_valid = false;
            error_info = 'singular_matrix';
            return;
        end
        
        D_new = (Dict1 * Ws + Y2 * X') / denominator;
        D_new = D_new ./ vecnorm(D_new);
        
        if any(isnan(D_new(:)))
            is_valid = false;
            error_info = 'update_nan';
            return;
        end
        
        is_valid = true;
        error_info = 'success';
        
    catch ME
        is_valid = false;
        error_info = ME.identifier;
    end
end

%% 辅助函数：完整测试
function [is_valid, error_info] = test_gildl_parameters_full(data_modes, n_atoms, sparsity, lambdaProtect)
    try
        all_modes = 3;
        iter_KSVD = 10;
        iter_gildl = 5;
        eps_norm = 1e-6;
        
        Ws = zeros(n_atoms);
        
        % Mode 1
        Y1 = data_modes{1};
        [m, ~] = size(Y1);
        
        if n_atoms > m
            is_valid = false;
            error_info = 'dimension_mismatch';
            return;
        end
        
        D0 = randn(m, n_atoms);
        D0 = D0 ./ vecnorm(D0);
        
        [Dict1, ~] = ksvd_simple(Y1, D0, sparsity, iter_KSVD);
        
        if any(isnan(Dict1(:)))
            is_valid = false;
            error_info = 'ksvd_nan';
            return;
        end
        
        D_prev = Dict1;
        
        % Mode 2-3
        for s = 2:all_modes
            Y_new = data_modes{s};
            [~, N] = size(Y_new);
            
            D = D_prev;
            omega_s = zeros(n_atoms, 1);
            
            for it = 1:iter_gildl
                X = zeros(n_atoms, N);
                for j = 1:N
                    X(:, j) = omp(D, Y_new(:, j), sparsity);
                end
                
                if any(isnan(X(:)))
                    is_valid = false;
                    error_info = 'omp_nan';
                    return;
                end
                
                denominator = X * X' + Ws + eps_norm * eye(n_atoms);
                if rcond(denominator) < 1e-12
                    is_valid = false;
                    error_info = 'singular_matrix';
                    return;
                end
                
                D = (D_prev * Ws + Y_new * X') / denominator;
                D = D ./ vecnorm(D);
                
                if any(isnan(D(:)))
                    is_valid = false;
                    error_info = 'update_nan';
                    return;
                end
                
                omega_s = omega_s + 1;  % 简化的omega更新
            end
            
            w_tilde = omega_s ./ (vecnorm(D - D_prev).^2 + eps_norm);
            
            if any(isnan(w_tilde))
                is_valid = false;
                error_info = 'w_tilde_nan';
                return;
            end
            
            Ws = Ws + lambdaProtect * diag(w_tilde);
            
            if any(isnan(Ws(:)))
                is_valid = false;
                error_info = 'weight_nan';
                return;
            end
            
            D_prev = D;
        end
        
        is_valid = true;
        error_info = 'success';
        
    catch ME
        is_valid = false;
        error_info = ME.identifier;
    end
end
