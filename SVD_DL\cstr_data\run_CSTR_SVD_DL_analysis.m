%% CSTR数据集SVD_DL方法一键运行脚本

fprintf('========== CSTR数据集SVD_DL方法分析 ==========\n');
fprintf('数据集: CSTR (3个模式)\n');
fprintf('方法: SVD_DL (主空间锁定字典学习)\n');
fprintf('目标: 学习每一阶段的字典矩阵并分析性能\n\n');

%% 1. 检查必要文件
fprintf('🔍 检查必要文件...\n');

% 检查数据文件
if exist('CSTR_3modes_train_data.mat', 'file')
    fprintf('   ✓ 找到CSTR训练数据文件\n');
else
    fprintf('   ❌ 缺少CSTR训练数据文件: CSTR_3modes_train_data.mat\n');
    return;
end

% 检查脚本文件
required_scripts = {'learn_DL_CSTR.m', 'analyze_CSTR_performance.m'};
missing_scripts = {};

for i = 1:length(required_scripts)
    if exist(required_scripts{i}, 'file')
        fprintf('   ✓ %s\n', required_scripts{i});
    else
        fprintf('   ❌ %s (缺失)\n', required_scripts{i});
        missing_scripts{end+1} = required_scripts{i};
    end
end

if ~isempty(missing_scripts)
    fprintf('   请确保所有必要的脚本文件都在当前目录中。\n');
    return;
end

fprintf('✅ 文件检查完成\n\n');

%% 2. 运行SVD_DL字典学习
fprintf('🚀 开始SVD_DL字典学习...\n');

try
    tic;
    
    % 检查是否已有训练结果
    if exist('CSTR_SVD_DL_results.mat', 'file')
        fprintf('   发现已有训练结果，跳过训练步骤\n');
        fprintf('   如需重新训练，请删除 CSTR_SVD_DL_results.mat 文件\n');
    else
        % 运行字典学习
        learn_DL_CSTR;
    end
    
    training_time = toc;
    fprintf('   ✅ SVD_DL字典学习完成，耗时: %.1f秒\n', training_time);
    
catch ME
    fprintf('   ❌ SVD_DL字典学习失败: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('   错误位置: %s 第%d行\n', ME.stack(1).name, ME.stack(1).line);
    end
    return;
end

%% 3. 运行性能分析
fprintf('\n📊 开始性能分析...\n');

try
    tic;
    
    % 运行性能分析
    analyze_CSTR_performance;
    
    analysis_time = toc;
    fprintf('   ✅ 性能分析完成，耗时: %.1f秒\n', analysis_time);
    
catch ME
    fprintf('   ❌ 性能分析失败: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('   错误位置: %s 第%d行\n', ME.stack(1).name, ME.stack(1).line);
    end
    return;
end

%% 4. 加载并显示关键结果
fprintf('\n📋 关键结果总览:\n');
fprintf('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');

try
    % 加载分析结果
    load('CSTR_SVD_DL_performance_analysis.mat', 'results_cstr_svd_dl');
    
    % 显示基本信息
    n_modes = length(results_cstr_svd_dl.Dictionary_history);
    dict_size = size(results_cstr_svd_dl.Dictionary_history{1});
    
    fprintf('📊 基本信息:\n');
    fprintf('   数据集: CSTR (3个模式)\n');
    fprintf('   字典大小: %dx%d\n', dict_size(1), dict_size(2));
    fprintf('   训练模式数: %d\n', n_modes);
    
    % 显示NMSC结果
    if isfield(results_cstr_svd_dl, 'NMSC_history') && ~isempty(results_cstr_svd_dl.NMSC_history)
        NMSC_history = results_cstr_svd_dl.NMSC_history;
        fprintf('\n📈 NMSC (归一化均方变化度):\n');
        for i = 1:length(NMSC_history)
            fprintf('   模式%d→%d: %.6f\n', i, i+1, NMSC_history(i));
        end
        fprintf('   平均值: %.6f\n', mean(NMSC_history));
    end
    
    % 显示子空间夹角结果
    if isfield(results_cstr_svd_dl, 'Subspace_angle_history') && ~isempty(results_cstr_svd_dl.Subspace_angle_history)
        Subspace_angle_history = results_cstr_svd_dl.Subspace_angle_history;
        valid_angles = Subspace_angle_history(~isnan(Subspace_angle_history));
        
        if ~isempty(valid_angles)
            fprintf('\n📐 主方向子空间夹角:\n');
            for i = 1:length(Subspace_angle_history)
                if ~isnan(Subspace_angle_history(i))
                    fprintf('   模式%d→%d: %.4f弧度 (%.2f°)\n', i, i+1, ...
                            Subspace_angle_history(i), Subspace_angle_history(i)*180/pi);
                end
            end
            fprintf('   平均值: %.4f弧度 (%.2f°)\n', mean(valid_angles), mean(valid_angles)*180/pi);
        end
    end
    
    % 显示主空间维度
    if isfield(results_cstr_svd_dl, 'subspace_dims') && ~isempty(results_cstr_svd_dl.subspace_dims)
        subspace_dims = results_cstr_svd_dl.subspace_dims;
        fprintf('\n🔍 主空间维度演化:\n');
        for i = 1:length(subspace_dims)
            fprintf('   模式%d: %d维\n', i, subspace_dims(i));
        end
    end
    
    % 显示字典质量指标
    if isfield(results_cstr_svd_dl, 'coherence_history') && isfield(results_cstr_svd_dl, 'reconstruction_errors')
        fprintf('\n🎯 字典质量指标:\n');
        fprintf('   平均相关性: %.4f\n', mean(results_cstr_svd_dl.coherence_history));
        fprintf('   平均重构误差: %.2e\n', mean(results_cstr_svd_dl.reconstruction_errors));
        
        if isfield(results_cstr_svd_dl, 'condition_number_history')
            fprintf('   平均条件数: %.2e\n', mean(results_cstr_svd_dl.condition_number_history));
        end
    end
    
catch ME
    fprintf('   ❌ 结果加载失败: %s\n', ME.message);
end

%% 5. 结果解释
fprintf('\n💡 结果解释:\n');
fprintf('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');

if exist('NMSC_history', 'var') && ~isempty(NMSC_history)
    avg_nmsc = mean(NMSC_history);
    fprintf('🔸 NMSC平均值 %.6f 说明:\n', avg_nmsc);
    if avg_nmsc < 0.1
        fprintf('   字典在模式转换中变化很小，SVD_DL保持了高度稳定性\n');
        fprintf('   主空间锁定机制有效地保持了已学习的特征\n');
    elseif avg_nmsc < 0.5
        fprintf('   字典在模式转换中有适度的变化\n');
        fprintf('   SVD_DL在稳定性和适应性之间取得了平衡\n');
    else
        fprintf('   字典在模式转换中变化较大\n');
        fprintf('   SVD_DL具有强适应性，能够学习新的特征\n');
    end
end

if exist('valid_angles', 'var') && ~isempty(valid_angles)
    avg_angle = mean(valid_angles);
    avg_angle_deg = avg_angle * 180 / pi;
    fprintf('\n🔸 子空间夹角平均值 %.4f弧度 (%.2f°) 说明:\n', avg_angle, avg_angle_deg);
    if avg_angle < pi/6  % 30度
        fprintf('   主方向在模式转换中变化较小，主空间锁定效果显著\n');
        fprintf('   SVD_DL有效保持了监测的一致性\n');
    elseif avg_angle < pi/3  % 60度
        fprintf('   主方向在模式转换中有中等程度的变化\n');
        fprintf('   SVD_DL在保持主方向和适应新模式之间取得了平衡\n');
    else
        fprintf('   主方向在模式转换中变化较大\n');
        fprintf('   可能需要调整主空间锁定参数\n');
    end
end

%% 6. 输出文件总结
fprintf('\n📁 输出文件总结:\n');
fprintf('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');

output_files = {
    'CSTR_SVD_DL_results.mat', '字典学习结果';
    'CSTR_SVD_DL_performance_analysis.mat', '性能分析结果';
    'CSTR_SVD_DL_evolution.fig', '字典演化可视化';
    'CSTR_SVD_DL_comprehensive_analysis.fig', '综合性能分析图'
};

fprintf('生成的文件:\n');
for i = 1:size(output_files, 1)
    filename = output_files{i, 1};
    description = output_files{i, 2};
    
    if exist(filename, 'file')
        file_info = dir(filename);
        fprintf('   ✓ %s (%.1f KB) - %s\n', filename, file_info.bytes/1024, description);
    else
        fprintf('   ❌ %s - %s (未生成)\n', filename, description);
    end
end

%% 7. 使用建议
fprintf('\n💡 使用建议:\n');
fprintf('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
fprintf('1. 查看可视化结果:\n');
fprintf('   - 打开 CSTR_SVD_DL_evolution.fig 查看字典演化过程\n');
fprintf('   - 打开 CSTR_SVD_DL_comprehensive_analysis.fig 查看详细性能分析\n');
fprintf('\n2. 进一步分析:\n');
fprintf('   - 加载 CSTR_SVD_DL_results.mat 获取字典矩阵\n');
fprintf('   - 加载 CSTR_SVD_DL_performance_analysis.mat 获取性能指标\n');
fprintf('\n3. 参数调优:\n');
fprintf('   - 如需调整参数，修改 learn_DL_CSTR.m 中的参数设置\n');
fprintf('   - 删除结果文件后重新运行以应用新参数\n');

fprintf('\n🎉 CSTR数据集SVD_DL方法分析完成！\n');
fprintf('📊 请查看生成的图表以获得更详细的信息。\n');

%% 8. 总耗时统计
total_time = training_time + analysis_time;
fprintf('\n⏱️  总耗时统计:\n');
fprintf('   字典学习: %.1f秒\n', training_time);
fprintf('   性能分析: %.1f秒\n', analysis_time);
fprintf('   总计: %.1f秒\n', total_time);

fprintf('\n========== 分析完成 ==========\n');
