# 基于现有代码定义的NMSC和子空间夹角分析更新

## 🔄 主要修改内容

根据 `DL_with_close_solution.m` 中的现有代码定义，我已经更新了所有相关文件以匹配你的实现方式。

### 📊 NMSC定义更新

**原定义** (相干性方法):
```matlab
coherence_matrix = abs(D_new' * D_old);
max_coherence = max(coherence_matrix, [], 2);
NMSC = mean(max_coherence.^2);
```

**新定义** (按照你的代码):
```matlab
epsilon = 1e-8;
num = (D_new - D_old).^2;
den = D_old.^2 + epsilon;
NMSC = mean(num(:) ./ den(:));
```

**含义变化**:
- **原来**: 衡量字典间的相干性，值域[0,1]，越大越相似
- **现在**: 衡量字典的归一化均方变化度，值域[0,+∞)，越小越稳定

### 📐 子空间夹角定义更新

**原定义** (主角度方法):
```matlab
[~, sigma, ~] = svd(U_old' * U_new);
principal_angles = acos(min(diag(sigma), 1));
subspace_angle = max(principal_angles) * 180 / pi;  % 度
```

**新定义** (按照你的代码):
```matlab
subspace_angle = subspace(U_old, U_new);  % 弧度
```

**含义变化**:
- **原来**: 返回度数，使用主角度的最大值
- **现在**: 返回弧度，使用MATLAB内置函数

## 📁 更新的文件列表

### 1. `compute_nmsc_and_angles.m`
- ✅ 更新NMSC计算公式
- ✅ 使用MATLAB的subspace函数
- ✅ 返回字典变化统计信息

### 2. `visualize_dictionary_evolution.m`
- ✅ 添加字典变化热图 (差分热图、绝对变化热图)
- ✅ 更新数值输出格式 (弧度和度数)
- ✅ 添加相对变化热图

### 3. `analyze_optimal_parameters.m`
- ✅ 更新结果解释 (NMSC值越小越稳定)
- ✅ 子空间夹角以弧度为单位
- ✅ 调整解释标准

### 4. `run_optimal_analysis.m`
- ✅ 更新控制台输出格式
- ✅ 修正结果解释逻辑
- ✅ 同时显示弧度和度数

### 5. `OPTIMAL_ANALYSIS_README.md`
- ✅ 更新指标定义和解释标准
- ✅ 修正值域和含义说明

## 🎯 新的解释标准

### NMSC (归一化均方变化度)
```
< 0.1    : 字典变化很小，高度稳定
0.1-0.5  : 适度变化，平衡稳定性和适应性  
> 0.5    : 变化较大，强适应性
```

### 子空间夹角 (弧度)
```
< π/6 (30°)     : 主方向变化小，高稳定性
π/6-π/3 (30°-60°) : 中等变化，平衡性能
> π/3 (60°)     : 变化大，强适应性
```

## 📊 新增可视化内容

### 字典变化热图
1. **差分热图**: `imagesc(D_last - D_1)`
2. **绝对变化热图**: `imagesc(abs(D_last - D_1))`
3. **相对变化热图**: `imagesc((D_last - D_1)^2 ./ (D_1^2 + ε))`
4. **各模式转换热图**: 显示每个模式转换的变化

### 输出格式更新
```
📐 主方向子空间夹角 (弧度):
   Mode 1→2: 0.3142弧度 (18.00°)
   Mode 2→3: 0.2618弧度 (15.00°)
   ...
   平均值: 0.2880弧度 (16.50°)
```

## 🚀 使用方法

### 快速运行 (推荐)
```matlab
run('run_optimal_analysis.m')
```

### 分步运行
```matlab
% 1. 主分析
run('analyze_optimal_parameters.m')

% 2. 可视化
load('optimal_parameter_analysis.mat', 'results_optimal');
visualize_dictionary_evolution(results_optimal);
```

## 📈 预期输出示例

```
📊 NMSC (归一化均方变化度):
   Mode 1→2: 0.1234
   Mode 2→3: 0.0987
   Mode 3→4: 0.1156
   Mode 4→5: 0.1045
   平均值: 0.1106

📐 主方向子空间夹角 (弧度):
   Mode 1→2: 0.2618弧度 (15.00°)
   Mode 2→3: 0.3491弧度 (20.00°)
   Mode 3→4: 0.2094弧度 (12.00°)
   Mode 4→5: 0.2793弧度 (16.00°)
   平均值: 0.2749弧度 (15.75°)

🔸 NMSC平均值 0.1106 说明 (归一化均方变化度):
   字典在模式转换中有适度的变化
   在稳定性和适应性之间取得了平衡

🔸 子空间夹角平均值 0.2749弧度 (15.75°) 说明:
   主方向在模式转换中变化较小，保持了良好的稳定性
   这有利于保持监测的一致性
```

## 🎯 与最优参数的匹配

基于最优参数 `λ=1e+03, n_atoms=20, sparsity=2`:

### 预期特征
- **高λ值**: 强主空间保护 → 低NMSC值，小子空间夹角
- **小原子数**: 紧凑字典 → 相对稳定的变化
- **低稀疏度**: 高重构精度 → 精确的特征保持

### 预期性能指标
- **NMSC**: 预计 0.05-0.2 (适度稳定)
- **子空间夹角**: 预计 0.1-0.4弧度 (6°-23°，高稳定性)
- **FDR**: 1.0000 (完美检测)
- **FAR**: 0.0084 (极低误报)

## ✅ 验证清单

运行前确保:
- ✅ 所有mode数据文件存在
- ✅ `ksvd_simple_silent.m` 可用
- ✅ `omp.m` 函数可用
- ✅ MATLAB版本支持subspace函数

## 🔍 关键差异总结

| 指标 | 原定义 | 新定义 (按现有代码) |
|------|--------|---------------------|
| NMSC | 相干性度量 [0,1] | 变化度量 [0,+∞) |
| 解释 | 值大=相似 | 值小=稳定 |
| 夹角 | 主角度最大值(度) | subspace函数(弧度) |
| 热图 | 相干性矩阵 | 字典差分热图 |

现在所有文件都已更新以匹配你现有代码的定义和计算方法！
