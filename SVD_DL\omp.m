function x = omp(D, y, sparsity)
% D: [m x K]
% y: [m x 1]
% sparsity: 稀疏度
K = size(D,2);
x = zeros(K,1);
r = y;
idx_set = [];
for s = 1:sparsity
    proj = D' * r;        % [K x 1]
    [~, idx] = max(abs(proj));
    if ismember(idx, idx_set)
        break;
    end
    idx_set = [idx_set, idx];
    Ds = D(:, idx_set);
    xs = Ds \ y;
    r = y - Ds*xs;
end
if ~isempty(idx_set)
    x(idx_set) = xs;
end
end
