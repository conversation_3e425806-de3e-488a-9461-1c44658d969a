% =========================================================
%  compute_dictionary_dmcdl_cuihua.m
%  DMCDL方法应用于催化数据集 (data_selected_F1~F3)
%  DM‑CDL : Dictionary Learning for Cuihua Modes 1‑3
% =========================================================
clear; clc; rng(42);

fprintf('========== DMCDL方法应用于催化数据集 ==========\n');
fprintf('数据集: 催化数据 (data_selected_F1~F3)\n');
fprintf('方法: DMCDL (双重记忆持续字典学习)\n\n');

%% -------- 超参数设置 --------
n_atoms  = 20;         % 字典原子数 (适应催化数据维度)
sparsity = 3;          % OMP 稀疏度
n_iter   = 50;         % 每个阶段迭代次数
lambda_1 = 1e-6;       % 初始正则系数
zeta     = 1e-12;      % 数值稳定性参数
M        = 10;         % 重放样本容量
alpha    = 0.99;       % KDE 阈值置信度

fprintf('参数设置:\n');
fprintf('  字典原子数: %d\n', n_atoms);
fprintf('  OMP稀疏度: %d\n', sparsity);
fprintf('  迭代次数: %d\n', n_iter);
fprintf('  记忆池容量: %d\n', M);

%% -------- 变量初始化 --------
Yh = [];               % 样本记忆池
W  = [];               % 参数记忆矩阵
Rtr = [];              % 重构误差阈值
D  = [];               % 字典

% 存储历史记录
Dictionary_history = cell(3, 1);
Weight_history = cell(3, 1);
Memory_history = cell(3, 1);
Threshold_history = zeros(3, 1);

%% ========================================================
%                主循环：F1 → F2 → F3
%% ========================================================
for mode = 1:3
    fprintf('\n===== 催化工况 F%d =====\n', mode);
    
    % 加载催化数据
    data_file = sprintf('data_selected_F%d.mat', mode);
    if exist(data_file, 'file')
        load(data_file, 'data_selected');
        Yk = cell2mat(data_selected)';           % [特征维度 × 样本数]
        fprintf('  加载%s: %dx%d\n', data_file, size(Yk,1), size(Yk,2));
    else
        error('无法找到文件: %s\n请确保催化数据文件在当前目录中。', data_file);
    end
    
    [m, nk] = size(Yk);
    fprintf('  F%d数据大小: %dx%d\n', mode, m, nk);

    % ---------- 拼接当前数据和记忆池 ----------
    if isempty(Yh)
        Y_all = Yk;                              % 第一个模式，无记忆池
        fprintf('  第一个模式，无记忆池拼接\n');
    else
        Y_all = [Yk, Yh];                        % Y' = Yk ∪ Yh
        fprintf('  拼接数据: 当前%d + 记忆%d = 总计%d样本\n', ...
                size(Yk,2), size(Yh,2), size(Y_all,2));
    end

    % =====================================================
    %  1) 字典学习
    % =====================================================
    if mode == 1
        % ---------- F1: 初始 K‑SVD ----------
        fprintf('  执行初始K-SVD字典学习...\n');
        D0 = randn(m, n_atoms);  
        D0 = D0 ./ vecnorm(D0);                  % 归一化初始字典
        [D, W] = ksvd_with_importance(D0, Yk, n_iter, sparsity, lambda_1, zeta);
        fprintf('  初始字典学习完成\n');
    else
        % ---------- F2/F3: 增量交替优化 ----------
        fprintf('  执行增量DMCDL学习...\n');
        [D, W] = incremental_dmcdl(D, W, Y_all, n_iter, sparsity, zeta);
        fprintf('  增量学习完成\n');
    end

    % =====================================================
    %  2) 计算重构误差阈值
    % =====================================================
    fprintf('  计算重构误差阈值...\n');
    X_all = omp(D, Y_all, sparsity);
    R_all = vecnorm(Y_all - D * X_all).^2;
    
    % 使用KDE估计阈值
    try
        pd = fitdist(R_all', 'Kernel', 'Bandwidth', []);
        Rtr = icdf(pd, alpha);
    catch
        % 如果KDE失败，使用分位数方法
        Rtr = quantile(R_all, alpha);
        fprintf('  警告: KDE失败，使用分位数方法\n');
    end
    
    fprintf('  重构误差阈值: %.6f\n', Rtr);

    % =====================================================
    %  3) 构建样本记忆池 (代表性 + 判别性)
    % =====================================================
    fprintf('  构建样本记忆池...\n');
    
    % 计算当前模式的重构误差
    Xk = X_all(:, 1:size(Yk,2));               % 对应当前模式的稀疏编码
    Rk = vecnorm(Yk - D * Xk).^2;              % 当前模式重构误差

    % --- 代表性指标 ρ_i (距离均值的距离) ---
    mu = mean(Yk, 2);                          % 当前模式数据均值
    rho = vecnorm(Yk - mu).^2;                 % 每个样本到均值的距离
    [~, idx_rep] = mink(rho, floor(M/2));      % 选择最代表性的样本

    % --- 判别性指标 Dis_i (距离阈值的距离) ---
    Dis = abs(Rk - Rtr);                       % 距离重构误差阈值的距离
    remaining_slots = M - numel(idx_rep);
    if remaining_slots > 0
        [~, idx_dis] = mink(Dis, min(remaining_slots, length(Dis)));
    else
        idx_dis = [];
    end

    % 合并代表性和判别性样本索引
    idx_mem = unique([idx_rep, idx_dis]);
    if length(idx_mem) > M
        idx_mem = idx_mem(1:M);                % 限制记忆池大小
    end
    
    Yh = Yk(:, idx_mem);                       % 更新记忆池
    fprintf('  记忆池更新: 选择了%d个样本 (代表性%d + 判别性%d)\n', ...
            length(idx_mem), length(idx_rep), length(idx_dis));

    % =====================================================
    %  4) 保存当前模式结果
    % =====================================================
    Dictionary_history{mode} = D;
    Weight_history{mode} = W;
    Memory_history{mode} = Yh;
    Threshold_history(mode) = Rtr;
    
    fprintf('  F%d完成: 字典%dx%d, 阈值=%.6f, 记忆池=%d样本\n', ...
            mode, size(D,1), size(D,2), Rtr, size(Yh,2));
end

%% -------- 保存模型和结果 --------
fprintf('\n保存DMCDL催化数据集结果...\n');

% 保存最终模型
D_final_cuihua = D;
W_final_cuihua = W;
Yh_final_cuihua = Yh;
Rtr_final_cuihua = Rtr;

save('dmcdl_cuihua_model.mat', 'D_final_cuihua', 'W_final_cuihua', ...
     'Yh_final_cuihua', 'Rtr_final_cuihua');
fprintf('✓ 最终模型已保存: dmcdl_cuihua_model.mat\n');

% 保存完整历史记录
dmcdl_cuihua_results = struct();
dmcdl_cuihua_results.method = 'DMCDL_Cuihua';
dmcdl_cuihua_results.Dictionary_history = Dictionary_history;
dmcdl_cuihua_results.Weight_history = Weight_history;
dmcdl_cuihua_results.Memory_history = Memory_history;
dmcdl_cuihua_results.Threshold_history = Threshold_history;
dmcdl_cuihua_results.parameters = struct('n_atoms', n_atoms, 'sparsity', sparsity, ...
                                         'n_iter', n_iter, 'lambda_1', lambda_1, ...
                                         'M', M, 'alpha', alpha);

save('dmcdl_cuihua_results.mat', 'dmcdl_cuihua_results');
fprintf('✓ 完整结果已保存: dmcdl_cuihua_results.mat\n');

%% -------- 结果分析 --------
fprintf('\n========== 结果分析 ==========\n');

% 计算NMSC
NMSC_history = zeros(2, 1);
epsilon = 1e-8;

for i = 2:3
    D_prev = Dictionary_history{i-1};
    D_curr = Dictionary_history{i};
    deltaD = D_curr - D_prev;
    NMSC_history(i-1) = mean((deltaD(:).^2) ./ (D_prev(:).^2 + epsilon));
    fprintf('  NMSC (F%d→F%d): %.6f\n', i-1, i, NMSC_history(i-1));
end

% 权重矩阵分析
fprintf('\n权重矩阵演化:\n');
for i = 1:3
    W_curr = Weight_history{i};
    weight_norm = norm(W_curr, 'fro');
    weight_trace = trace(W_curr);
    fprintf('  F%d: 权重范数=%.4f, 迹=%.4f\n', i, weight_norm, weight_trace);
end

% 记忆池分析
fprintf('\n记忆池演化:\n');
for i = 1:3
    Yh_curr = Memory_history{i};
    memory_size = size(Yh_curr, 2);
    fprintf('  F%d: 记忆池大小=%d, 阈值=%.6f\n', i, memory_size, Threshold_history(i));
end

fprintf('\n🎉 DMCDL催化数据集字典学习完成！\n');
fprintf('平均NMSC: %.6f\n', mean(NMSC_history));
fprintf('最终字典大小: %dx%d\n', size(D_final_cuihua));
fprintf('最终记忆池大小: %d\n', size(Yh_final_cuihua, 2));

%% ========================================================
%                   --- 函 数 定 义 ---
%% ========================================================

% ---------- K‑SVD + 优化敏感度权重 (Mode 1) ----------
function [D, W] = ksvd_with_importance(D, Y, n_iter, sparsity, lambda1, zeta)
    n_atoms = size(D,2);  
    D_init = D;  
    omega = zeros(n_atoms,1);
    
    for it = 1:n_iter
        X = omp(D, Y, sparsity);
        for i = 1:n_atoms
            idx = find(X(i,:) ~= 0);  
            if isempty(idx), continue; end
            
            R = Y(:,idx) - D*X(:,idx) + D(:,i)*X(i,idx);
            Lb = norm(R,'fro')^2;
            [u,s,v] = svd(R,'econ');
            
            if ~isempty(u) && size(u,2) >= 1
                D(:,i) = u(:,1);
                X(i,idx) = s(1,1) * v(:,1)';
                Ra = R - D(:,i)*X(i,idx);
                La = norm(Ra,'fro')^2;
                omega(i) = omega(i) - (La - Lb);       % Σ ΔL_i^η
            end
        end
        
        % 归一化字典原子
        for i = 1:n_atoms
            if norm(D(:,i)) > 1e-12
                D(:,i) = D(:,i) / norm(D(:,i));
            end
        end
    end
    
    delta_d = sum((D - D_init).^2,1) + zeta;
    w = omega ./ delta_d';
    W = lambda1 * diag(w);
end

% ---------- 增量交替优化 (Modes ≥2) ----------
function [D, W_new] = incremental_dmcdl(D_prev, W_prev, Y, n_iter, sparsity, zeta)
    n_atoms = size(D_prev,2);  
    D0 = D_prev;  
    omega = zeros(n_atoms,1);
    
    for it = 1:n_iter
        X = omp(D_prev, Y, sparsity);                 % Step 9
        
        % Step 10 (闭式更新)
        G = X*X' + W_prev + 1e-12*eye(n_atoms);      % 添加正则化
        F = Y*X' + D_prev * W_prev;
        
        % 检查矩阵条件数
        if rcond(G) < 1e-12
            G = G + 1e-6*eye(n_atoms);
        end
        
        D = F / G;
        D = D ./ (vecnorm(D) + 1e-12);               % 安全归一化
        
        % 记录 ΔL (公式 19)
        Rb = Y - D_prev*X;  
        Ra = Y - D*X;
        deltaL = norm(Rb,'fro')^2 - norm(Ra,'fro')^2;
        omega = omega - deltaL / n_atoms;
        D_prev = D;
    end
    
    delta_d = sum((D - D0).^2,1) + zeta;
    w = omega ./ delta_d';
    W_new = diag(w);
end

% ---------- 极简 OMP ----------
function X = omp(D, Y, T)
    [~,N] = size(Y);  
    l = size(D,2);  
    X = zeros(l,N);
    
    for j = 1:N
        r = Y(:,j);  
        S = [];
        for t = 1:T
            if norm(r) < 1e-10, break; end
            
            proj = abs(D' * r);
            [~,k] = max(proj);  
            S = unique([S k]);
            
            if length(S) <= size(D,1)
                a = pinv(D(:,S)) * Y(:,j);
                r = Y(:,j) - D(:,S) * a;
            else
                break;
            end
        end
        if ~isempty(S) && exist('a', 'var')
            X(S,j) = a;
        end
    end
end
