%% 将SVD_DL的训练和测试数据复制到JMSDL目录
% 这样JMSDL方法就可以使用SVD_DL的数据进行训练和测试

fprintf('========== 复制SVD_DL数据到JMSDL目录 ==========\n');

% 检查SVD_DL目录是否存在
svd_dl_path = '../SVD_DL/';
if ~exist(svd_dl_path, 'dir')
    error('SVD_DL目录不存在: %s', svd_dl_path);
end

% 要复制的文件列表
file_types = {'train', 'test_normal', 'test_fault'};
modes = 1:5;

copied_files = 0;
total_files = length(file_types) * length(modes);

fprintf('开始复制数据文件...\n');

for mode = modes
    for i = 1:length(file_types)
        file_type = file_types{i};
        source_file = sprintf('%smode%d_%s.mat', svd_dl_path, mode, file_type);
        target_file = sprintf('mode%d_%s.mat', mode, file_type);
        
        if exist(source_file, 'file')
            try
                % 加载源文件
                data = load(source_file);
                
                % 保存到目标文件
                if strcmp(file_type, 'train')
                    train_data = data.train_data;
                    save(target_file, 'train_data');
                    fprintf('✓ 复制训练数据: %s -> %s (大小: %dx%d)\n', ...
                            source_file, target_file, size(train_data,1), size(train_data,2));
                elseif strcmp(file_type, 'test_normal')
                    test_normal_data = data.test_normal_data;
                    save(target_file, 'test_normal_data');
                    fprintf('✓ 复制正常测试数据: %s -> %s (大小: %dx%d)\n', ...
                            source_file, target_file, size(test_normal_data,1), size(test_normal_data,2));
                elseif strcmp(file_type, 'test_fault')
                    test_fault_data = data.test_fault_data;
                    save(target_file, 'test_fault_data');
                    fprintf('✓ 复制故障测试数据: %s -> %s (大小: %dx%d)\n', ...
                            source_file, target_file, size(test_fault_data,1), size(test_fault_data,2));
                end
                
                copied_files = copied_files + 1;
                
            catch ME
                fprintf('❌ 复制失败: %s -> %s (错误: %s)\n', ...
                        source_file, target_file, ME.message);
            end
        else
            fprintf('⚠️  源文件不存在: %s\n', source_file);
        end
    end
end

fprintf('\n========== 复制完成 ==========\n');
fprintf('成功复制: %d/%d 个文件\n', copied_files, total_files);

% 验证复制的数据
fprintf('\n========== 验证复制的数据 ==========\n');
for mode = 1:3  % 只验证前3个模式（JMSDL主要使用这些）
    train_file = sprintf('mode%d_train.mat', mode);
    if exist(train_file, 'file')
        load(train_file);
        fprintf('Mode %d 训练数据: %dx%d\n', mode, size(train_data,1), size(train_data,2));
    end
end

fprintf('\n现在JMSDL可以使用SVD_DL的数据进行训练了！\n');
fprintf('运行: Copy_of_JMSDL_new2_num.m\n');
