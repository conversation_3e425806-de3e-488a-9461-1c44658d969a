%% 简化版JMSDL运行脚本
% 直接使用当前目录中的训练数据运行JMSDL方法

fprintf('========== JMSDL方法运行 ==========\n');

%% 1. 检查数据文件
fprintf('1. 检查数据文件...\n');

required_files = {'mode1_train.mat', 'mode2_train.mat', 'mode3_train.mat', 'mode4_train.mat', 'mode5_train.mat'};
all_exist = true;

for i = 1:length(required_files)
    if exist(required_files{i}, 'file')
        fprintf('   ✓ %s\n', required_files{i});
    else
        fprintf('   ❌ %s (缺失)\n', required_files{i});
        all_exist = false;
    end
end

if ~all_exist
    error('缺少必要的数据文件，请确保mode1-5的训练数据在当前目录中。');
end

%% 2. 运行JMSDL字典学习
fprintf('\n2. 运行JMSDL字典学习...\n');
try
    tic;
    
    % 运行主要的JMSDL算法
    Copy_of_JMSDL_new2_num;
    
    elapsed_time = toc;
    fprintf('   ✓ JMSDL字典学习完成，耗时: %.2f秒\n', elapsed_time);
    
catch ME
    fprintf('   ❌ JMSDL运行失败: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('   错误位置: %s 第%d行\n', ME.stack(1).name, ME.stack(1).line);
    end
    return;
end

%% 3. 检查输出结果
fprintf('\n3. 检查输出结果...\n');

if exist('D_n_num_JMSDL.mat', 'file')
    load('D_n_num_JMSDL.mat', 'D_n');
    fprintf('   ✓ 最终字典已保存: D_n_num_JMSDL.mat\n');
    fprintf('   字典大小: %dx%d\n', size(D_n,1), size(D_n,2));

    % 显示字典的基本统计信息
    fprintf('   字典统计信息:\n');
    fprintf('     - 范数范围: [%.6f, %.6f]\n', min(vecnorm(D_n)), max(vecnorm(D_n)));
    fprintf('     - 元素范围: [%.6f, %.6f]\n', min(D_n(:)), max(D_n(:)));

else
    fprintf('   ❌ 未找到输出字典文件 D_n_num_JMSDL.mat\n');
end

%% 4. 显示使用的数据信息
fprintf('\n4. 数据使用情况:\n');
load('mode1_train.mat');
fprintf('   - 特征维度: %d\n', size(train_data,1));
fprintf('   - 每模式样本数: %d\n', size(train_data,2));
fprintf('   - 使用模式数: 5 (Mode 1-5)\n');
fprintf('   - 总样本数: %d\n', size(train_data,2) * 5);

fprintf('\n========== JMSDL运行完成 ==========\n');
fprintf('输出文件: D_n_num_JMSDL.mat (最终字典)\n');
fprintf('如需完整性能分析，请运行: run_jmsdl_analysis.m\n');
