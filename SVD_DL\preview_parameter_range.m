%% 参数范围预览 - 显示细化后的参数搜索范围

%% 定义参数范围
% lambda: 1e-6到10，步长为5倍
lambda_list = [1e-6, 5e-6, 1e-5, 5e-5, 1e-4, 5e-4, 1e-3, 5e-3, 1e-2, 5e-2, 1e-1, 5e-1, 1, 5, 10];
% n_atoms: 30到70，步长为2
n_atoms_list = 30:2:70;
% sparsity: 1,2,3
sparsity_list = [1, 2, 3];

%% 显示参数信息
fprintf('========== 细化参数搜索范围预览 ==========\n\n');

fprintf('📊 Lambda参数 (%d个值):\n', length(lambda_list));
fprintf('   范围: %.0e 到 %.0e\n', min(lambda_list), max(lambda_list));
fprintf('   值: ');
for i = 1:length(lambda_list)
    if i <= 10
        fprintf('%.0e ', lambda_list(i));
    elseif i == 11
        fprintf('... ');
    end
end
fprintf('\n   (显示前10个值)\n\n');

fprintf('📊 N_atoms参数 (%d个值):\n', length(n_atoms_list));
fprintf('   范围: %d 到 %d (步长: 2)\n', min(n_atoms_list), max(n_atoms_list));
fprintf('   值: ');
for i = 1:min(15, length(n_atoms_list))
    fprintf('%d ', n_atoms_list(i));
end
if length(n_atoms_list) > 15
    fprintf('... %d', n_atoms_list(end));
end
fprintf('\n\n');

fprintf('📊 Sparsity参数 (%d个值):\n', length(sparsity_list));
fprintf('   值: ');
for i = 1:length(sparsity_list)
    fprintf('%d ', sparsity_list(i));
end
fprintf('\n\n');

%% 计算总组合数
total_combinations = length(lambda_list) * length(n_atoms_list) * length(sparsity_list);
fprintf('🎯 总参数组合数: %d × %d × %d = %d\n', ...
        length(lambda_list), length(n_atoms_list), length(sparsity_list), total_combinations);

%% 时间估计
fprintf('\n⏱️  运行时间估计:\n');
fprintf('   假设每个组合平均耗时: 2-4分钟\n');
fprintf('   最短预计时间: %.1f 小时\n', total_combinations * 2 / 60);
fprintf('   最长预计时间: %.1f 小时\n', total_combinations * 4 / 60);
fprintf('   建议预留时间: %.1f 小时\n', total_combinations * 3 / 60);

%% 与原版本对比
original_combinations = 6 * 5 * 4;  % 原始版本的组合数
fprintf('\n📈 与原版本对比:\n');
fprintf('   原版本组合数: %d\n', original_combinations);
fprintf('   新版本组合数: %d\n', total_combinations);
fprintf('   增加倍数: %.1fx\n', total_combinations / original_combinations);

%% 参数密度分析
fprintf('\n🔍 参数密度分析:\n');
lambda_density = length(lambda_list) / (log10(max(lambda_list)) - log10(min(lambda_list)));
n_atoms_density = length(n_atoms_list) / (max(n_atoms_list) - min(n_atoms_list));
sparsity_density = length(sparsity_list) / (max(sparsity_list) - min(sparsity_list));

fprintf('   Lambda密度: %.1f 个值/对数单位\n', lambda_density);
fprintf('   N_atoms密度: %.2f 个值/单位\n', n_atoms_density);
fprintf('   Sparsity密度: %.1f 个值/单位\n', sparsity_density);

%% 可视化参数分布
figure('Position', [100, 100, 1200, 400]);

subplot(1,3,1);
semilogx(lambda_list, ones(size(lambda_list)), 'ro', 'MarkerSize', 8, 'MarkerFaceColor', 'r');
xlabel('Lambda');
ylabel('');
title('Lambda参数分布 (对数尺度)');
grid on;
set(gca, 'YTick', []);
ylim([0.5, 1.5]);

subplot(1,3,2);
plot(n_atoms_list, ones(size(n_atoms_list)), 'bo', 'MarkerSize', 8, 'MarkerFaceColor', 'b');
xlabel('N\_atoms');
ylabel('');
title('N\_atoms参数分布');
grid on;
set(gca, 'YTick', []);
ylim([0.5, 1.5]);

subplot(1,3,3);
plot(sparsity_list, ones(size(sparsity_list)), 'go', 'MarkerSize', 12, 'MarkerFaceColor', 'g');
xlabel('Sparsity');
ylabel('');
title('Sparsity参数分布');
grid on;
set(gca, 'YTick', []);
ylim([0.5, 1.5]);

sgtitle('细化参数搜索范围可视化', 'FontSize', 14);

%% 建议
fprintf('\n💡 建议:\n');
fprintf('   1. 首次运行建议使用较小的参数子集进行测试\n');
fprintf('   2. 可以先运行 parameter_search_quick.m 验证代码正确性\n');
fprintf('   3. 确保有足够的计算时间和存储空间\n');
fprintf('   4. 建议在计算资源充足时运行完整搜索\n');
fprintf('   5. 可以考虑分批运行，每次运行部分lambda范围\n');

%% 分批运行建议
fprintf('\n🔄 分批运行建议:\n');
n_batches = 3;
lambda_per_batch = ceil(length(lambda_list) / n_batches);
for batch = 1:n_batches
    start_idx = (batch-1) * lambda_per_batch + 1;
    end_idx = min(batch * lambda_per_batch, length(lambda_list));
    batch_lambda = lambda_list(start_idx:end_idx);
    batch_combinations = length(batch_lambda) * length(n_atoms_list) * length(sparsity_list);
    batch_time = batch_combinations * 3 / 60;  % 小时
    
    fprintf('   批次 %d: Lambda %.0e 到 %.0e (%d个组合, 约%.1f小时)\n', ...
            batch, min(batch_lambda), max(batch_lambda), batch_combinations, batch_time);
end

fprintf('\n========== 预览完成 ==========\n');
fprintf('准备好后，运行: parameter_search_clean.m\n');
