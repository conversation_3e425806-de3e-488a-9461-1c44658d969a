%% CSTR数据集SVD_DL性能分析
% 分析字典演化过程的性能指标

rng(42);
clear; close all;

fprintf('========== CSTR数据集SVD_DL性能分析 ==========\n');

%% 1. 检查和加载结果
fprintf('1. 检查SVD_DL训练结果...\n');

if exist('CSTR_SVD_DL_results.mat', 'file')
    load('CSTR_SVD_DL_results.mat');
    fprintf('   ✓ 成功加载SVD_DL训练结果\n');
else
    fprintf('   ❌ 未找到SVD_DL训练结果，开始训练...\n');
    learn_DL_CSTR;
    load('CSTR_SVD_DL_results.mat');
end

%% 2. 性能指标计算
fprintf('\n2. 计算性能指标...\n');

% 基本信息
n_modes = length(Dictionary_history_CSTR);
fprintf('   模式数量: %d\n', n_modes);
fprintf('   字典大小: %dx%d\n', size(Dictionary_history_CSTR{1}));

% 计算NMSC (归一化均方变化度)
epsilon = 1e-8;
NMSC_history = zeros(n_modes-1, 1);

for i = 2:n_modes
    D_prev = Dictionary_history_CSTR{i-1};
    D_curr = Dictionary_history_CSTR{i};
    deltaD = D_curr - D_prev;
    NMSC_history(i-1) = mean((deltaD(:).^2) ./ (D_prev(:).^2 + epsilon));
end

fprintf('   NMSC计算完成\n');

% 计算主方向子空间夹角
Subspace_angle_history = zeros(n_modes-1, 1);

for i = 2:n_modes
    U_prev = U_locked_history_CSTR{i-1};
    U_curr = U_locked_history_CSTR{i};
    
    % 确保维度匹配
    min_dim = min(size(U_prev,2), size(U_curr,2));
    if min_dim > 0
        try
            Subspace_angle_history(i-1) = subspace(U_prev(:,1:min_dim), U_curr(:,1:min_dim));
        catch
            Subspace_angle_history(i-1) = NaN;
        end
    else
        Subspace_angle_history(i-1) = NaN;
    end
end

fprintf('   子空间夹角计算完成\n');

%% 3. 字典质量评估
fprintf('\n3. 字典质量评估...\n');

% 计算字典原子的相关性
coherence_history = zeros(n_modes, 1);
condition_number_history = zeros(n_modes, 1);

for i = 1:n_modes
    D = Dictionary_history_CSTR{i};
    
    % 计算相关性矩阵
    G = D' * D;
    G = G - eye(size(G));  % 移除对角线元素
    coherence_history(i) = max(abs(G(:)));
    
    % 计算条件数
    condition_number_history(i) = cond(D);
end

fprintf('   字典质量指标计算完成\n');

%% 4. 重构性能评估
fprintf('\n4. 重构性能评估...\n');

sparsity = 2;
reconstruction_errors = zeros(n_modes, 1);
sparsity_levels = zeros(n_modes, 1);

% 对每个模式计算重构误差
train_data_modes = {train_data_mode1, train_data_mode2, train_data_mode3};

for i = 1:n_modes
    D = Dictionary_history_CSTR{i};
    Y = train_data_modes{i};
    [~, n_samples] = size(Y);
    
    % 稀疏编码
    X = zeros(size(D,2), n_samples);
    for j = 1:n_samples
        X(:,j) = omp(D, Y(:,j), sparsity);
    end
    
    % 计算重构误差
    reconstruction_errors(i) = norm(Y - D*X, 'fro')^2 / (size(Y,1) * size(Y,2));
    
    % 计算平均稀疏度
    sparsity_levels(i) = mean(sum(X ~= 0, 1));
end

fprintf('   重构性能评估完成\n');

%% 5. 保存分析结果
fprintf('\n5. 保存分析结果...\n');

results_cstr_svd_dl = struct();
results_cstr_svd_dl.method = 'SVD_DL_CSTR';
results_cstr_svd_dl.Dictionary_history = Dictionary_history_CSTR;
results_cstr_svd_dl.U_locked_history = U_locked_history_CSTR;
results_cstr_svd_dl.subspace_dims = subspace_dims_CSTR;
results_cstr_svd_dl.NMSC_history = NMSC_history;
results_cstr_svd_dl.Subspace_angle_history = Subspace_angle_history;
results_cstr_svd_dl.coherence_history = coherence_history;
results_cstr_svd_dl.condition_number_history = condition_number_history;
results_cstr_svd_dl.reconstruction_errors = reconstruction_errors;
results_cstr_svd_dl.sparsity_levels = sparsity_levels;

save('CSTR_SVD_DL_performance_analysis.mat', 'results_cstr_svd_dl');
fprintf('   分析结果已保存到: CSTR_SVD_DL_performance_analysis.mat\n');

%% 6. 详细可视化分析
fprintf('\n6. 生成详细可视化分析...\n');

% 创建综合分析图
figure('Position', [50, 50, 1400, 1000]);

% 子图1: NMSC变化趋势
subplot(3,3,1);
plot(2:n_modes, NMSC_history, 'bo-', 'LineWidth', 2, 'MarkerSize', 8);
xlabel('目标模式', 'FontSize', 12);
ylabel('NMSC', 'FontSize', 12);
title('SVD\_DL: NMSC变化趋势', 'FontSize', 14);
grid on;
set(gca, 'FontSize', 12);
for i = 1:length(NMSC_history)
    text(i+1, NMSC_history(i), sprintf('%.4f', NMSC_history(i)), ...
         'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', 'FontSize', 10);
end

% 子图2: 子空间夹角变化
subplot(3,3,2);
valid_angles = Subspace_angle_history(~isnan(Subspace_angle_history));
valid_modes = find(~isnan(Subspace_angle_history)) + 1;
if ~isempty(valid_angles)
    plot(valid_modes, valid_angles, 'rs-', 'LineWidth', 2, 'MarkerSize', 8);
    xlabel('目标模式', 'FontSize', 12);
    ylabel('子空间夹角 (弧度)', 'FontSize', 12);
    title('SVD\_DL: 子空间夹角变化', 'FontSize', 14);
    grid on;
    set(gca, 'FontSize', 12);
    for i = 1:length(valid_angles)
        text(valid_modes(i), valid_angles(i), sprintf('%.3f', valid_angles(i)), ...
             'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', 'FontSize', 10);
    end
end

% 子图3: 主空间维度演化
subplot(3,3,3);
plot(1:n_modes, subspace_dims_CSTR, 'go-', 'LineWidth', 2, 'MarkerSize', 8);
xlabel('模式', 'FontSize', 12);
ylabel('主空间维度', 'FontSize', 12);
title('SVD\_DL: 主空间维度演化', 'FontSize', 14);
grid on;
set(gca, 'FontSize', 12);
for i = 1:length(subspace_dims_CSTR)
    text(i, subspace_dims_CSTR(i), sprintf('%d', subspace_dims_CSTR(i)), ...
         'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', 'FontSize', 10);
end

% 子图4: 字典相关性
subplot(3,3,4);
plot(1:n_modes, coherence_history, 'mo-', 'LineWidth', 2, 'MarkerSize', 8);
xlabel('模式', 'FontSize', 12);
ylabel('最大相关性', 'FontSize', 12);
title('SVD\_DL: 字典相关性', 'FontSize', 14);
grid on;
set(gca, 'FontSize', 12);

% 子图5: 条件数
subplot(3,3,5);
semilogy(1:n_modes, condition_number_history, 'co-', 'LineWidth', 2, 'MarkerSize', 8);
xlabel('模式', 'FontSize', 12);
ylabel('条件数 (对数尺度)', 'FontSize', 12);
title('SVD\_DL: 字典条件数', 'FontSize', 14);
grid on;
set(gca, 'FontSize', 12);

% 子图6: 重构误差
subplot(3,3,6);
semilogy(1:n_modes, reconstruction_errors, 'ko-', 'LineWidth', 2, 'MarkerSize', 8);
xlabel('模式', 'FontSize', 12);
ylabel('重构误差 (对数尺度)', 'FontSize', 12);
title('SVD\_DL: 重构误差', 'FontSize', 14);
grid on;
set(gca, 'FontSize', 12);

% 子图7: 字典变化热图 (模式1→2)
subplot(3,3,7);
if n_modes >= 2
    diff_12 = Dictionary_history_CSTR{2} - Dictionary_history_CSTR{1};
    imagesc(diff_12);
    colorbar;
    title('字典变化 (模式1→2)', 'FontSize', 12);
    xlabel('原子索引');
    ylabel('特征维度');
end

% 子图8: 字典变化热图 (模式2→3)
subplot(3,3,8);
if n_modes >= 3
    diff_23 = Dictionary_history_CSTR{3} - Dictionary_history_CSTR{2};
    imagesc(diff_23);
    colorbar;
    title('字典变化 (模式2→3)', 'FontSize', 12);
    xlabel('原子索引');
    ylabel('特征维度');
end

% 子图9: 方法总结
subplot(3,3,9);
axis off;
summary_text = {'CSTR数据集 SVD\_DL分析:', '', ...
                sprintf('模式数: %d', n_modes), ...
                sprintf('字典大小: %dx%d', size(Dictionary_history_CSTR{1})), ...
                sprintf('平均NMSC: %.4f', mean(NMSC_history))};

if ~isempty(valid_angles)
    summary_text{end+1} = sprintf('平均子空间夹角: %.4f弧度', mean(valid_angles));
end

summary_text{end+1} = sprintf('平均重构误差: %.2e', mean(reconstruction_errors));

text(0.1, 0.8, summary_text, 'FontSize', 12, 'VerticalAlignment', 'top');

sgtitle('CSTR数据集 - SVD\_DL方法综合性能分析', 'FontSize', 16);

% 保存图像
savefig('CSTR_SVD_DL_comprehensive_analysis.fig');
fprintf('   综合分析图已保存到: CSTR_SVD_DL_comprehensive_analysis.fig\n');

%% 7. 结果总结
fprintf('\n7. 结果总结...\n');
fprintf('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
fprintf('📊 CSTR数据集 SVD_DL方法结果总览:\n');
fprintf('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');

% NMSC结果
fprintf('📈 NMSC (归一化均方变化度):\n');
for i = 1:length(NMSC_history)
    fprintf('   模式%d→%d: %.6f\n', i, i+1, NMSC_history(i));
end
fprintf('   平均值: %.6f\n', mean(NMSC_history));

% 子空间夹角结果
if ~isempty(valid_angles)
    fprintf('\n📐 主方向子空间夹角:\n');
    for i = 1:length(valid_angles)
        fprintf('   模式%d→%d: %.4f弧度 (%.2f°)\n', valid_modes(i)-1, valid_modes(i), ...
                valid_angles(i), valid_angles(i)*180/pi);
    end
    fprintf('   平均值: %.4f弧度 (%.2f°)\n', mean(valid_angles), mean(valid_angles)*180/pi);
end

% 主空间维度
fprintf('\n🔍 主空间维度演化:\n');
for i = 1:length(subspace_dims_CSTR)
    fprintf('   模式%d: %d维\n', i, subspace_dims_CSTR(i));
end

% 字典质量
fprintf('\n🎯 字典质量指标:\n');
fprintf('   平均相关性: %.4f\n', mean(coherence_history));
fprintf('   平均条件数: %.2e\n', mean(condition_number_history));
fprintf('   平均重构误差: %.2e\n', mean(reconstruction_errors));

fprintf('\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
fprintf('🎉 CSTR数据集SVD_DL性能分析完成！\n');
fprintf('📁 输出文件:\n');
fprintf('   - CSTR_SVD_DL_results.mat: 字典学习结果\n');
fprintf('   - CSTR_SVD_DL_performance_analysis.mat: 性能分析结果\n');
fprintf('   - CSTR_SVD_DL_comprehensive_analysis.fig: 综合分析图\n');

%% OMP函数定义
function w = omp(D, x, L)
% OMP  正交匹配追踪
    [~, K] = size(D);
    w = zeros(K, 1);
    r = x;
    idx = [];
    
    for i = 1:L
        proj = abs(D' * r);
        [~, k] = max(proj);
        idx = [idx, k];
        D_sel = D(:, idx);
        w_sel = D_sel \ x;
        w(idx) = w_sel;
        r = x - D_sel * w_sel;
        if norm(r) < 1e-6
            break;
        end
    end
end
